---
title: "Skill Gap Analyzer - Deployment Guide"
category: "archives"
subcategory: "completion-reports"
tags: ["skill-gap-analyzer", "deployment", "guide", "production", "setup"]
last_updated: "2025-06-28"
last_validated: "2025-06-28"
dependencies: []
used_by: []
maintainer: "deployment-team"
ai_context: "Archived deployment guide for Skill Gap Analyzer feature"
archived_date: "2025-06-28"
reason: "Deployment completed - archived for historical reference"
---

# Skill Gap Analyzer - Deployment Guide

## Overview

This guide covers the deployment process for the Skill Gap Analyzer feature, including environment setup, configuration, and production deployment procedures.

## Prerequisites

### System Requirements
- **Node.js** 18.x or higher
- **PostgreSQL** 14.x or higher
- **Redis** 6.x or higher
- **Git** for version control

### Required Services
- **Vercel** account for hosting
- **Neon** PostgreSQL database
- **Upstash** Redis instance
- **Google Cloud** account for Gemini AI
- **Resend** account for email services
- **Sentry** account for monitoring

## Environment Setup

### Development Environment

1. **Clone the repository**
```bash
git clone https://github.com/your-org/faafo-career-platform.git
cd faafo-career-platform
```

2. **Install dependencies**
```bash
npm install
```

3. **Setup environment variables**
```bash
cp .env.example .env.local
```

4. **Configure environment variables**
```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/faafo_dev"

# Authentication
NEXTAUTH_SECRET="your-development-secret"
NEXTAUTH_URL="http://localhost:3000"

# AI Services
GEMINI_API_KEY="your-gemini-api-key"

# Email
RESEND_API_KEY="your-resend-api-key"

# Caching
REDIS_URL="redis://localhost:6379"

# Monitoring
SENTRY_DSN="your-sentry-dsn"
```

5. **Setup database**
```bash
npx prisma migrate dev
npx prisma db seed
```

6. **Start development server**
```bash
npm run dev
```

### Staging Environment

1. **Create staging branch**
```bash
git checkout -b staging
git push origin staging
```

2. **Deploy to Vercel staging**
```bash
vercel --env staging
```

3. **Configure staging environment variables**
```env
DATABASE_URL="postgresql://staging-db-url"
NEXTAUTH_URL="https://staging.faafo.com"
# ... other staging-specific variables
```

4. **Run staging tests**
```bash
npm run test:staging
```

## Production Deployment

### Pre-deployment Checklist

- [ ] All tests passing (unit, integration, E2E)
- [ ] Code review completed and approved
- [ ] Database migrations tested
- [ ] Environment variables configured
- [ ] Performance benchmarks met
- [ ] Security scan completed
- [ ] Backup procedures verified

### Deployment Process

1. **Merge to main branch**
```bash
git checkout main
git merge staging
git push origin main
```

2. **Tag the release**
```bash
git tag -a v1.0.0 -m "Skill Gap Analyzer v1.0.0"
git push origin v1.0.0
```

3. **Deploy to production**
```bash
vercel --prod
```

4. **Run database migrations**
```bash
npx prisma migrate deploy
```

5. **Verify deployment**
```bash
npm run test:production
```

### Production Environment Variables

```env
# Database
DATABASE_URL="postgresql://prod-neon-url"

# Authentication
NEXTAUTH_SECRET="secure-production-secret"
NEXTAUTH_URL="https://faafo.com"

# AI Services
GEMINI_API_KEY="production-gemini-key"

# Email
RESEND_API_KEY="production-resend-key"

# Caching
REDIS_URL="redis://production-upstash-url"

# Monitoring
SENTRY_DSN="production-sentry-dsn"
SENTRY_ENVIRONMENT="production"

# Performance
NODE_ENV="production"
```

## Database Configuration

### PostgreSQL Setup (Neon)

1. **Create production database**
```sql
CREATE DATABASE faafo_production;
CREATE USER faafo_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE faafo_production TO faafo_user;
```

2. **Configure connection pooling**
```env
DATABASE_URL="******************************************/faafo_production?pgbouncer=true&connection_limit=10"
```

3. **Setup automated backups**
```bash
# Neon automatically handles backups
# Configure retention policy in Neon dashboard
```

### Redis Setup (Upstash)

1. **Create Redis instance**
```bash
# Create through Upstash dashboard
# Configure memory limit and eviction policy
```

2. **Configure connection**
```env
REDIS_URL="rediss://username:password@host:port"
```

## Monitoring Setup

### Sentry Configuration

1. **Install Sentry**
```bash
npm install @sentry/nextjs
```

2. **Configure Sentry**
```javascript
// sentry.client.config.ts
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.SENTRY_ENVIRONMENT,
  tracesSampleRate: 1.0,
  beforeSend(event) {
    // Filter sensitive data
    return event;
  }
});
```

3. **Setup alerts**
```yaml
# Sentry alert rules
- condition: error_count > 10
  time_window: 5m
  action: email_team

- condition: performance_score < 0.8
  time_window: 10m
  action: slack_notification
```

### Application Monitoring

```typescript
// lib/monitoring.ts
export const trackSkillAssessment = (userId: string, skillName: string) => {
  analytics.track('Skill Assessment Created', {
    userId,
    skillName,
    timestamp: new Date().toISOString()
  });
};

export const trackAIAnalysis = (userId: string, careerPath: string, readiness: number) => {
  analytics.track('AI Analysis Generated', {
    userId,
    careerPath,
    readiness,
    timestamp: new Date().toISOString()
  });
};
```

## Performance Optimization

### Caching Strategy

1. **API Response Caching**
```typescript
// lib/cache.ts
export const cacheAnalysis = async (key: string, data: any, ttl = 3600) => {
  await redis.setex(key, ttl, JSON.stringify(data));
};

export const getCachedAnalysis = async (key: string) => {
  const cached = await redis.get(key);
  return cached ? JSON.parse(cached) : null;
};
```

2. **Database Query Optimization**
```sql
-- Add indexes for common queries
CREATE INDEX idx_skill_assessments_user_id ON skill_assessments(user_id);
CREATE INDEX idx_skill_gap_analyses_user_id ON skill_gap_analyses(user_id);
CREATE INDEX idx_learning_recommendations_user_id ON learning_recommendations(user_id);
```

### CDN Configuration

```javascript
// next.config.js
module.exports = {
  images: {
    domains: ['cdn.faafo.com'],
    loader: 'custom',
    loaderFile: './lib/imageLoader.js'
  },
  experimental: {
    optimizeCss: true,
    optimizeImages: true
  }
};
```

## Security Configuration

### Environment Security

1. **Secure environment variables**
```bash
# Use Vercel's encrypted environment variables
vercel env add GEMINI_API_KEY production
vercel env add DATABASE_URL production
```

2. **API Rate Limiting**
```typescript
// middleware.ts
import { Ratelimit } from '@upstash/ratelimit';

const ratelimit = new Ratelimit({
  redis: Redis.fromEnv(),
  limiter: Ratelimit.slidingWindow(10, '1 h'),
});

export async function middleware(request: NextRequest) {
  const ip = request.ip ?? '127.0.0.1';
  const { success } = await ratelimit.limit(ip);
  
  if (!success) {
    return new Response('Rate limit exceeded', { status: 429 });
  }
}
```

### Data Protection

1. **Input validation**
```typescript
// lib/validation.ts
import { z } from 'zod';

export const skillAssessmentSchema = z.object({
  skillName: z.string().min(1).max(100),
  currentLevel: z.number().min(1).max(10),
  confidenceLevel: z.number().min(1).max(10),
  yearsExperience: z.number().min(0).max(50)
});
```

2. **Data encryption**
```typescript
// lib/encryption.ts
import crypto from 'crypto';

export const encryptSensitiveData = (data: string): string => {
  const cipher = crypto.createCipher('aes-256-cbc', process.env.ENCRYPTION_KEY);
  let encrypted = cipher.update(data, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
};
```

## Backup and Recovery

### Database Backups

1. **Automated backups** (Neon handles this automatically)
2. **Manual backup procedure**
```bash
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql
```

3. **Backup verification**
```bash
# Test restore on staging environment
psql $STAGING_DATABASE_URL < backup_file.sql
```

### Application State Backup

```typescript
// scripts/backup-user-data.ts
export const backupUserAssessments = async (userId: string) => {
  const assessments = await prisma.skillAssessment.findMany({
    where: { userId },
    include: {
      gapAnalyses: true,
      recommendations: true
    }
  });
  
  const backup = {
    userId,
    timestamp: new Date().toISOString(),
    data: assessments
  };
  
  await saveToS3(`backups/users/${userId}/${Date.now()}.json`, backup);
};
```

## Rollback Procedures

### Application Rollback

1. **Identify the issue**
2. **Revert to previous deployment**
```bash
vercel rollback
```

3. **Verify rollback success**
```bash
npm run test:production
```

### Database Rollback

1. **Stop application traffic**
2. **Restore from backup**
```bash
psql $DATABASE_URL < backup_file.sql
```

3. **Verify data integrity**
4. **Resume application traffic**

## Health Checks

### Application Health

```typescript
// pages/api/health.ts
export default async function handler(req: NextRequest, res: NextResponse) {
  try {
    // Check database connection
    await prisma.$queryRaw`SELECT 1`;
    
    // Check Redis connection
    await redis.ping();
    
    // Check AI service
    await testGeminiConnection();
    
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'up',
        redis: 'up',
        ai: 'up'
      }
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message
    });
  }
}
```

### Monitoring Alerts

```yaml
# monitoring/alerts.yml
alerts:
  - name: High Error Rate
    condition: error_rate > 5%
    duration: 5m
    action: page_oncall
    
  - name: Slow Response Time
    condition: p95_response_time > 2s
    duration: 10m
    action: slack_notification
    
  - name: Database Connection Issues
    condition: db_connection_errors > 0
    duration: 1m
    action: page_oncall
```

## Troubleshooting

### Common Issues

1. **Database connection timeouts**
   - Check connection pool settings
   - Verify network connectivity
   - Review query performance

2. **AI service rate limits**
   - Implement exponential backoff
   - Add request queuing
   - Monitor usage quotas

3. **Memory leaks**
   - Monitor heap usage
   - Review event listener cleanup
   - Check for circular references

### Debug Tools

```bash
# Performance profiling
npm run build:analyze

# Memory usage monitoring
node --inspect pages/api/assessment.js

# Database query analysis
EXPLAIN ANALYZE SELECT * FROM skill_assessments WHERE user_id = $1;
```

## Maintenance

### Regular Tasks

1. **Weekly**
   - Review error logs
   - Check performance metrics
   - Update dependencies

2. **Monthly**
   - Database maintenance
   - Security updates
   - Backup verification

3. **Quarterly**
   - Performance optimization
   - Security audit
   - Disaster recovery testing

### Update Procedures

1. **Dependency updates**
```bash
npm audit
npm update
npm run test
```

2. **Security patches**
```bash
npm audit fix
npm run security:scan
```

3. **Database schema updates**
```bash
npx prisma migrate deploy
npx prisma generate
```
