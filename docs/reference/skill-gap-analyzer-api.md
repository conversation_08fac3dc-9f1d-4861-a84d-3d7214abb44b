---
title: "Skill Gap Analyzer API Documentation"
category: "reference"
subcategory: "api"
tags: ["skill-gap-analyzer", "api", "documentation", "endpoints", "ai-powered"]
last_updated: "2025-06-28"
last_validated: "2025-06-28"
dependencies: ["API_RESPONSE_FORMATS.md", "ai-service-documentation.md"]
used_by: ["skill-gap-analyzer.md"]
maintainer: "api-team"
ai_context: "API documentation for Skill Gap Analyzer endpoints"
generated_date: "2025-06-28"
generator: "api-team"
---

# Skill Gap Analyzer API Documentation

## Overview

The Skill Gap Analyzer API provides comprehensive endpoints for skill assessment, gap analysis, and personalized learning recommendations. This API enables users to evaluate their current skills, identify gaps relative to career goals, and receive AI-powered recommendations for skill development.

## Base URL

```
Production: https://faafo.com/api
Development: http://localhost:3000/api
```

## Authentication

All API endpoints require authentication via JWT token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

## Core Endpoints

### Assessment Management

#### Submit Skill Assessment
```http
POST /api/assessment
Content-Type: application/json
Authorization: Bearer <jwt_token>

{
  "skillName": "JavaScript",
  "currentLevel": 7,
  "confidenceLevel": 8,
  "yearsExperience": 3,
  "notes": "Strong in ES6+ features, working with React daily"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "clx123abc",
    "skillName": "JavaScript",
    "currentLevel": 7,
    "confidenceLevel": 8,
    "yearsExperience": 3,
    "notes": "Strong in ES6+ features, working with React daily",
    "createdAt": "2024-06-26T16:19:40.000Z"
  }
}
```

#### Retrieve Assessment
```http
GET /api/assessment/{id}
Authorization: Bearer <jwt_token>
```

#### Update Assessment
```http
PUT /api/assessment/{id}
Content-Type: application/json
Authorization: Bearer <jwt_token>

{
  "currentLevel": 8,
  "confidenceLevel": 9,
  "notes": "Updated after completing advanced course"
}
```

#### Delete Assessment
```http
DELETE /api/assessment/{id}
Authorization: Bearer <jwt_token>
```

### AI Analysis

#### Generate Skills Analysis
```http
POST /api/ai/skills-analysis
Content-Type: application/json
Authorization: Bearer <jwt_token>

{
  "careerPath": "Full Stack Developer",
  "assessments": [
    {
      "skillName": "JavaScript",
      "currentLevel": 7,
      "confidenceLevel": 8,
      "yearsExperience": 3
    },
    {
      "skillName": "Python",
      "currentLevel": 4,
      "confidenceLevel": 5,
      "yearsExperience": 1
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "analysisId": "clx456def",
    "overallReadiness": 72.5,
    "criticalGaps": [
      {
        "skillName": "System Design",
        "currentLevel": 0,
        "targetLevel": 7,
        "priority": "critical",
        "impact": "Essential for senior roles"
      }
    ],
    "recommendations": [
      {
        "skillName": "System Design",
        "resourceType": "course",
        "resourceTitle": "System Design Interview Course",
        "estimatedHours": 40,
        "priority": "critical"
      }
    ],
    "marketInsights": {
      "averageSalary": "$95,000",
      "demandLevel": "high",
      "growthProjection": "15% over next 2 years"
    }
  }
}
```

#### Comprehensive Analysis
```http
POST /api/ai/skills-analysis/comprehensive
Content-Type: application/json
Authorization: Bearer <jwt_token>

{
  "careerPath": "Full Stack Developer",
  "targetLevel": "Senior",
  "timeframe": "12 months",
  "assessments": [...],
  "preferences": {
    "learningStyle": "hands-on",
    "timeCommitment": "10 hours/week"
  }
}
```

### Recommendations & Progress

#### Get Learning Recommendations
```http
GET /api/recommendations/{userId}
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "recommendations": [
      {
        "id": "clx789ghi",
        "skillName": "System Design",
        "resourceType": "course",
        "resourceTitle": "Designing Data-Intensive Applications",
        "resourceUrl": "https://example.com/course",
        "estimatedHours": 40,
        "priority": "critical",
        "completed": false
      }
    ],
    "totalRecommendations": 15,
    "completedCount": 3
  }
}
```

#### Update Learning Progress
```http
POST /api/progress/update
Content-Type: application/json
Authorization: Bearer <jwt_token>

{
  "recommendationId": "clx789ghi",
  "completed": true,
  "hoursSpent": 35,
  "rating": 4,
  "notes": "Excellent course, very practical examples"
}
```

#### Get Market Data
```http
GET /api/market-data/{skillId}
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "skillName": "JavaScript",
    "averageSalary": "$85,000",
    "salaryRange": {
      "min": "$60,000",
      "max": "$120,000"
    },
    "demandLevel": "very-high",
    "jobOpenings": 15420,
    "growthProjection": "22% over next 5 years",
    "topCompanies": ["Google", "Microsoft", "Amazon"],
    "relatedSkills": ["React", "Node.js", "TypeScript"]
  }
}
```

## Error Handling

### Standard Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid skill level. Must be between 1 and 10.",
    "details": {
      "field": "currentLevel",
      "value": 15,
      "constraint": "min: 1, max: 10"
    }
  }
}
```

### Common Error Codes
- `VALIDATION_ERROR` - Invalid input data
- `AUTHENTICATION_ERROR` - Invalid or missing JWT token
- `AUTHORIZATION_ERROR` - Insufficient permissions
- `NOT_FOUND` - Resource not found
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `AI_SERVICE_ERROR` - AI analysis service unavailable
- `DATABASE_ERROR` - Database operation failed

## Rate Limiting

- **Assessment endpoints**: 100 requests per hour per user
- **AI analysis endpoints**: 10 requests per hour per user
- **General endpoints**: 1000 requests per hour per user

Rate limit headers are included in all responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Data Models

### SkillAssessment
```typescript
interface SkillAssessment {
  id: string;
  userId: string;
  skillName: string;
  currentLevel: number; // 1-10
  confidenceLevel: number; // 1-10
  yearsExperience: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}
```

### SkillGapAnalysis
```typescript
interface SkillGapAnalysis {
  id: string;
  userId: string;
  careerPath: string;
  overallReadiness: number; // 0-100 percentage
  criticalGaps: SkillGap[];
  recommendations: LearningRecommendation[];
  marketInsights: MarketInsights;
  generatedAt: string;
}
```

### LearningRecommendation
```typescript
interface LearningRecommendation {
  id: string;
  userId: string;
  skillName: string;
  resourceType: 'course' | 'book' | 'project' | 'tutorial' | 'certification';
  resourceTitle: string;
  resourceUrl?: string;
  estimatedHours?: number;
  priority: 'critical' | 'high' | 'medium' | 'low';
  completed: boolean;
  createdAt: string;
}
```

## Testing

### Test Environment
```
Base URL: https://test.faafo.com/api
Test User: <EMAIL>
Test Password: TestPassword123!
```

### Sample Test Data
```json
{
  "testAssessments": [
    {
      "skillName": "JavaScript",
      "currentLevel": 7,
      "confidenceLevel": 8,
      "yearsExperience": 3
    },
    {
      "skillName": "Python",
      "currentLevel": 5,
      "confidenceLevel": 6,
      "yearsExperience": 1
    }
  ],
  "testCareerPaths": [
    "Full Stack Developer",
    "Data Scientist",
    "DevOps Engineer",
    "Product Manager"
  ]
}
```
