---
title: "Skill Gap Analyzer - Troubleshooting Guide"
category: "reference"
subcategory: "troubleshooting"
tags: ["skill-gap-analyzer", "troubleshooting", "guide", "edge-case-handler", "debugging"]
last_updated: "2025-06-28"
last_validated: "2025-06-28"
dependencies: ["skill-gap-analyzer.md", "technical-implementation.md"]
used_by: []
maintainer: "support-team"
ai_context: "Troubleshooting guide for Skill Gap Analyzer implementation issues"
generated_date: "2025-06-28"
generator: "support-team"
---

# Skill Gap Analyzer - Troubleshooting Guide

## 🎯 Overview

This guide provides comprehensive troubleshooting steps for common issues with the Skill Gap Analyzer implementation, including EdgeCaseHandler integration problems.

## 🚨 Common Issues & Solutions

### 1. API Endpoint Issues

#### Issue: Skills Analysis Returns 500 Error
**Symptoms**: 
- API calls to `/api/ai/skills-analysis` fail
- Error message: "Internal server error"
- No fallback data provided

**Diagnosis Steps**:
```bash
# Check API endpoint health
curl -X GET http://localhost:3000/api/health

# Check specific endpoint
curl -X POST http://localhost:3000/api/ai/skills-analysis \
  -H "Content-Type: application/json" \
  -d '{"currentSkills":["React"],"targetCareerPath":"Frontend Developer","experienceLevel":"intermediate"}'
```

**Solutions**:
1. **Check EdgeCaseHandler Integration**:
   ```typescript
   // Verify EdgeCaseHandler is properly imported
   import { edgeCaseHandlerService } from '@/lib/services/edgeCaseHandlerService';
   
   // Check if service is initialized
   console.log('EdgeCaseHandler available:', !!edgeCaseHandlerService);
   ```

2. **Verify Environment Variables**:
   ```bash
   # Check required environment variables
   echo $GEMINI_API_KEY
   echo $DATABASE_URL
   echo $REDIS_URL
   ```

3. **Check Database Connection**:
   ```bash
   # Test database connection
   npx prisma db push --preview-feature
   npx prisma generate
   ```

#### Issue: Authentication Required Error
**Symptoms**:
- 401 Unauthorized responses
- "Authentication required" error message

**Solutions**:
1. **Verify Session**:
   ```typescript
   // Check if user is authenticated
   const session = await getServerSession(authOptions);
   if (!session?.user?.id) {
     return NextResponse.json(
       { success: false, error: 'Authentication required' },
       { status: 401 }
     );
   }
   ```

2. **Check Auth Configuration**:
   ```bash
   # Verify auth environment variables
   echo $NEXTAUTH_SECRET
   echo $NEXTAUTH_URL
   ```

### 2. EdgeCaseHandler Issues

#### Issue: EdgeCaseHandler Not Providing Fallback Data
**Symptoms**:
- API errors don't include `fallbackData`
- No `suggestedAlternatives` in error responses
- Frontend shows generic error messages

**Diagnosis**:
```typescript
// Check EdgeCaseHandler response structure
const edgeCaseResult = await edgeCaseHandlerService.handleAISkillsAnalysis(params);
console.log('EdgeCase Result:', {
  success: edgeCaseResult.success,
  hasData: !!edgeCaseResult.data,
  hasFallback: !!edgeCaseResult.fallbackData,
  errorType: edgeCaseResult.errorType
});
```

**Solutions**:
1. **Verify EdgeCaseHandler Service**:
   ```bash
   # Check if EdgeCaseHandler tests pass
   npm test -- --testPathPattern=edgeCaseHandler
   ```

2. **Update API Error Response**:
   ```typescript
   // Ensure proper error response format
   return NextResponse.json({
     success: false,
     error: analysisResult.error,
     errorType: edgeCaseResult.errorType,
     fallbackData: edgeCaseResult.fallbackData, // ← Must include
     suggestedAlternatives: edgeCaseResult.suggestedAlternatives,
     retryable: edgeCaseResult.retryable
   }, { status: 500 });
   ```

#### Issue: Circuit Breaker Always Open
**Symptoms**:
- All requests fail with "CIRCUIT_BREAKER_OPEN"
- EdgeCaseHandler not attempting AI service calls

**Solutions**:
1. **Reset Circuit Breaker**:
   ```typescript
   // Reset circuit breaker manually
   await edgeCaseHandlerService.resetCircuitBreaker();
   ```

2. **Check Failure Threshold**:
   ```typescript
   // Verify circuit breaker configuration
   const config = edgeCaseHandlerService.getCircuitBreakerConfig();
   console.log('Failure threshold:', config.failureThreshold);
   console.log('Reset timeout:', config.resetTimeout);
   ```

### 3. Frontend Component Issues

#### Issue: SkillGapAnalysis Component Not Displaying EdgeCase Data
**Symptoms**:
- Error information not shown to users
- No fallback data warnings displayed
- Missing suggested alternatives

**Solutions**:
1. **Check Component Props**:
   ```tsx
   // Ensure edgeCaseHandlerData prop is passed
   <SkillGapAnalysis
     analysisId={result.analysisId}
     skillGaps={result.skillGaps}
     learningPlan={result.learningPlan}
     careerReadiness={result.careerReadiness}
     edgeCaseHandlerData={result.metadata?.edgeCaseHandlerData} // ← Required
   />
   ```

2. **Verify Error Handling in Component**:
   ```tsx
   // Check if component handles fallback data
   {edgeCaseHandlerData?.fallbackDataUsed && (
     <Alert variant="warning">
       <AlertTriangle className="h-4 w-4" />
       <AlertTitle>Limited Results</AlertTitle>
       <AlertDescription>
         Some services were unavailable. Results may be incomplete.
       </AlertDescription>
     </Alert>
   )}
   ```

#### Issue: Form Submission Fails
**Symptoms**:
- Skill assessment form doesn't submit
- No error messages displayed
- Form state not preserved

**Solutions**:
1. **Check Form Validation**:
   ```typescript
   // Verify form validation schema
   const validation = skillsAnalysisSchema.safeParse(formData);
   if (!validation.success) {
     console.log('Validation errors:', validation.error.errors);
   }
   ```

2. **Check Network Requests**:
   ```javascript
   // Monitor network requests in browser dev tools
   // Look for failed requests or CORS issues
   ```

### 4. Performance Issues

#### Issue: Slow Analysis Processing
**Symptoms**:
- Analysis takes >30 seconds
- Timeout errors
- Poor user experience

**Solutions**:
1. **Check Performance Monitoring**:
   ```typescript
   // Monitor analysis performance
   const metrics = await skillGapPerformanceMonitor.getMetrics();
   console.log('Average response time:', metrics.averageResponseTime);
   ```

2. **Optimize Caching**:
   ```typescript
   // Check cache hit rates
   const cacheStats = await cacheService.getStats();
   console.log('Cache hit rate:', cacheStats.hitRate);
   ```

3. **Enable Result Caching**:
   ```typescript
   // Verify caching is enabled
   const cacheKey = aiCacheKeys.skillsAnalysis(userId, analysisParams);
   const cached = await cacheService.getJSON(cacheKey);
   ```

#### Issue: High Memory Usage
**Symptoms**:
- Application becomes slow
- Memory leaks detected
- Server crashes

**Solutions**:
1. **Clear EdgeCaseHandler Cache**:
   ```typescript
   // Clear cache periodically
   await edgeCaseHandlerService.clearCache();
   ```

2. **Monitor Memory Usage**:
   ```bash
   # Check Node.js memory usage
   node --inspect app.js
   # Use Chrome DevTools to monitor memory
   ```

### 5. Database Issues

#### Issue: Skill Assessment Data Not Saving
**Symptoms**:
- Form submissions succeed but data not persisted
- Database errors in logs
- Inconsistent data retrieval

**Solutions**:
1. **Check Database Schema**:
   ```bash
   # Verify database schema is up to date
   npx prisma db push
   npx prisma generate
   ```

2. **Check Database Connection**:
   ```typescript
   // Test database connection
   try {
     await prisma.$connect();
     console.log('Database connected successfully');
   } catch (error) {
     console.error('Database connection failed:', error);
   }
   ```

3. **Verify Data Migration**:
   ```bash
   # Check migration status
   npx prisma migrate status
   
   # Apply pending migrations
   npx prisma migrate deploy
   ```

### 6. AI Service Issues

#### Issue: Gemini API Failures
**Symptoms**:
- "AI service unavailable" errors
- No analysis results generated
- Timeout errors

**Solutions**:
1. **Check API Key**:
   ```bash
   # Verify Gemini API key is set
   echo $GEMINI_API_KEY
   ```

2. **Test API Connection**:
   ```typescript
   // Test Gemini service directly
   const testResult = await geminiService.testConnection();
   console.log('Gemini service status:', testResult);
   ```

3. **Check Rate Limits**:
   ```typescript
   // Monitor API usage
   const usage = await geminiService.getUsageStats();
   console.log('API calls today:', usage.callsToday);
   console.log('Rate limit remaining:', usage.remaining);
   ```

## 🔧 Diagnostic Tools

### Health Check Endpoints

```bash
# Check overall system health
curl -X GET http://localhost:3000/api/health

# Check specific service health
curl -X GET http://localhost:3000/api/health/edge-case-handler
curl -X GET http://localhost:3000/api/health/database
curl -X GET http://localhost:3000/api/health/ai-services
```

### Debug Mode

Enable debug mode for detailed logging:

```bash
# Set debug environment variable
export DEBUG=skill-gap-analyzer:*

# Run application with debug logging
npm run dev
```

### Performance Monitoring

```typescript
// Enable performance monitoring
import { PerformanceMonitor } from '@/lib/performance-monitor';

const monitor = new PerformanceMonitor();
monitor.startMonitoring('skill-gap-analyzer');
```

## 📊 Monitoring & Alerts

### Key Metrics to Monitor

1. **API Response Times**
   - Target: <2 seconds for analysis
   - Alert: >5 seconds

2. **Error Rates**
   - Target: <5% error rate
   - Alert: >10% error rate

3. **EdgeCaseHandler Usage**
   - Monitor fallback data usage
   - Track circuit breaker state

4. **Cache Hit Rates**
   - Target: >80% cache hit rate
   - Alert: <60% cache hit rate

### Setting Up Alerts

```typescript
// Example alert configuration
const alertConfig = {
  responseTimeThreshold: 5000,
  errorRateThreshold: 0.1,
  cacheHitRateThreshold: 0.6
};

// Monitor and alert
monitor.setAlerts(alertConfig);
```

## 🆘 Emergency Procedures

### Disable EdgeCaseHandler

If EdgeCaseHandler is causing issues:

```typescript
// Disable via feature flag
await featureFlags.disable('edge-case-handler');

// Or disable in environment
export DISABLE_EDGE_CASE_HANDLER=true
```

### Rollback to Previous Version

```bash
# Rollback deployment
git revert HEAD
npm run build
npm run deploy

# Or use feature flag to disable new features
curl -X POST /api/admin/feature-flags \
  -d '{"flag":"skill-gap-analyzer","enabled":false}'
```

### Emergency Contact

For critical issues:
1. Check system status dashboard
2. Review error logs in Sentry
3. Contact development team
4. Escalate to infrastructure team if needed

## 📚 Additional Resources

- [EdgeCaseHandler Integration Guide](../integration/EDGE_CASE_HANDLER_INTEGRATION.md)
- [API Response Formats](../api/API_RESPONSE_FORMATS.md)
- [Performance Monitoring Guide](../monitoring/PERFORMANCE_MONITORING.md)
- [Development Setup Guide](../development/SETUP.md)
