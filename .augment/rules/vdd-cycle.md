---
type: "agent_requested"
description: "The core Verification-Driven Development (VDD) process for writing code: generate failing tests first, then write code to make them pass."
---
# Rule: Verification-Driven Development (VDD) Cycle

This is the mandatory, non-negotiable process for all new feature implementation and bug fixing. You must follow these steps sequentially.

**1. Define the Verification (Test Plan):**
   - Before requesting feature code, first generate a comprehensive test plan in plain English describing the exact behaviors to be tested. State the plan clearly.

**2. Generate the Failing Test (The "Red" Light):**
   - **Command:** Use the `/test` command to write the automated test code based on the plan from Step 1. If a test file doesn't exist, use `/edit` to create it first.
   - **Verification:** Run the new test and confirm that it fails as expected. This failing test is the objective "definition of done."

**3. Generate the Feature Code (The "Green" Light):**
   - **Command:** Use the `/edit` or `/debug` command.
   - **Context:** Your prompt MUST include the full requirements AND the failing test code from Step 2.
   - **Goal:** Command the AI to write the minimum viable code required to make the test pass.

**4. Refactor and Finalize on Green:**
   - **Action:** Only after all tests for the feature are passing can you command the AI to refactor the code for quality, clarity, and performance.
   - **Final Verification:** After refactoring, you MUST run the entire test suite (`npm test`) one final time to guarantee no regressions were introduced.