---
type: "agent_requested"
description: "Comprehensive Feature Testing Protocol"
---
# Rule: Comprehensive Feature Testing Protocol

This rule defines the mandatory, three-phase process for achieving >95% test coverage for any given feature.

The goal is to produce a comprehensive suite of unit, integration, and end-to-end tests that validate all aspects of the feature's functionality, from its individual components to the complete user journey.

You must adhere to the following rules and protocols throughout this entire task:
- `@rule-vdd-cycle.md`
- `@rule-security-best-practices.md`
- `@rule-performance-best-practices.md`

Your final deliverable must be a pull request containing all new test files and a report confirming that all tests pass, as defined in `@rule-delivery-checklist.md`.

---

### The Three-Phase Testing Plan

**Phase 1: Component Unit Tests**

For each of the primary frontend components listed in the prompt, use the `/test` command to generate unit tests. These tests must verify all critical UI states (Loading, Empty, Error, Success) and user interactions (button clicks, form inputs, etc.).

**Phase 2: API Endpoint Integration Tests**

For each of the API endpoints listed in the prompt, use the `/test` command to generate integration tests. These tests MUST interact with a test database and not use mocks for our own services. They must validate:
1.  Successful data creation, retrieval, update, and deletion (CRUD).
2.  Correct error handling for invalid input or missing parameters.
3.  Proper authentication and authorization checks to ensure data privacy.

**Phase 3: End-to-End (E2E) User Flow Tests**

Use the `/test` command to create E2E tests that simulate a complete user journey from start to finish. The prompt will specify the key user flows to test, which must include:
1.  **The "Happy Path" Flow:** The ideal, successful user journey.
2.  **Edge Case / Failure Flows:** Common but non-ideal user paths, such as abandoning a task midway or encountering a third-party service error.