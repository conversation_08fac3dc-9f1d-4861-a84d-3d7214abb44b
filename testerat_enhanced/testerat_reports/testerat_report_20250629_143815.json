{"metadata": {"report_id": "20250629_143815", "generated_at": "2025-06-29T14:38:15.059600", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.ANGULAR", "app_name": "httpbin.org", "version": null, "features": ["loading-states", "accessibility-labels"], "confidence": 1.0, "detection_methods": ["DOM analysis"], "detected_patterns": ["ng-container", "ng-"]}, "app_info": {"name": "httpbin.org", "url": "https://httpbin.org"}}, "summary": {"suite_name": "Enhanced Testerat - https://httpbin.org", "total_tests": 3, "passed": 3, "failed": 0, "critical_issues": 0, "success_rate": 100.0, "total_execution_time": 3.756649971008301, "start_time": "2025-06-29T14:38:15.059745", "end_time": "2025-06-29T14:38:18.816383"}, "test_results": [{"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-29T14:38:18.812616", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_accessibility", "status": "PASSED", "details": "Basic accessibility checks passed", "severity": "LOW", "recommendations": ["Accessibility basics look good - consider comprehensive accessibility audit"], "execution_time": 0.0, "screenshot_path": null, "category": "accessibility", "timestamp": "2025-06-29T14:38:18.815072", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_performance", "status": "PASSED", "details": "Performance metrics within acceptable ranges", "severity": "LOW", "recommendations": ["Performance looks good - continue monitoring"], "execution_time": 0.0, "screenshot_path": null, "category": "performance", "timestamp": "2025-06-29T14:38:18.816375", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [], "recommendations": ["Accessibility basics look good - consider comprehensive accessibility audit", "Security basics look good - consider comprehensive security audit", "Performance looks good - continue monitoring"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"security": {"total": 1, "passed": 1, "failed": 0}, "accessibility": {"total": 1, "passed": 1, "failed": 0}, "performance": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"LOW": 3}, "avg_execution_time": 0.0, "slowest_test": "basic_security", "fastest_test": "basic_security"}}