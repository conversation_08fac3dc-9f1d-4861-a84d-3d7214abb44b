{"metadata": {"report_id": "20250629_143820", "generated_at": "2025-06-29T14:38:20.835348", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.REACT", "app_name": "httpbin.org", "version": null, "features": ["interactive-elements", "accessibility-labels"], "confidence": 0.9, "detection_methods": ["DOM analysis"], "detected_patterns": ["[data-reactroot]"]}, "app_info": {"name": "httpbin.org", "url": "https://httpbin.org"}}, "summary": {"suite_name": "Enhanced Testerat - https://httpbin.org", "total_tests": 4, "passed": 3, "failed": 0, "critical_issues": 0, "success_rate": 75.0, "total_execution_time": 4.156552791595459, "start_time": "2025-06-29T14:38:20.835459", "end_time": "2025-06-29T14:38:24.992006"}, "test_results": [{"test_name": "authentication_system_detection", "status": "SKIPPED", "details": "No authentication system detected on this site", "severity": "LOW", "recommendations": ["This appears to be a public site without user authentication", "If authentication should be present, verify login forms and links are properly implemented"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-29T14:38:24.884522", "framework_detected": "react", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-29T14:38:24.987113", "framework_detected": "react", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_accessibility", "status": "PASSED", "details": "Basic accessibility checks passed", "severity": "LOW", "recommendations": ["Accessibility basics look good - consider comprehensive accessibility audit"], "execution_time": 0.0, "screenshot_path": null, "category": "accessibility", "timestamp": "2025-06-29T14:38:24.990628", "framework_detected": "react", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_performance", "status": "PASSED", "details": "Performance metrics within acceptable ranges", "severity": "LOW", "recommendations": ["Performance looks good - continue monitoring"], "execution_time": 0.0, "screenshot_path": null, "category": "performance", "timestamp": "2025-06-29T14:38:24.992000", "framework_detected": "react", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [], "recommendations": ["This appears to be a public site without user authentication", "Security basics look good - consider comprehensive security audit", "Accessibility basics look good - consider comprehensive accessibility audit", "Performance looks good - continue monitoring", "If authentication should be present, verify login forms and links are properly implemented"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"general": {"total": 1, "passed": 1, "failed": 0}, "security": {"total": 1, "passed": 1, "failed": 0}, "accessibility": {"total": 1, "passed": 1, "failed": 0}, "performance": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"LOW": 4}, "avg_execution_time": 0.0, "slowest_test": "authentication_system_detection", "fastest_test": "authentication_system_detection"}}