/**
 * Performance Monitoring Enhancement Tests
 * 
 * Comprehensive tests for the enhanced performance monitoring system
 * including dashboard, alerting, and API integration.
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { NextRequest } from 'next/server';
import { GET, POST } from '@/app/api/admin/performance-monitoring/route';
import { enhancedAlertingService } from '@/lib/enhanced-alerting-service';
import { performanceMonitor } from '@/lib/performance-monitor';

// Mock dependencies
jest.mock('@/lib/auth-utils', () => ({
  requireAdmin: jest.fn().mockResolvedValue({
    success: true,
    user: { email: '<EMAIL>', role: 'admin' }
  })
}));

jest.mock('@/lib/performance-monitor', () => ({
  performanceMonitor: {
    getInsights: jest.fn(),
    recordMetric: jest.fn(),
    getAlerts: jest.fn()
  }
}));

jest.mock('@/lib/advanced-cache-manager', () => ({
  advancedCacheManager: {
    getStats: jest.fn(),
    clearAll: jest.fn()
  }
}));

jest.mock('@/lib/request-optimizer', () => ({
  requestOptimizer: {
    getStats: jest.fn(),
    getActiveRequestCount: jest.fn(),
    getQueuedRequestCount: jest.fn(),
    optimizeQueue: jest.fn()
  }
}));

jest.mock('@/lib/ai-service-monitor', () => ({
  aiServiceMonitor: {
    getMetrics: jest.fn()
  }
}));

describe('Performance Monitoring Enhancement', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Clear alerting service state
    enhancedAlertingService.stop();

    // Setup default mock responses
    (performanceMonitor.getInsights as jest.Mock).mockResolvedValue({
      averageResponseTime: 150,
      overallScore: 85,
      trends: {
        responseTime: 'stable',
        throughput: 'improving',
        errorRate: 'improving'
      },
      bottlenecks: [],
      recommendations: []
    });

    (require('@/lib/advanced-cache-manager').advancedCacheManager.getStats as jest.Mock).mockResolvedValue({
      hitRate: 0.85,
      memoryUsage: 50 * 1024 * 1024, // 50MB
      totalRequests: 1000,
      hits: 850,
      misses: 150
    });

    (require('@/lib/request-optimizer').requestOptimizer.getStats as jest.Mock).mockResolvedValue({
      throughputPerSecond: 25.5,
      averageWaitTime: 50,
      totalOptimized: 500
    });

    (require('@/lib/request-optimizer').requestOptimizer.getActiveRequestCount as jest.Mock).mockReturnValue(5);
    (require('@/lib/request-optimizer').requestOptimizer.getQueuedRequestCount as jest.Mock).mockReturnValue(12);

    (require('@/lib/ai-service-monitor').aiServiceMonitor.getMetrics as jest.Mock).mockResolvedValue({
      totalRequests: 1000,
      failedRequests: 25,
      averageResponseTime: 150,
      cacheHitRate: 0.85
    });
  });

  afterEach(() => {
    enhancedAlertingService.stop();
  });

  describe('Enhanced Alerting Service', () => {
    it('should start and stop correctly', () => {
      expect(() => enhancedAlertingService.start()).not.toThrow();
      expect(() => enhancedAlertingService.stop()).not.toThrow();
    });

    it('should create alerts with proper structure', async () => {
      enhancedAlertingService.start();

      const alertId = await enhancedAlertingService.createAlert({
        title: 'Test Alert',
        message: 'This is a test alert',
        severity: 'medium',
        type: 'PERFORMANCE',
        source: 'test',
        metadata: { test: true }
      });

      expect(alertId).toBeDefined();
      expect(typeof alertId).toBe('string');

      const alerts = enhancedAlertingService.getActiveAlerts();
      expect(alerts).toHaveLength(1);
      expect(alerts[0].title).toBe('Test Alert');
      expect(alerts[0].severity).toBe('medium');
      expect(alerts[0].acknowledged).toBe(false);
      expect(alerts[0].resolved).toBe(false);
    });

    it('should acknowledge alerts correctly', async () => {
      enhancedAlertingService.start();

      const alertId = await enhancedAlertingService.createAlert({
        title: 'Test Alert',
        message: 'This is a test alert',
        severity: 'high',
        type: 'PERFORMANCE',
        source: 'test',
        metadata: {}
      });

      const acknowledged = enhancedAlertingService.acknowledgeAlert(alertId, '<EMAIL>');
      expect(acknowledged).toBe(true);

      const alerts = enhancedAlertingService.getActiveAlerts();
      expect(alerts[0].acknowledged).toBe(true);
      expect(alerts[0].acknowledgedBy).toBe('<EMAIL>');
    });

    it('should resolve alerts correctly', async () => {
      enhancedAlertingService.stop(); // Ensure clean state
      enhancedAlertingService.start();

      const alertId = await enhancedAlertingService.createAlert({
        title: 'Test Alert for Resolution',
        message: 'This is a test alert for resolution',
        severity: 'critical',
        type: 'ERROR',
        source: 'test_resolve',
        metadata: {}
      });

      const resolved = enhancedAlertingService.resolveAlert(alertId, '<EMAIL>');
      expect(resolved).toBe(true);

      const activeAlerts = enhancedAlertingService.getActiveAlerts();
      expect(activeAlerts).toHaveLength(0); // Resolved alerts are filtered out
    });

    it('should provide alert statistics', async () => {
      enhancedAlertingService.stop(); // Ensure clean state
      enhancedAlertingService.start();

      // Create multiple alerts
      await enhancedAlertingService.createAlert({
        title: 'Critical Alert for Stats',
        message: 'Critical issue for stats',
        severity: 'critical',
        type: 'PERFORMANCE',
        source: 'test_stats',
        metadata: {}
      });

      await enhancedAlertingService.createAlert({
        title: 'Medium Alert for Stats',
        message: 'Medium issue for stats',
        severity: 'medium',
        type: 'CACHE',
        source: 'test_stats',
        metadata: {}
      });

      const stats = enhancedAlertingService.getAlertStats();
      expect(stats.total).toBe(2);
      expect(stats.active).toBe(2);
      expect(stats.critical).toBe(1);
      expect(stats.medium).toBe(1);
    });
  });

  describe('Performance Monitoring API', () => {
    it('should return overview data', async () => {
      const request = new NextRequest('http://localhost:3000/api/admin/performance-monitoring?view=overview&timeRange=1h');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      expect(data.data.overview).toBeDefined();
      expect(data.data.overview.systemHealth).toBeDefined();
      expect(data.data.overview.performanceScore).toBeDefined();
      expect(data.data.quickStats).toBeDefined();
    });

    it('should return alerts data', async () => {
      // Create a test alert first
      enhancedAlertingService.start();
      await enhancedAlertingService.createAlert({
        title: 'API Test Alert',
        message: 'Test alert for API',
        severity: 'medium',
        type: 'PERFORMANCE',
        source: 'api_test',
        metadata: {}
      });

      const request = new NextRequest('http://localhost:3000/api/admin/performance-monitoring?view=alerts');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.alerts).toBeDefined();
      expect(Array.isArray(data.data.alerts)).toBe(true);
      expect(data.data.stats).toBeDefined();
    });

    it('should handle acknowledge alert action', async () => {
      enhancedAlertingService.start();
      const alertId = await enhancedAlertingService.createAlert({
        title: 'Test Alert for Acknowledgment',
        message: 'Test alert',
        severity: 'high',
        type: 'PERFORMANCE',
        source: 'test',
        metadata: {}
      });

      const request = new NextRequest('http://localhost:3000/api/admin/performance-monitoring', {
        method: 'POST',
        body: JSON.stringify({
          action: 'acknowledge_alert',
          alertId
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.message).toBe('Alert acknowledged');
    });

    it('should handle test notification action', async () => {
      const request = new NextRequest('http://localhost:3000/api/admin/performance-monitoring', {
        method: 'POST',
        body: JSON.stringify({
          action: 'test_notification'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.message).toBe('Test alert created');
    });

    it('should handle invalid view parameter', async () => {
      const request = new NextRequest('http://localhost:3000/api/admin/performance-monitoring?view=invalid');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid view parameter');
    });

    it('should handle invalid action parameter', async () => {
      const request = new NextRequest('http://localhost:3000/api/admin/performance-monitoring', {
        method: 'POST',
        body: JSON.stringify({
          action: 'invalid_action'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid action');
    });
  });

  describe('System Health Calculation', () => {
    it('should calculate healthy status for good metrics', async () => {
      // Mock excellent performance metrics
      (performanceMonitor.getInsights as jest.Mock).mockResolvedValue({
        averageResponseTime: 100, // Very fast
        overallScore: 95
      });

      (require('@/lib/ai-service-monitor').aiServiceMonitor.getMetrics as jest.Mock).mockResolvedValue({
        totalRequests: 1000,
        failedRequests: 5, // Low error rate (0.5%)
        averageResponseTime: 100
      });

      (require('@/lib/advanced-cache-manager').advancedCacheManager.getStats as jest.Mock).mockResolvedValue({
        hitRate: 0.95, // Excellent cache performance
        memoryUsage: 20 * 1024 * 1024 // Low memory usage
      });

      (require('@/lib/request-optimizer').requestOptimizer.getQueuedRequestCount as jest.Mock).mockReturnValue(5); // Low queue

      const request = new NextRequest('http://localhost:3000/api/admin/performance-monitoring?view=overview');
      const response = await GET(request);
      const data = await response.json();

      expect(data.data.overview.systemHealth).toBe('Healthy');
      expect(data.data.overview.performanceScore).toBeGreaterThan(80);
    });

    it('should calculate warning status for degraded metrics', async () => {
      // Mock degraded performance - need to trigger warning thresholds
      (performanceMonitor.getInsights as jest.Mock).mockResolvedValue({
        averageResponseTime: 1100, // Above 1000ms threshold for warning
        overallScore: 75
      });

      (require('@/lib/ai-service-monitor').aiServiceMonitor.getMetrics as jest.Mock).mockResolvedValue({
        totalRequests: 1000,
        failedRequests: 8, // Low error rate (0.8%) - below 1% threshold
        averageResponseTime: 1100
      });

      (require('@/lib/advanced-cache-manager').advancedCacheManager.getStats as jest.Mock).mockResolvedValue({
        hitRate: 0.75, // Good cache performance
        memoryUsage: 60 * 1024 * 1024 // Moderate memory usage
      });

      (require('@/lib/request-optimizer').requestOptimizer.getQueuedRequestCount as jest.Mock).mockReturnValue(30); // Low queue

      const request = new NextRequest('http://localhost:3000/api/admin/performance-monitoring?view=overview');
      const response = await GET(request);
      const data = await response.json();

      expect(data.data.overview.systemHealth).toBe('Warning');
    });

    it('should calculate critical status for poor metrics', async () => {
      // Mock critical performance
      (performanceMonitor.getInsights as jest.Mock).mockResolvedValue({
        averageResponseTime: 3000, // Very slow response time
        overallScore: 40
      });

      (require('@/lib/ai-service-monitor').aiServiceMonitor.getMetrics as jest.Mock).mockResolvedValue({
        totalRequests: 1000,
        failedRequests: 100, // Very high error rate
        averageResponseTime: 3000
      });

      (require('@/lib/advanced-cache-manager').advancedCacheManager.getStats as jest.Mock).mockResolvedValue({
        hitRate: 0.3, // Poor cache performance
        memoryUsage: 200 * 1024 * 1024 // High memory usage
      });

      (require('@/lib/request-optimizer').requestOptimizer.getQueuedRequestCount as jest.Mock).mockReturnValue(120); // High queue for critical status

      const request = new NextRequest('http://localhost:3000/api/admin/performance-monitoring?view=overview');
      const response = await GET(request);
      const data = await response.json();

      expect(data.data.overview.systemHealth).toBe('Critical');
    });
  });

  describe('Performance Score Calculation', () => {
    it('should calculate high score for excellent performance', async () => {
      // Mock excellent performance
      (performanceMonitor.getInsights as jest.Mock).mockResolvedValue({
        averageResponseTime: 100, // Very fast
        overallScore: 95
      });

      (require('@/lib/ai-service-monitor').aiServiceMonitor.getMetrics as jest.Mock).mockResolvedValue({
        totalRequests: 1000,
        failedRequests: 1, // Very low error rate
        averageResponseTime: 100
      });

      (require('@/lib/advanced-cache-manager').advancedCacheManager.getStats as jest.Mock).mockResolvedValue({
        hitRate: 0.95, // Excellent cache performance
        memoryUsage: 20 * 1024 * 1024 // Low memory usage
      });

      (require('@/lib/request-optimizer').requestOptimizer.getQueuedRequestCount as jest.Mock).mockReturnValue(2); // Very low queue

      const request = new NextRequest('http://localhost:3000/api/admin/performance-monitoring?view=overview');
      const response = await GET(request);
      const data = await response.json();

      expect(data.data.overview.performanceScore).toBeGreaterThan(80);
    });
  });

  describe('Integration with Performance Monitor', () => {
    it('should integrate alerting service with performance monitor', () => {
      // Test that performance monitor can create alerts through enhanced alerting service
      const originalCreateAlert = enhancedAlertingService.createAlert;
      const createAlertSpy = jest.spyOn(enhancedAlertingService, 'createAlert');

      // Simulate performance monitor creating an alert
      performanceMonitor.recordMetric('test_operation', 3000); // High response time

      // In a real scenario, this would trigger threshold checks
      // For testing, we'll verify the integration exists
      expect(createAlertSpy).toBeDefined();
      
      createAlertSpy.mockRestore();
    });
  });
});
