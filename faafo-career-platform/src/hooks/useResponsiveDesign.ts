'use client';

import { useState, useEffect, useCallback } from 'react';

export type BreakpointKey = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

export interface BreakpointConfig {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  '2xl': number;
}

const defaultBreakpoints: BreakpointConfig = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

/**
 * Hook for responsive design utilities
 */
export function useResponsiveDesign(customBreakpoints?: Partial<BreakpointConfig>) {
  const breakpoints = { ...defaultBreakpoints, ...customBreakpoints };
  const [windowSize, setWindowSize] = useState({
    width: 0,
    height: 0,
  });
  const [currentBreakpoint, setCurrentBreakpoint] = useState<BreakpointKey>('xs');
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setWindowSize({ width, height });

      // Determine current breakpoint
      let currentBp: BreakpointKey = 'xs';
      if (width >= breakpoints['2xl']) currentBp = '2xl';
      else if (width >= breakpoints.xl) currentBp = 'xl';
      else if (width >= breakpoints.lg) currentBp = 'lg';
      else if (width >= breakpoints.md) currentBp = 'md';
      else if (width >= breakpoints.sm) currentBp = 'sm';

      setCurrentBreakpoint(currentBp);
      setIsMobile(width < breakpoints.md);
      setIsTablet(width >= breakpoints.md && width < breakpoints.lg);
      setIsDesktop(width >= breakpoints.lg);
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, [breakpoints]);

  const isBreakpoint = useCallback((bp: BreakpointKey) => {
    return currentBreakpoint === bp;
  }, [currentBreakpoint]);

  const isBreakpointUp = useCallback((bp: BreakpointKey) => {
    return windowSize.width >= breakpoints[bp];
  }, [windowSize.width, breakpoints]);

  const isBreakpointDown = useCallback((bp: BreakpointKey) => {
    return windowSize.width < breakpoints[bp];
  }, [windowSize.width, breakpoints]);

  const getResponsiveValue = useCallback(<T>(values: Partial<Record<BreakpointKey, T>>): T | undefined => {
    const orderedBreakpoints: BreakpointKey[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
    
    for (const bp of orderedBreakpoints) {
      if (isBreakpointUp(bp) && values[bp] !== undefined) {
        return values[bp];
      }
    }
    
    return values.xs;
  }, [isBreakpointUp]);

  return {
    windowSize,
    currentBreakpoint,
    isMobile,
    isTablet,
    isDesktop,
    isBreakpoint,
    isBreakpointUp,
    isBreakpointDown,
    getResponsiveValue,
    breakpoints,
  };
}

/**
 * Hook for touch device detection
 */
export function useTouchDevice() {
  const [isTouchDevice, setIsTouchDevice] = useState(false);
  const [hasHover, setHasHover] = useState(true);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const checkTouchDevice = () => {
      const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      const hasHoverCapability = window.matchMedia('(hover: hover)').matches;
      
      setIsTouchDevice(hasTouch);
      setHasHover(hasHoverCapability);
    };

    checkTouchDevice();
    
    // Listen for changes in hover capability (e.g., when connecting/disconnecting mouse)
    const hoverMediaQuery = window.matchMedia('(hover: hover)');
    hoverMediaQuery.addEventListener('change', checkTouchDevice);

    return () => {
      hoverMediaQuery.removeEventListener('change', checkTouchDevice);
    };
  }, []);

  return { isTouchDevice, hasHover };
}

/**
 * Hook for responsive typography
 */
export function useResponsiveTypography() {
  const { currentBreakpoint, getResponsiveValue } = useResponsiveDesign();

  const getTextSize = useCallback((sizes: Partial<Record<BreakpointKey, string>>) => {
    return getResponsiveValue(sizes) || 'text-base';
  }, [getResponsiveValue]);

  const getHeadingSize = useCallback((level: 1 | 2 | 3 | 4 | 5 | 6) => {
    const headingSizes = {
      1: {
        xs: 'text-2xl',
        sm: 'text-3xl',
        md: 'text-4xl',
        lg: 'text-5xl',
        xl: 'text-6xl',
      },
      2: {
        xs: 'text-xl',
        sm: 'text-2xl',
        md: 'text-3xl',
        lg: 'text-4xl',
        xl: 'text-5xl',
      },
      3: {
        xs: 'text-lg',
        sm: 'text-xl',
        md: 'text-2xl',
        lg: 'text-3xl',
        xl: 'text-4xl',
      },
      4: {
        xs: 'text-base',
        sm: 'text-lg',
        md: 'text-xl',
        lg: 'text-2xl',
        xl: 'text-3xl',
      },
      5: {
        xs: 'text-sm',
        sm: 'text-base',
        md: 'text-lg',
        lg: 'text-xl',
        xl: 'text-2xl',
      },
      6: {
        xs: 'text-xs',
        sm: 'text-sm',
        md: 'text-base',
        lg: 'text-lg',
        xl: 'text-xl',
      },
    };

    return getResponsiveValue(headingSizes[level]) || 'text-base';
  }, [getResponsiveValue]);

  return { getTextSize, getHeadingSize, currentBreakpoint };
}

/**
 * Hook for responsive spacing
 */
export function useResponsiveSpacing() {
  const { getResponsiveValue } = useResponsiveDesign();

  const getSpacing = useCallback((sizes: Partial<Record<BreakpointKey, string>>) => {
    return getResponsiveValue(sizes) || 'p-4';
  }, [getResponsiveValue]);

  const getMargin = useCallback((sizes: Partial<Record<BreakpointKey, string>>) => {
    return getResponsiveValue(sizes) || 'm-4';
  }, [getResponsiveValue]);

  const getPadding = useCallback((sizes: Partial<Record<BreakpointKey, string>>) => {
    return getResponsiveValue(sizes) || 'p-4';
  }, [getResponsiveValue]);

  return { getSpacing, getMargin, getPadding };
}

/**
 * Hook for responsive grid and layout
 */
export function useResponsiveLayout() {
  const { getResponsiveValue, isMobile, isTablet, isDesktop } = useResponsiveDesign();

  const getGridCols = useCallback((cols: Partial<Record<BreakpointKey, number>>) => {
    const colValue = getResponsiveValue(cols) || 1;
    return `grid-cols-${colValue}`;
  }, [getResponsiveValue]);

  const getFlexDirection = useCallback((directions: Partial<Record<BreakpointKey, 'row' | 'col'>>) => {
    const direction = getResponsiveValue(directions) || 'col';
    return direction === 'row' ? 'flex-row' : 'flex-col';
  }, [getResponsiveValue]);

  const getContainerClass = useCallback(() => {
    if (isMobile) return 'container-mobile px-4 max-w-full';
    if (isTablet) return 'container-tablet px-6 max-w-4xl mx-auto';
    if (isDesktop) return 'container-desktop px-8 max-w-7xl mx-auto';
    return 'container px-4';
  }, [isMobile, isTablet, isDesktop]);

  return {
    getGridCols,
    getFlexDirection,
    getContainerClass,
    isMobile,
    isTablet,
    isDesktop,
  };
}
