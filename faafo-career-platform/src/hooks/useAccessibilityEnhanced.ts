'use client';

import React, { useEffect, useRef, useCallback, useState } from 'react';
// Mock accessibility provider for now - will be replaced with actual provider
const mockAccessibilityProvider = {
  settings: {
    reducedMotion: false,
    highContrast: false,
    largeText: false,
    screenReaderMode: false,
    keyboardNavigation: true,
    focusVisible: true,
  },
  updateSetting: () => {},
  announceToScreenReader: (message: string, priority?: 'polite' | 'assertive') => {
    console.log(`Screen reader announcement (${priority || 'polite'}): ${message}`);
  },
  setFocusToElement: () => {},
  skipToContent: () => {},
};

const useAccessibility = () => mockAccessibilityProvider;

/**
 * Enhanced accessibility hook with comprehensive features
 */
export function useAccessibilityEnhanced() {
  const accessibility = useAccessibility();
  const [isReducedMotion, setIsReducedMotion] = useState(false);
  const [prefersHighContrast, setPrefersHighContrast] = useState(false);
  const [prefersDarkMode, setPrefersDarkMode] = useState(false);

  // Detect user preferences
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQueries = {
      reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)'),
      highContrast: window.matchMedia('(prefers-contrast: high)'),
      darkMode: window.matchMedia('(prefers-color-scheme: dark)')
    };

    const updatePreferences = () => {
      setIsReducedMotion(mediaQueries.reducedMotion.matches);
      setPrefersHighContrast(mediaQueries.highContrast.matches);
      setPrefersDarkMode(mediaQueries.darkMode.matches);
    };

    updatePreferences();

    Object.values(mediaQueries).forEach(mq => {
      mq.addEventListener('change', updatePreferences);
    });

    return () => {
      Object.values(mediaQueries).forEach(mq => {
        mq.removeEventListener('change', updatePreferences);
      });
    };
  }, []);

  return {
    ...accessibility,
    isReducedMotion,
    prefersHighContrast,
    prefersDarkMode,
  };
}

/**
 * Hook for managing ARIA live regions
 */
export function useAriaLiveRegion() {
  const liveRegionRef = useRef<HTMLDivElement>(null);
  const { announceToScreenReader } = useAccessibility();

  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    // Use the accessibility provider's announce function
    announceToScreenReader(message, priority);

    // Also update live region if it exists
    if (liveRegionRef.current) {
      liveRegionRef.current.textContent = message;
      liveRegionRef.current.setAttribute('aria-live', priority);
    }
  }, [announceToScreenReader]);

  const createLiveRegion = useCallback((priority: 'polite' | 'assertive' = 'polite') => {
    return React.createElement('div', {
      ref: liveRegionRef,
      'aria-live': priority,
      'aria-atomic': 'true',
      className: 'sr-only',
      role: 'status'
    });
  }, []);

  return { announce, createLiveRegion };
}

/**
 * Hook for keyboard navigation management
 */
export function useKeyboardNavigation() {
  const { settings } = useAccessibility();

  const handleKeyDown = useCallback((event: KeyboardEvent, handlers: Record<string, () => void>) => {
    if (!settings.keyboardNavigation) return;

    const handler = handlers[event.key];
    if (handler) {
      event.preventDefault();
      handler();
    }
  }, [settings.keyboardNavigation]);

  const trapFocus = useCallback((containerRef: React.RefObject<HTMLElement>) => {
    if (!containerRef.current) return;

    const focusableElements = containerRef.current.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    };

    document.addEventListener('keydown', handleTabKey);
    firstElement?.focus();

    return () => {
      document.removeEventListener('keydown', handleTabKey);
    };
  }, []);

  return { handleKeyDown, trapFocus };
}

/**
 * Hook for form accessibility enhancements
 */
export function useFormAccessibility() {
  const { announce } = useAriaLiveRegion();

  const announceError = useCallback((fieldName: string, error: string) => {
    announce(`Error in ${fieldName}: ${error}`, 'assertive');
  }, [announce]);

  const announceSuccess = useCallback((message: string) => {
    announce(message, 'polite');
  }, [announce]);

  const announceValidation = useCallback((isValid: boolean, fieldName: string, message?: string) => {
    if (isValid) {
      announce(`${fieldName} is valid`, 'polite');
    } else if (message) {
      announceError(fieldName, message);
    }
  }, [announce, announceError]);

  const getFieldProps = useCallback((fieldName: string, error?: string, description?: string) => {
    const fieldId = `field-${fieldName.toLowerCase().replace(/\s+/g, '-')}`;
    const errorId = error ? `${fieldId}-error` : undefined;
    const descriptionId = description ? `${fieldId}-description` : undefined;

    return {
      id: fieldId,
      'aria-invalid': !!error,
      'aria-describedby': [descriptionId, errorId].filter(Boolean).join(' ') || undefined,
      'aria-required': true,
    };
  }, []);

  return {
    announceError,
    announceSuccess,
    announceValidation,
    getFieldProps,
  };
}

/**
 * Hook for loading state accessibility
 */
export function useLoadingAccessibility() {
  const { announce } = useAriaLiveRegion();
  const [isLoading, setIsLoading] = useState(false);

  const startLoading = useCallback((message = 'Loading...') => {
    setIsLoading(true);
    announce(message, 'polite');
  }, [announce]);

  const stopLoading = useCallback((message = 'Loading complete') => {
    setIsLoading(false);
    announce(message, 'polite');
  }, [announce]);

  const loadingProps = {
    'aria-busy': isLoading,
    'aria-live': 'polite' as const,
    role: 'status' as const,
  };

  return {
    isLoading,
    startLoading,
    stopLoading,
    loadingProps,
  };
}

/**
 * Hook for color contrast utilities
 */
export function useColorContrast() {
  const { settings } = useAccessibility();

  const getContrastClass = useCallback((baseClass: string) => {
    if (settings.highContrast) {
      return `${baseClass} high-contrast`;
    }
    return baseClass;
  }, [settings.highContrast]);

  const getTextColor = useCallback((background: 'light' | 'dark') => {
    if (settings.highContrast) {
      return background === 'light' ? 'text-black' : 'text-white';
    }
    return background === 'light' ? 'text-gray-900' : 'text-gray-100';
  }, [settings.highContrast]);

  return { getContrastClass, getTextColor };
}
