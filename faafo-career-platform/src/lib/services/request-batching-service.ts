/**
 * Request Batching Service for Skills Analysis API
 * Implements intelligent request batching, deduplication, and concurrent processing
 * to optimize performance and reduce AI service calls
 */

import { consolidatedCache } from './consolidated-cache-service';
import { geminiService } from './geminiService';
import { edgeCaseHandlerService } from '@/lib/skills/EdgeCaseHandlerService';

interface BatchRequest {
  id: string;
  userId: string;
  requestData: any;
  resolve: (result: any) => void;
  reject: (error: any) => void;
  timestamp: number;
  priority: 'high' | 'medium' | 'low';
}

interface BatchProcessingResult {
  success: boolean;
  data?: any;
  error?: string;
  cached?: boolean;
  batchId?: string;
  processingTime?: number;
}

interface BatchConfiguration {
  maxBatchSize: number;
  batchTimeoutMs: number;
  maxWaitTimeMs: number;
  enableDeduplication: boolean;
  enableConcurrentProcessing: boolean;
}

export class RequestBatchingService {
  private static instance: RequestBatchingService;
  private pendingRequests: Map<string, BatchRequest[]> = new Map();
  private processingBatches: Set<string> = new Set();
  private batchTimers: Map<string, NodeJS.Timeout> = new Map();
  
  private readonly config: BatchConfiguration = {
    maxBatchSize: parseInt(process.env.BATCH_MAX_SIZE || '5'),
    batchTimeoutMs: parseInt(process.env.BATCH_TIMEOUT_MS || '2000'),
    maxWaitTimeMs: parseInt(process.env.BATCH_MAX_WAIT_MS || '5000'),
    enableDeduplication: process.env.ENABLE_REQUEST_DEDUPLICATION !== 'false',
    enableConcurrentProcessing: process.env.ENABLE_CONCURRENT_PROCESSING !== 'false',
  };

  // Performance metrics
  private metrics = {
    totalRequests: 0,
    batchedRequests: 0,
    deduplicatedRequests: 0,
    cacheHits: 0,
    averageResponseTime: 0,
    batchSavings: 0,
  };

  private constructor() {
    // Initialize cleanup interval
    setInterval(() => this.cleanupStaleRequests(), 30000);
  }

  public static getInstance(): RequestBatchingService {
    if (!RequestBatchingService.instance) {
      RequestBatchingService.instance = new RequestBatchingService();
    }
    return RequestBatchingService.instance;
  }

  /**
   * Add a comprehensive skills analysis request to the batch
   */
  async batchComprehensiveAnalysis(
    userId: string,
    requestData: any,
    priority: 'high' | 'medium' | 'low' = 'medium'
  ): Promise<BatchProcessingResult> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    // Generate request signature for deduplication
    const requestSignature = this.generateRequestSignature(requestData);
    
    // Check for immediate cache hit
    const cacheKey = this.generateCacheKey(userId, requestSignature);
    const cachedResult = await consolidatedCache.get<any>(cacheKey);
    if (cachedResult) {
      this.metrics.cacheHits++;
      return {
        success: true,
        data: cachedResult,
        cached: true,
        processingTime: Date.now() - startTime,
      };
    }

    // Check for duplicate request in current batch
    if (this.config.enableDeduplication) {
      const duplicateResult = await this.checkForDuplicateRequest(requestSignature);
      if (duplicateResult) {
        this.metrics.deduplicatedRequests++;
        return duplicateResult;
      }
    }

    // Create batch request
    return new Promise((resolve, reject) => {
      const batchRequest: BatchRequest = {
        id: this.generateRequestId(),
        userId,
        requestData,
        resolve,
        reject,
        timestamp: Date.now(),
        priority,
      };

      this.addToBatch(requestSignature, batchRequest);
    });
  }

  /**
   * Add request to appropriate batch and trigger processing if needed
   */
  private addToBatch(signature: string, request: BatchRequest): void {
    if (!this.pendingRequests.has(signature)) {
      this.pendingRequests.set(signature, []);
    }

    const batch = this.pendingRequests.get(signature)!;
    batch.push(request);
    this.metrics.batchedRequests++;

    // Sort by priority
    batch.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });

    // Check if batch should be processed
    if (batch.length >= this.config.maxBatchSize) {
      this.processBatch(signature);
    } else if (!this.batchTimers.has(signature)) {
      // Set timer for batch processing
      const timer = setTimeout(() => {
        this.processBatch(signature);
      }, this.config.batchTimeoutMs);
      this.batchTimers.set(signature, timer);
    }
  }

  /**
   * Process a batch of similar requests
   */
  private async processBatch(signature: string): Promise<void> {
    const batch = this.pendingRequests.get(signature);
    if (!batch || batch.length === 0 || this.processingBatches.has(signature)) {
      return;
    }

    // Mark batch as processing
    this.processingBatches.add(signature);
    this.pendingRequests.delete(signature);
    
    // Clear timer
    const timer = this.batchTimers.get(signature);
    if (timer) {
      clearTimeout(timer);
      this.batchTimers.delete(signature);
    }

    const batchId = this.generateBatchId();
    const startTime = Date.now();

    try {
      // Process requests concurrently if enabled
      if (this.config.enableConcurrentProcessing && batch.length > 1) {
        await this.processConcurrentBatch(batch, batchId);
      } else {
        await this.processSequentialBatch(batch, batchId);
      }

      const processingTime = Date.now() - startTime;
      this.metrics.batchSavings += Math.max(0, (batch.length - 1) * 30000 - processingTime);
      
    } catch (error) {
      console.error(`Batch processing failed for signature ${signature}:`, error);
      // Reject all requests in batch
      batch.forEach(request => {
        request.reject(new Error(`Batch processing failed: ${error}`));
      });
    } finally {
      this.processingBatches.delete(signature);
    }
  }

  /**
   * Process batch requests concurrently
   */
  private async processConcurrentBatch(batch: BatchRequest[], batchId: string): Promise<void> {
    const concurrentPromises = batch.map(async (request) => {
      try {
        const result = await this.processIndividualRequest(request, batchId);
        request.resolve(result);
      } catch (error) {
        request.reject(error);
      }
    });

    await Promise.allSettled(concurrentPromises);
  }

  /**
   * Process batch requests sequentially (fallback)
   */
  private async processSequentialBatch(batch: BatchRequest[], batchId: string): Promise<void> {
    for (const request of batch) {
      try {
        const result = await this.processIndividualRequest(request, batchId);
        request.resolve(result);
      } catch (error) {
        request.reject(error);
      }
    }
  }

  /**
   * Process individual request within batch
   */
  private async processIndividualRequest(
    request: BatchRequest,
    batchId: string
  ): Promise<BatchProcessingResult> {
    const startTime = Date.now();
    
    try {
      // Use EdgeCaseHandler for comprehensive error handling
      const edgeCaseResult = await edgeCaseHandlerService.handleLearningPathGeneration({
        userId: request.userId,
        currentSkills: request.requestData.currentSkills?.map((skill: any) => ({
          skill: skill.skillName,
          level: skill.selfRating,
          confidence: skill.confidenceLevel
        })) || [],
        targetRole: request.requestData.targetCareerPath?.careerPathName,
        timeframe: this.mapTimeframeToMonths(request.requestData.preferences?.timeframe),
        learningStyle: 'balanced',
        availability: request.requestData.preferences?.hoursPerWeek || 10,
        budget: this.mapBudgetToAmount(request.requestData.preferences?.budget)
      });

      let analysisResult;
      if (edgeCaseResult.success) {
        analysisResult = { success: true, data: edgeCaseResult.data };
      } else {
        // Fall back to direct AI service call
        analysisResult = await geminiService.analyzeComprehensiveSkillGap(
          request.requestData.currentSkills || [],
          request.requestData.targetCareerPath,
          request.requestData.preferences,
          null, // careerPathData will be fetched separately
          request.userId
        );
      }

      if (!analysisResult.success) {
        throw new Error(analysisResult.error || 'Analysis failed');
      }

      // Cache the result
      const requestSignature = this.generateRequestSignature(request.requestData);
      const cacheKey = this.generateCacheKey(request.userId, requestSignature);
      await consolidatedCache.set(cacheKey, analysisResult.data, {
        ttl: 1800000, // 30 minutes in milliseconds
        tags: ['skills_analysis', 'batch_request', request.userId]
      });

      return {
        success: true,
        data: analysisResult.data,
        batchId,
        processingTime: Date.now() - startTime,
      };

    } catch (error) {
      return {
        success: false,
        error: (error as Error).message,
        batchId,
        processingTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Generate request signature for deduplication
   */
  private generateRequestSignature(requestData: any): string {
    const key = {
      careerPath: requestData.targetCareerPath?.careerPathName || '',
      targetLevel: requestData.targetCareerPath?.targetLevel || '',
      timeframe: requestData.preferences?.timeframe || '',
      skillsHash: this.hashSkills(requestData.currentSkills || []),
    };
    
    return Buffer.from(JSON.stringify(key)).toString('base64').slice(0, 32);
  }

  /**
   * Generate cache key for request
   */
  private generateCacheKey(userId: string, signature: string): string {
    return `batch_analysis:${signature}:${userId}`;
  }

  /**
   * Hash skills array for signature generation
   */
  private hashSkills(skills: any[]): string {
    const skillsString = skills
      .map(skill => `${skill.skillName}:${skill.selfRating}`)
      .sort()
      .join('|');
    
    let hash = 0;
    for (let i = 0; i < skillsString.length; i++) {
      const char = skillsString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Check for duplicate request in processing batches
   */
  private async checkForDuplicateRequest(signature: string): Promise<BatchProcessingResult | null> {
    // Check if there's already a batch processing this signature
    if (this.processingBatches.has(signature)) {
      // Wait for the batch to complete
      return new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (!this.processingBatches.has(signature)) {
            clearInterval(checkInterval);
            // Try to get from cache
            const cacheKey = this.generateCacheKey('shared', signature);
            consolidatedCache.get<any>(cacheKey).then(cached => {
              if (cached) {
                resolve({
                  success: true,
                  data: cached,
                  cached: true,
                });
              } else {
                resolve(null);
              }
            });
          }
        }, 100);
      });
    }
    return null;
  }

  /**
   * Utility methods
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateBatchId(): string {
    return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private mapTimeframeToMonths(timeframe: string): number {
    switch (timeframe) {
      case 'THREE_MONTHS': return 3;
      case 'SIX_MONTHS': return 6;
      case 'ONE_YEAR': return 12;
      case 'TWO_YEARS': return 24;
      default: return 12;
    }
  }

  private mapBudgetToAmount(budget: string): number {
    switch (budget) {
      case 'FREE': return 0;
      case 'FREEMIUM': return 100;
      case 'PAID': return 1000;
      default: return 500;
    }
  }

  /**
   * Cleanup stale requests
   */
  private cleanupStaleRequests(): void {
    const now = Date.now();
    const maxAge = this.config.maxWaitTimeMs;

    Array.from(this.pendingRequests.entries()).forEach(([signature, batch]) => {
      const staleBatch = batch.filter(request => now - request.timestamp > maxAge);
      if (staleBatch.length > 0) {
        // Remove stale requests and reject them
        staleBatch.forEach(request => {
          request.reject(new Error('Request timeout - exceeded maximum wait time'));
        });
        
        // Update batch
        const freshBatch = batch.filter(request => now - request.timestamp <= maxAge);
        if (freshBatch.length > 0) {
          this.pendingRequests.set(signature, freshBatch);
        } else {
          this.pendingRequests.delete(signature);
          const timer = this.batchTimers.get(signature);
          if (timer) {
            clearTimeout(timer);
            this.batchTimers.delete(signature);
          }
        }
      }
    });
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      batchEfficiency: this.metrics.totalRequests > 0 
        ? (this.metrics.batchedRequests / this.metrics.totalRequests) * 100 
        : 0,
      cacheHitRate: this.metrics.totalRequests > 0 
        ? (this.metrics.cacheHits / this.metrics.totalRequests) * 100 
        : 0,
      deduplicationRate: this.metrics.totalRequests > 0 
        ? (this.metrics.deduplicatedRequests / this.metrics.totalRequests) * 100 
        : 0,
      pendingBatches: this.pendingRequests.size,
      processingBatches: this.processingBatches.size,
    };
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const metrics = this.getMetrics();
      return metrics.pendingBatches < 10 && metrics.processingBatches < 5;
    } catch (error) {
      console.error('Request batching service health check failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const requestBatchingService = RequestBatchingService.getInstance();
