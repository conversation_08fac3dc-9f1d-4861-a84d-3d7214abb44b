/**
 * Memory Cache Service
 * 
 * Simple in-memory cache implementation that follows the CacheServiceInterface.
 * Used as a fallback when Redis is not available or for development environments.
 */

import { 
  CacheServiceInterface, 
  CacheConfiguration 
} from '@/lib/interfaces/service-interfaces';

interface CacheEntry<T> {
  value: T;
  expiresAt: number;
  createdAt: number;
  accessCount: number;
  lastAccessed: number;
}

export class MemoryCacheService implements CacheServiceInterface {
  readonly name = 'MemoryCacheService';
  readonly version = '1.0.0';

  private cache = new Map<string, CacheEntry<any>>();
  private defaultTTL: number;
  private maxSize: number;
  private cleanupInterval: NodeJS.Timeout;

  constructor(config: CacheConfiguration) {
    this.defaultTTL = config.ttl || 3600;
    this.maxSize = config.maxSize || 10000;

    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000); // Cleanup every minute

    console.log(`[MemoryCacheService] Initialized with TTL: ${this.defaultTTL}s, Max Size: ${this.maxSize}`);
  }

  /**
   * Get value from cache
   */
  async get<T>(key: string): Promise<T | null> {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();

    return entry.value as T;
  }

  /**
   * Set value in cache
   */
  async set<T>(key: string, value: T, ttlSeconds?: number): Promise<void> {
    const ttl = ttlSeconds || this.defaultTTL;
    const now = Date.now();
    
    // Check cache size limit
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLRU();
    }

    const entry: CacheEntry<T> = {
      value,
      expiresAt: now + (ttl * 1000),
      createdAt: now,
      accessCount: 0,
      lastAccessed: now
    };

    this.cache.set(key, entry);
  }

  /**
   * Delete value from cache
   */
  async delete(key: string): Promise<void> {
    this.cache.delete(key);
  }

  /**
   * Clear all cache entries
   */
  async clear(): Promise<void> {
    this.cache.clear();
  }

  /**
   * Get multiple values from cache
   */
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    const results: (T | null)[] = [];
    
    for (const key of keys) {
      const value = await this.get<T>(key);
      results.push(value);
    }
    
    return results;
  }

  /**
   * Set multiple values in cache
   */
  async mset<T>(keyValues: Record<string, T>, ttlSeconds?: number): Promise<void> {
    const promises = Object.entries(keyValues).map(([key, value]) =>
      this.set(key, value, ttlSeconds)
    );
    
    await Promise.all(promises);
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const testKey = '__health_check__';
      const testValue = 'ok';
      
      await this.set(testKey, testValue, 1);
      const result = await this.get(testKey);
      await this.delete(testKey);
      
      return result === testValue;
    } catch (error) {
      console.error('[MemoryCacheService] Health check failed:', error);
      return false;
    }
  }

  /**
   * Get cache metrics
   */
  async getMetrics(): Promise<Record<string, any>> {
    const now = Date.now();
    let totalSize = 0;
    let expiredCount = 0;
    let totalAccessCount = 0;

    const cacheEntries = Array.from(this.cache.entries());
    for (const [key, entry] of cacheEntries) {
      totalSize += this.estimateSize(key, entry.value);

      if (now > entry.expiresAt) {
        expiredCount++;
      }

      totalAccessCount += entry.accessCount;
    }

    return {
      totalEntries: this.cache.size,
      maxSize: this.maxSize,
      utilizationPercent: (this.cache.size / this.maxSize) * 100,
      estimatedSizeBytes: totalSize,
      expiredEntries: expiredCount,
      totalAccessCount,
      averageAccessCount: this.cache.size > 0 ? totalAccessCount / this.cache.size : 0
    };
  }

  /**
   * Cleanup expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    const cacheEntries = Array.from(this.cache.entries());
    for (const [key, entry] of cacheEntries) {
      if (now > entry.expiresAt) {
        keysToDelete.push(key);
      }
    }

    for (const key of keysToDelete) {
      this.cache.delete(key);
    }

    if (keysToDelete.length > 0) {
      console.log(`[MemoryCacheService] Cleaned up ${keysToDelete.length} expired entries`);
    }
  }

  /**
   * Evict least recently used entry
   */
  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    const cacheEntries = Array.from(this.cache.entries());
    for (const [key, entry] of cacheEntries) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      console.log(`[MemoryCacheService] Evicted LRU entry: ${oldestKey}`);
    }
  }

  /**
   * Estimate size of cache entry
   */
  private estimateSize(key: string, value: any): number {
    try {
      const keySize = Buffer.byteLength(key, 'utf8');
      const valueSize = Buffer.byteLength(JSON.stringify(value), 'utf8');
      return keySize + valueSize + 64; // Add overhead for entry metadata
    } catch (error) {
      return 1024; // Default estimate if serialization fails
    }
  }

  /**
   * Shutdown cache service
   */
  async shutdown(): Promise<void> {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    this.cache.clear();
    console.log('[MemoryCacheService] Shutdown complete');
  }

  /**
   * Get cache statistics for debugging
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    memoryUsage: number;
  } {
    const metrics = this.getMetrics();
    
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: 0, // Would need to track hits/misses for accurate calculation
      memoryUsage: 0 // Would need process.memoryUsage() for accurate calculation
    };
  }
}
