# Service Decoupling Strategy

## Overview

This document outlines the strategy to reduce service coupling in the FAAFO Career Platform codebase. The analysis identified several high-coupling services that need refactoring to improve maintainability, testability, and modularity.

## High Coupling Issues Identified

### 1. **Security Middleware** (9 dependencies)
**Current Dependencies:**
- `withCSRFProtection` from `./csrf`
- `withRateLimit, rateLimiters` from `./rate-limit`
- `SecurityValidator` from `./validation`
- `getServerSession` from `next-auth/next`
- `authOptions` from `./auth`
- `enhancedRateLimiters` from `./enhanced-rate-limiter`
- `ComprehensiveSecurityValidator` from `./comprehensive-security-validator`
- `SecureCacheService` from `./secure-cache-service`

**Issues:**
- Violates Single Responsibility Principle
- Tightly coupled to multiple security services
- Difficult to test in isolation
- Hard to extend or modify

### 2. **Gemini Service** (8 dependencies)
**Current Dependencies:**
- `GoogleGenerativeAI` from `@google/generative-ai`
- `cache` from `@/lib/cache`
- `AIInputValidator` from `@/lib/ai-input-validator`
- Multiple commented-out advanced modules
- Direct database access patterns
- Hardcoded configuration values

**Issues:**
- Mixed concerns (AI logic + caching + validation + logging)
- Hardcoded dependencies
- Difficult to mock for testing
- Configuration scattered throughout

### 3. **AI Enhanced Assessment Service** (6 dependencies)
**Current Dependencies:**
- `AssessmentResponse, AssessmentInsights` from `./assessmentScoring`
- `EnhancedAssessmentResults, CareerPathRecommendation` from `./enhancedAssessmentService`
- `geminiService` from `./services/geminiService`
- `prisma` from `./prisma`
- `SecurityValidator` from `./validation`

**Issues:**
- Direct database coupling
- Tight coupling to specific AI service
- Mixed business logic and data access
- Hard to test without full stack

## Decoupling Strategy

### Phase 1: Interface Extraction and Dependency Injection

#### 1.1 Create Core Interfaces

**File: `src/lib/interfaces/security.ts`**
```typescript
export interface ISecurityValidator {
  validateInput(input: any): Promise<ValidationResult>;
  securityScan(input: string): SecurityScanResult;
}

export interface IRateLimiter {
  checkLimit(identifier: string): Promise<RateLimitResult>;
  resetLimit(identifier: string): Promise<void>;
}

export interface ICSRFProtection {
  validateToken(token: string, session: any): boolean;
  generateToken(): string;
}

export interface IAuthProvider {
  getSession(request: any): Promise<Session | null>;
  validateSession(session: any): boolean;
}
```

**File: `src/lib/interfaces/ai.ts`**
```typescript
export interface IAIService {
  generateContent(prompt: string, options?: AIOptions): Promise<AIResponse>;
  analyzeSkillsGap(params: SkillGapParams): Promise<AIResponse>;
  generateCareerRecommendations(params: CareerParams): Promise<AIResponse>;
  healthCheck(): Promise<HealthStatus>;
}

export interface ICacheService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, options?: CacheOptions): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
}

export interface ILogger {
  info(message: string, context?: any): void;
  warn(message: string, context?: any): void;
  error(message: string, error?: any, context?: any): void;
  debug(message: string, context?: any): void;
}
```

#### 1.2 Create Service Factory Pattern

**File: `src/lib/factories/SecurityServiceFactory.ts`**
```typescript
export class SecurityServiceFactory {
  static createSecurityValidator(): ISecurityValidator {
    return new ComprehensiveSecurityValidator();
  }
  
  static createRateLimiter(type: string): IRateLimiter {
    switch (type) {
      case 'enhanced':
        return new EnhancedRateLimiter();
      default:
        return new StandardRateLimiter();
    }
  }
  
  static createCSRFProtection(): ICSRFProtection {
    return new CSRFProtectionService();
  }
}
```

#### 1.3 Refactor Security Middleware

**File: `src/lib/security/UnifiedSecurityMiddleware.ts`**
```typescript
export class UnifiedSecurityMiddleware {
  constructor(
    private validator: ISecurityValidator,
    private rateLimiter: IRateLimiter,
    private csrfProtection: ICSRFProtection,
    private authProvider: IAuthProvider,
    private logger: ILogger
  ) {}

  async protect(
    request: NextRequest,
    handler: () => Promise<NextResponse>,
    options: SecurityOptions = {}
  ): Promise<NextResponse> {
    // Implementation using injected dependencies
  }
}
```

### Phase 2: Configuration Externalization

#### 2.1 Create Centralized Configuration

**File: `src/lib/config/ServiceConfig.ts`**
```typescript
export interface ServiceConfiguration {
  ai: {
    provider: 'gemini' | 'openai';
    apiKey: string;
    model: string;
    timeout: number;
    retries: number;
  };
  cache: {
    provider: 'redis' | 'memory';
    url?: string;
    ttl: number;
    maxSize: number;
  };
  security: {
    rateLimits: Record<string, RateLimitConfig>;
    validation: ValidationConfig;
    csrf: CSRFConfig;
  };
}

export const serviceConfig = createServiceConfig();
```

#### 2.2 Environment-Based Configuration

**File: `src/lib/config/EnvironmentConfig.ts`**
```typescript
export class EnvironmentConfig {
  static getAIConfig(): AIConfig {
    return {
      provider: process.env.AI_PROVIDER as 'gemini' | 'openai',
      apiKey: process.env.GOOGLE_GEMINI_API_KEY || '',
      model: process.env.AI_MODEL || 'gemini-1.5-flash',
      timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
      retries: parseInt(process.env.AI_RETRIES || '3')
    };
  }
}
```

### Phase 3: Service Abstraction Layers

#### 3.1 Create AI Service Abstraction

**File: `src/lib/services/AbstractAIService.ts`**
```typescript
export abstract class AbstractAIService implements IAIService {
  constructor(
    protected cache: ICacheService,
    protected validator: ISecurityValidator,
    protected logger: ILogger,
    protected config: AIConfig
  ) {}

  abstract generateContent(prompt: string, options?: AIOptions): Promise<AIResponse>;
  
  protected async validateAndCache<T>(
    key: string,
    generator: () => Promise<T>,
    options?: CacheOptions
  ): Promise<T> {
    // Common validation and caching logic
  }
}
```

#### 3.2 Implement Specific AI Services

**File: `src/lib/services/GeminiAIService.ts`**
```typescript
export class GeminiAIService extends AbstractAIService {
  private model: GenerativeModel;

  constructor(
    cache: ICacheService,
    validator: ISecurityValidator,
    logger: ILogger,
    config: AIConfig
  ) {
    super(cache, validator, logger, config);
    this.initializeModel();
  }

  async generateContent(prompt: string, options?: AIOptions): Promise<AIResponse> {
    // Gemini-specific implementation
  }
}
```

### Phase 4: Event-Driven Architecture

#### 4.1 Create Event System

**File: `src/lib/events/EventBus.ts`**
```typescript
export interface DomainEvent {
  type: string;
  payload: any;
  timestamp: Date;
  userId?: string;
}

export class EventBus {
  private handlers = new Map<string, Array<(event: DomainEvent) => Promise<void>>>();

  subscribe(eventType: string, handler: (event: DomainEvent) => Promise<void>): void {
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, []);
    }
    this.handlers.get(eventType)!.push(handler);
  }

  async publish(event: DomainEvent): Promise<void> {
    const handlers = this.handlers.get(event.type) || [];
    await Promise.all(handlers.map(handler => handler(event)));
  }
}
```

#### 4.2 Implement Event-Driven Services

**File: `src/lib/services/EventDrivenAssessmentService.ts`**
```typescript
export class EventDrivenAssessmentService {
  constructor(
    private eventBus: EventBus,
    private aiService: IAIService,
    private repository: IAssessmentRepository
  ) {
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.eventBus.subscribe('assessment.completed', this.handleAssessmentCompleted.bind(this));
    this.eventBus.subscribe('ai.insights.requested', this.handleInsightsRequested.bind(this));
  }
}
```

## Implementation Plan

### Week 1: Interface Extraction
- [ ] Create core interfaces for all major services
- [ ] Extract configuration interfaces
- [ ] Create factory patterns for service creation

### Week 2: Security Middleware Refactoring
- [ ] Implement dependency injection for SecurityMiddleware
- [ ] Create unified security service factory
- [ ] Update all security middleware usage

### Week 3: AI Service Decoupling
- [ ] Create AbstractAIService base class
- [ ] Refactor GeminiService to use dependency injection
- [ ] Implement service factory for AI services

### Week 4: Assessment Service Refactoring
- [ ] Extract repository pattern for data access
- [ ] Implement event-driven architecture
- [ ] Create service composition patterns

### Week 5: Testing and Validation
- [ ] Create comprehensive unit tests for decoupled services
- [ ] Implement integration tests
- [ ] Performance testing and optimization

## Benefits After Decoupling

### 1. **Improved Testability**
- Services can be tested in isolation
- Easy mocking of dependencies
- Faster test execution

### 2. **Better Maintainability**
- Single Responsibility Principle adherence
- Easier to understand and modify
- Reduced risk of breaking changes

### 3. **Enhanced Flexibility**
- Easy to swap implementations
- Configuration-driven behavior
- Support for multiple providers

### 4. **Reduced Coupling**
- Services depend on interfaces, not implementations
- Loose coupling through events
- Clear separation of concerns

## Success Metrics

- [ ] Reduce average service dependencies from >5 to <3
- [ ] Achieve >90% test coverage for all services
- [ ] Zero circular dependencies
- [ ] <2 second build time improvement
- [ ] Successful A/B testing of different AI providers
