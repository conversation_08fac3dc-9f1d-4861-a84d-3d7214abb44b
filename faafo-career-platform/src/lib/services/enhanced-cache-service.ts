/**
 * Enhanced Caching Service for Skills Analysis API
 * Implements multi-level caching, compression, cache warming, and intelligent cache strategies
 */

import { consolidatedCache } from './consolidated-cache-service';
import { compress, decompress } from '@/lib/utils/compression';

interface CacheEntry {
  data: any;
  timestamp: number;
  hits: number;
  size: number;
  compressed: boolean;
  tags: string[];
}

interface CacheConfiguration {
  enableCompression: boolean;
  compressionThreshold: number; // bytes
  enableL1Cache: boolean;
  l1CacheSize: number;
  enableCacheWarming: boolean;
  warmingPatterns: string[];
  enableSharedCache: boolean;
  sharedCacheTTL: number;
}

interface CacheMetrics {
  l1Hits: number;
  l2Hits: number;
  misses: number;
  compressionSavings: number;
  warmingHits: number;
  sharedCacheHits: number;
  totalRequests: number;
  averageResponseTime: number;
}

export class EnhancedCacheService {
  private static instance: EnhancedCacheService;
  private l1Cache: Map<string, CacheEntry> = new Map();
  private cacheMetrics: CacheMetrics = {
    l1Hits: 0,
    l2Hits: 0,
    misses: 0,
    compressionSavings: 0,
    warmingHits: 0,
    sharedCacheHits: 0,
    totalRequests: 0,
    averageResponseTime: 0,
  };

  private readonly config: CacheConfiguration = {
    enableCompression: process.env.ENABLE_CACHE_COMPRESSION !== 'false',
    compressionThreshold: parseInt(process.env.CACHE_COMPRESSION_THRESHOLD || '1024'),
    enableL1Cache: process.env.ENABLE_L1_CACHE !== 'false',
    l1CacheSize: parseInt(process.env.L1_CACHE_SIZE || '100'),
    enableCacheWarming: process.env.ENABLE_CACHE_WARMING === 'true',
    warmingPatterns: (process.env.CACHE_WARMING_PATTERNS || '').split(',').filter(Boolean),
    enableSharedCache: process.env.ENABLE_SHARED_CACHE !== 'false',
    sharedCacheTTL: parseInt(process.env.SHARED_CACHE_TTL || '7200'), // 2 hours
  };

  private constructor() {
    // Initialize cache warming if enabled
    if (this.config.enableCacheWarming) {
      this.initializeCacheWarming();
    }
    
    // Initialize cleanup interval
    setInterval(() => this.cleanupL1Cache(), 300000); // 5 minutes
  }

  public static getInstance(): EnhancedCacheService {
    if (!EnhancedCacheService.instance) {
      EnhancedCacheService.instance = new EnhancedCacheService();
    }
    return EnhancedCacheService.instance;
  }

  /**
   * Get data from multi-level cache
   */
  async get<T>(key: string, tags: string[] = []): Promise<T | null> {
    const startTime = Date.now();
    this.cacheMetrics.totalRequests++;

    try {
      // L1 Cache check
      if (this.config.enableL1Cache) {
        const l1Result = this.getFromL1Cache<T>(key);
        if (l1Result !== null) {
          this.cacheMetrics.l1Hits++;
          this.updateAverageResponseTime(Date.now() - startTime);
          return l1Result;
        }
      }

      // L2 Cache check (Redis/Memory)
      const l2Result = await this.getFromL2Cache<T>(key);
      if (l2Result !== null) {
        this.cacheMetrics.l2Hits++;
        
        // Store in L1 cache for faster future access
        if (this.config.enableL1Cache) {
          this.setInL1Cache(key, l2Result, tags);
        }
        
        this.updateAverageResponseTime(Date.now() - startTime);
        return l2Result;
      }

      // Check shared cache for similar requests
      if (this.config.enableSharedCache) {
        const sharedResult = await this.getFromSharedCache<T>(key, tags);
        if (sharedResult !== null) {
          this.cacheMetrics.sharedCacheHits++;
          
          // Store in both L1 and L2 caches
          if (this.config.enableL1Cache) {
            this.setInL1Cache(key, sharedResult, tags);
          }
          await this.setInL2Cache(key, sharedResult, 3600); // 1 hour
          
          this.updateAverageResponseTime(Date.now() - startTime);
          return sharedResult;
        }
      }

      this.cacheMetrics.misses++;
      this.updateAverageResponseTime(Date.now() - startTime);
      return null;

    } catch (error) {
      console.error('Enhanced cache get error:', error);
      this.cacheMetrics.misses++;
      return null;
    }
  }

  /**
   * Set data in multi-level cache
   */
  async set<T>(
    key: string, 
    data: T, 
    ttlSeconds: number = 3600, 
    tags: string[] = []
  ): Promise<void> {
    try {
      // Store in L1 cache
      if (this.config.enableL1Cache) {
        this.setInL1Cache(key, data, tags);
      }

      // Store in L2 cache
      await this.setInL2Cache(key, data, ttlSeconds);

      // Store in shared cache if applicable
      if (this.config.enableSharedCache && this.isSharedCacheCandidate(key, tags)) {
        await this.setInSharedCache(key, data, tags);
      }

    } catch (error) {
      console.error('Enhanced cache set error:', error);
    }
  }

  /**
   * L1 Cache operations (in-memory)
   */
  private getFromL1Cache<T>(key: string): T | null {
    const entry = this.l1Cache.get(key);
    if (!entry) return null;

    // Check if entry is still valid (simple TTL check)
    const maxAge = 300000; // 5 minutes for L1 cache
    if (Date.now() - entry.timestamp > maxAge) {
      this.l1Cache.delete(key);
      return null;
    }

    entry.hits++;
    return entry.compressed ? this.decompress(entry.data) : entry.data;
  }

  private setInL1Cache<T>(key: string, data: T, tags: string[]): void {
    // Check cache size limit
    if (this.l1Cache.size >= this.config.l1CacheSize) {
      this.evictLRUFromL1Cache();
    }

    const serialized = JSON.stringify(data);
    const size = Buffer.byteLength(serialized, 'utf8');
    const shouldCompress = this.config.enableCompression && size > this.config.compressionThreshold;

    const entry: CacheEntry = {
      data: shouldCompress ? this.compress(data) : data,
      timestamp: Date.now(),
      hits: 0,
      size,
      compressed: shouldCompress,
      tags,
    };

    if (shouldCompress) {
      this.cacheMetrics.compressionSavings += size - Buffer.byteLength(JSON.stringify(entry.data), 'utf8');
    }

    this.l1Cache.set(key, entry);
  }

  /**
   * L2 Cache operations (Redis/Memory)
   */
  private async getFromL2Cache<T>(key: string): Promise<T | null> {
    try {
      const cached = await consolidatedCache.get<any>(key);
      if (!cached) return null;

      // Handle compressed data
      if (cached._compressed) {
        return this.decompress(cached.data);
      }

      return cached;
    } catch (error) {
      console.error('L2 cache get error:', error);
      return null;
    }
  }

  private async setInL2Cache<T>(key: string, data: T, ttlSeconds: number): Promise<void> {
    try {
      const serialized = JSON.stringify(data);
      const size = Buffer.byteLength(serialized, 'utf8');
      const shouldCompress = this.config.enableCompression && size > this.config.compressionThreshold;

      const cacheData = shouldCompress 
        ? { _compressed: true, data: this.compress(data) }
        : data;

      await consolidatedCache.set(key, cacheData, { ttl: ttlSeconds * 1000, tags: ['enhanced_cache'] });

      if (shouldCompress) {
        this.cacheMetrics.compressionSavings += size - Buffer.byteLength(JSON.stringify(cacheData), 'utf8');
      }
    } catch (error) {
      console.error('L2 cache set error:', error);
    }
  }

  /**
   * Shared Cache operations (for similar requests)
   */
  private async getFromSharedCache<T>(key: string, tags: string[]): Promise<T | null> {
    try {
      // Generate shared cache keys based on tags
      const sharedKeys = this.generateSharedCacheKeys(key, tags);
      
      for (const sharedKey of sharedKeys) {
        const cached = await consolidatedCache.get<T>(sharedKey);
        if (cached) {
          return cached;
        }
      }

      return null;
    } catch (error) {
      console.error('Shared cache get error:', error);
      return null;
    }
  }

  private async setInSharedCache<T>(key: string, data: T, tags: string[]): Promise<void> {
    try {
      const sharedKeys = this.generateSharedCacheKeys(key, tags);
      
      for (const sharedKey of sharedKeys) {
        await consolidatedCache.set(sharedKey, data, { ttl: this.config.sharedCacheTTL * 1000, tags: ['shared_cache'] });
      }
    } catch (error) {
      console.error('Shared cache set error:', error);
    }
  }

  /**
   * Generate shared cache keys for similar requests
   */
  private generateSharedCacheKeys(key: string, tags: string[]): string[] {
    const sharedKeys: string[] = [];

    // Generate keys based on tags
    if (tags.includes('career_path')) {
      const careerPathKey = key.replace(/user:\w+/, 'shared:career_path');
      sharedKeys.push(careerPathKey);
    }

    if (tags.includes('skill_analysis')) {
      const skillKey = key.replace(/user:\w+/, 'shared:skill_analysis');
      sharedKeys.push(skillKey);
    }

    if (tags.includes('market_data')) {
      const marketKey = key.replace(/user:\w+/, 'shared:market_data');
      sharedKeys.push(marketKey);
    }

    return sharedKeys;
  }

  /**
   * Cache warming functionality
   */
  private async initializeCacheWarming(): Promise<void> {
    if (!this.config.enableCacheWarming) return;

    try {
      // Warm cache with popular patterns
      for (const pattern of this.config.warmingPatterns) {
        await this.warmCachePattern(pattern);
      }
    } catch (error) {
      console.error('Cache warming initialization failed:', error);
    }
  }

  private async warmCachePattern(pattern: string): Promise<void> {
    try {
      // Implementation would depend on specific warming patterns
      // For now, just log the warming attempt
      console.log(`Warming cache for pattern: ${pattern}`);
    } catch (error) {
      console.error(`Cache warming failed for pattern ${pattern}:`, error);
    }
  }

  /**
   * Utility methods
   */
  private compress<T>(data: T): string {
    try {
      return compress(JSON.stringify(data));
    } catch (error) {
      console.error('Compression failed:', error);
      return JSON.stringify(data);
    }
  }

  private decompress<T>(compressedData: string): T {
    try {
      return JSON.parse(decompress(compressedData));
    } catch (error) {
      console.error('Decompression failed:', error);
      return compressedData as any;
    }
  }

  private isSharedCacheCandidate(key: string, tags: string[]): boolean {
    return tags.some(tag => ['career_path', 'market_data', 'popular_skills'].includes(tag));
  }

  private evictLRUFromL1Cache(): void {
    let lruKey = '';
    let lruTimestamp = Date.now();
    let lruHits = Infinity;

    Array.from(this.l1Cache.entries()).forEach(([key, entry]) => {
      if (entry.timestamp < lruTimestamp || (entry.timestamp === lruTimestamp && entry.hits < lruHits)) {
        lruKey = key;
        lruTimestamp = entry.timestamp;
        lruHits = entry.hits;
      }
    });

    if (lruKey) {
      this.l1Cache.delete(lruKey);
    }
  }

  private cleanupL1Cache(): void {
    const maxAge = 300000; // 5 minutes
    const now = Date.now();

    Array.from(this.l1Cache.entries()).forEach(([key, entry]) => {
      if (now - entry.timestamp > maxAge) {
        this.l1Cache.delete(key);
      }
    });
  }

  private updateAverageResponseTime(responseTime: number): void {
    const totalTime = this.cacheMetrics.averageResponseTime * (this.cacheMetrics.totalRequests - 1);
    this.cacheMetrics.averageResponseTime = (totalTime + responseTime) / this.cacheMetrics.totalRequests;
  }

  /**
   * Cache invalidation
   */
  async invalidate(pattern: string): Promise<void> {
    try {
      // Invalidate L1 cache
      Array.from(this.l1Cache.keys()).forEach(key => {
        if (key.includes(pattern)) {
          this.l1Cache.delete(key);
        }
      });

      // Invalidate L2 cache (implementation depends on cache service capabilities)
      // For now, we'll just log the invalidation
      console.log(`Cache invalidated for pattern: ${pattern}`);
    } catch (error) {
      console.error('Cache invalidation failed:', error);
    }
  }

  /**
   * Get cache metrics
   */
  getMetrics(): CacheMetrics & {
    l1CacheSize: number;
    l1HitRate: number;
    l2HitRate: number;
    overallHitRate: number;
    compressionRatio: number;
  } {
    const totalHits = this.cacheMetrics.l1Hits + this.cacheMetrics.l2Hits + this.cacheMetrics.sharedCacheHits;
    const totalRequests = this.cacheMetrics.totalRequests;

    return {
      ...this.cacheMetrics,
      l1CacheSize: this.l1Cache.size,
      l1HitRate: totalRequests > 0 ? (this.cacheMetrics.l1Hits / totalRequests) * 100 : 0,
      l2HitRate: totalRequests > 0 ? (this.cacheMetrics.l2Hits / totalRequests) * 100 : 0,
      overallHitRate: totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0,
      compressionRatio: this.cacheMetrics.compressionSavings > 0 ? 
        (this.cacheMetrics.compressionSavings / (this.cacheMetrics.compressionSavings + 1000)) * 100 : 0,
    };
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const metrics = this.getMetrics();
      const l2Healthy = await consolidatedCache.healthCheck();
      
      return l2Healthy && 
             metrics.l1CacheSize < this.config.l1CacheSize * 1.1 && 
             metrics.overallHitRate > 30; // At least 30% hit rate
    } catch (error) {
      console.error('Enhanced cache service health check failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const enhancedCacheService = EnhancedCacheService.getInstance();
