/**
 * Optimized Database Service
 * Leverages new composite indexes for maximum query performance
 * Part of Phase 2 Database Query Optimization
 */

import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

interface QueryPerformanceMetrics {
  queryName: string;
  executionTime: number;
  rowsReturned: number;
  indexesUsed: string[];
  cached: boolean;
}

class OptimizedDatabaseService {
  private performanceMetrics: QueryPerformanceMetrics[] = [];

  /**
   * Optimized user skill assessments query
   * Uses composite index: [userId, isActive, assessmentDate]
   */
  async getUserSkillAssessmentsOptimized(
    userId: string,
    limit: number = 50,
    includeInactive: boolean = false
  ) {
    const startTime = Date.now();
    
    try {
      const whereClause: Prisma.SkillAssessmentWhereInput = {
        userId,
        ...(includeInactive ? {} : { isActive: true }),
      };

      const assessments = await prisma.skillAssessment.findMany({
        where: whereClause,
        select: {
          id: true,
          skillId: true,
          selfRating: true,
          confidenceLevel: true,
          assessmentDate: true,
          assessmentType: true,
          skill: {
            select: {
              id: true,
              name: true,
              category: true,
            },
          },
        },
        orderBy: {
          assessmentDate: 'desc',
        },
        take: limit,
      });

      const executionTime = Date.now() - startTime;
      this.recordPerformanceMetrics({
        queryName: 'getUserSkillAssessmentsOptimized',
        executionTime,
        rowsReturned: assessments.length,
        indexesUsed: ['userId_isActive_assessmentDate'],
        cached: false,
      });

      return assessments.map(assessment => ({
        skillId: assessment.skillId,
        skillName: assessment.skill.name,
        skillCategory: assessment.skill.category,
        selfRating: assessment.selfRating,
        confidenceLevel: assessment.confidenceLevel,
        lastAssessed: assessment.assessmentDate,
        assessmentType: assessment.assessmentType,
      }));
    } catch (error) {
      console.error('Error in getUserSkillAssessmentsOptimized:', error);
      throw error;
    }
  }

  /**
   * Optimized career assessment query
   * Uses composite index: [userId, status, completedAt]
   */
  async getCareerAssessmentOptimized(
    userId: string,
    status: 'COMPLETED' | 'IN_PROGRESS' = 'COMPLETED'
  ) {
    const startTime = Date.now();
    
    try {
      const assessment = await prisma.assessment.findFirst({
        where: {
          userId,
          status,
        },
        select: {
          id: true,
          status: true,
          completedAt: true,
          responses: {
            select: {
              questionKey: true,
              answerValue: true,
            },
          },
        },
        orderBy: {
          completedAt: 'desc',
        },
      });

      const executionTime = Date.now() - startTime;
      this.recordPerformanceMetrics({
        queryName: 'getCareerAssessmentOptimized',
        executionTime,
        rowsReturned: assessment ? 1 : 0,
        indexesUsed: ['userId_status_completedAt'],
        cached: false,
      });

      return assessment;
    } catch (error) {
      console.error('Error in getCareerAssessmentOptimized:', error);
      throw error;
    }
  }

  /**
   * Optimized career path search
   * Uses composite index: [name, isActive] and [slug, isActive]
   */
  async findCareerPathOptimized(
    searchTerm: string,
    searchType: 'name' | 'slug' | 'both' = 'both'
  ) {
    const startTime = Date.now();
    
    try {
      let whereClause: Prisma.CareerPathWhereInput;

      if (searchType === 'name') {
        whereClause = {
          name: { contains: searchTerm, mode: 'insensitive' },
          isActive: true,
        };
      } else if (searchType === 'slug') {
        whereClause = {
          slug: searchTerm.toLowerCase().replace(/\s+/g, '-'),
          isActive: true,
        };
      } else {
        whereClause = {
          OR: [
            { name: { contains: searchTerm, mode: 'insensitive' }, isActive: true },
            { slug: searchTerm.toLowerCase().replace(/\s+/g, '-'), isActive: true },
          ],
        };
      }

      const careerPath = await prisma.careerPath.findFirst({
        where: whereClause,
        select: {
          id: true,
          name: true,
          slug: true,
          overview: true,
          pros: true,
          cons: true,
          actionableSteps: true,
          isActive: true,
        },
      });

      const executionTime = Date.now() - startTime;
      this.recordPerformanceMetrics({
        queryName: 'findCareerPathOptimized',
        executionTime,
        rowsReturned: careerPath ? 1 : 0,
        indexesUsed: ['name_isActive', 'slug_isActive'],
        cached: false,
      });

      return careerPath;
    } catch (error) {
      console.error('Error in findCareerPathOptimized:', error);
      throw error;
    }
  }

  /**
   * Optimized skill gap analysis query
   * Uses composite index: [userId, status, lastUpdated]
   */
  async getUserSkillGapAnalysisOptimized(
    userId: string,
    status: 'ACTIVE' | 'COMPLETED' | 'EXPIRED' = 'ACTIVE',
    limit: number = 10
  ) {
    const startTime = Date.now();
    
    try {
      const analyses = await prisma.skillGapAnalysis.findMany({
        where: {
          userId,
          status,
        },
        select: {
          id: true,
          targetCareerPathName: true,
          experienceLevel: true,
          timeframe: true,
          analysisData: true,
          skillGaps: true,
          learningPlan: true,
          completionPercentage: true,
          lastUpdated: true,
          createdAt: true,
        },
        orderBy: {
          lastUpdated: 'desc',
        },
        take: limit,
      });

      const executionTime = Date.now() - startTime;
      this.recordPerformanceMetrics({
        queryName: 'getUserSkillGapAnalysisOptimized',
        executionTime,
        rowsReturned: analyses.length,
        indexesUsed: ['userId_status_lastUpdated'],
        cached: false,
      });

      return analyses;
    } catch (error) {
      console.error('Error in getUserSkillGapAnalysisOptimized:', error);
      throw error;
    }
  }

  /**
   * Optimized market data query
   * Uses composite index: [isActive, dataDate, demandLevel]
   */
  async getSkillMarketDataOptimized(
    skillIds: string[],
    region: string = 'GLOBAL',
    limit: number = 100
  ) {
    const startTime = Date.now();
    
    try {
      const marketData = await prisma.skillMarketData.findMany({
        where: {
          skillId: { in: skillIds },
          region,
          isActive: true,
        },
        select: {
          skillId: true,
          demandLevel: true,
          averageSalaryImpact: true,
          jobPostingsCount: true,
          growthTrend: true,
          dataDate: true,
          skill: {
            select: {
              name: true,
              category: true,
            },
          },
        },
        orderBy: {
          dataDate: 'desc',
        },
        take: limit,
      });

      const executionTime = Date.now() - startTime;
      this.recordPerformanceMetrics({
        queryName: 'getSkillMarketDataOptimized',
        executionTime,
        rowsReturned: marketData.length,
        indexesUsed: ['isActive_dataDate_demandLevel'],
        cached: false,
      });

      return marketData;
    } catch (error) {
      console.error('Error in getSkillMarketDataOptimized:', error);
      throw error;
    }
  }

  /**
   * Record performance metrics for monitoring
   */
  private recordPerformanceMetrics(metrics: QueryPerformanceMetrics): void {
    this.performanceMetrics.push(metrics);
    
    // Keep only last 1000 metrics
    if (this.performanceMetrics.length > 1000) {
      this.performanceMetrics = this.performanceMetrics.slice(-1000);
    }

    // Log slow queries (>500ms)
    if (metrics.executionTime > 500) {
      console.warn(`Slow query detected: ${metrics.queryName} took ${metrics.executionTime}ms`);
    }
  }

  /**
   * Get performance metrics summary
   */
  getPerformanceMetrics(): {
    totalQueries: number;
    averageExecutionTime: number;
    slowQueries: number;
    queryBreakdown: Record<string, { count: number; avgTime: number }>;
  } {
    const totalQueries = this.performanceMetrics.length;
    const totalTime = this.performanceMetrics.reduce((sum, m) => sum + m.executionTime, 0);
    const averageExecutionTime = totalQueries > 0 ? totalTime / totalQueries : 0;
    const slowQueries = this.performanceMetrics.filter(m => m.executionTime > 500).length;

    const queryBreakdown: Record<string, { count: number; avgTime: number }> = {};
    this.performanceMetrics.forEach(metric => {
      if (!queryBreakdown[metric.queryName]) {
        queryBreakdown[metric.queryName] = { count: 0, avgTime: 0 };
      }
      queryBreakdown[metric.queryName].count++;
      queryBreakdown[metric.queryName].avgTime = 
        (queryBreakdown[metric.queryName].avgTime * (queryBreakdown[metric.queryName].count - 1) + metric.executionTime) / 
        queryBreakdown[metric.queryName].count;
    });

    return {
      totalQueries,
      averageExecutionTime,
      slowQueries,
      queryBreakdown,
    };
  }
}

export const optimizedDatabaseService = new OptimizedDatabaseService();
