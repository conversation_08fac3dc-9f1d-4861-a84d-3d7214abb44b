/**
 * Prisma Select Optimizer
 * Provides advanced selective loading patterns and query optimization utilities
 * Part of Phase 2B: Advanced Query Optimization
 */

import { Prisma } from '@prisma/client';

export interface SelectOptimizationConfig {
  includeRelations?: boolean;
  includeMetadata?: boolean;
  includeCounts?: boolean;
  maxDepth?: number;
  fields?: string[];
  excludeFields?: string[];
}

export interface OptimizedSelectResult<T> {
  select: any;
  include?: any;
  performance: {
    estimatedComplexity: 'low' | 'medium' | 'high';
    relationDepth: number;
    fieldCount: number;
    recommendations: string[];
  };
}

class PrismaSelectOptimizer {
  /**
   * Optimized select patterns for common models
   */
  private readonly OPTIMIZED_SELECTS = {
    // User profile with minimal data
    userMinimal: {
      id: true,
      email: true,
      name: true,
      createdAt: true
    },

    // User profile with extended data
    userExtended: {
      id: true,
      email: true,
      name: true,
      createdAt: true,
      updatedAt: true,
      profile: {
        select: {
          profilePictureUrl: true,
          currentCareerPath: true,
          progressLevel: true,
          forumReputation: true
        }
      }
    },

    // Career path minimal
    careerPathMinimal: {
      id: true,
      name: true,
      slug: true,
      overview: true,
      isActive: true
    },

    // Career path with relations
    careerPathWithRelations: {
      id: true,
      name: true,
      slug: true,
      overview: true,
      pros: true,
      cons: true,
      actionableSteps: true,
      isActive: true,
      _count: {
        select: {
          relatedSkills: true,
          learningPaths: true,
          learningResources: true
        }
      }
    },

    // Learning resource minimal
    learningResourceMinimal: {
      id: true,
      title: true,
      type: true,
      category: true,
      skillLevel: true,
      url: true,
      isActive: true
    },

    // Learning resource with metadata
    learningResourceWithMetadata: {
      id: true,
      title: true,
      description: true,
      type: true,
      category: true,
      skillLevel: true,
      url: true,
      author: true,
      duration: true,
      cost: true,
      format: true,
      isActive: true,
      createdAt: true,
      updatedAt: true
    },

    // Skill minimal
    skillMinimal: {
      id: true,
      name: true,
      category: true,
      description: true
    },

    // Forum post minimal
    forumPostMinimal: {
      id: true,
      title: true,
      content: true,
      createdAt: true,
      isPinned: true,
      isLocked: true,
      author: {
        select: {
          id: true,
          name: true,
          email: true
        }
      },
      _count: {
        select: {
          replies: true,
          reactions: true
        }
      }
    }
  };

  /**
   * Get optimized select for a specific model and use case
   */
  getOptimizedSelect<T extends keyof typeof this.OPTIMIZED_SELECTS>(
    model: T,
    config?: SelectOptimizationConfig
  ): OptimizedSelectResult<any> {
    const baseSelect = this.OPTIMIZED_SELECTS[model];
    
    if (!config) {
      return {
        select: baseSelect,
        performance: this.analyzeSelectPerformance(baseSelect)
      };
    }

    let optimizedSelect = { ...baseSelect };

    // Apply field filtering
    if (config.fields && config.fields.length > 0) {
      optimizedSelect = this.filterSelectFields(optimizedSelect, config.fields);
    }

    if (config.excludeFields && config.excludeFields.length > 0) {
      optimizedSelect = this.excludeSelectFields(optimizedSelect, config.excludeFields);
    }

    // Handle relation inclusion
    if (!config.includeRelations) {
      optimizedSelect = this.removeRelations(optimizedSelect);
    }

    // Handle metadata inclusion
    if (!config.includeMetadata) {
      optimizedSelect = this.removeMetadataFields(optimizedSelect);
    }

    // Handle count inclusion
    if (!config.includeCounts) {
      optimizedSelect = this.removeCounts(optimizedSelect);
    }

    return {
      select: optimizedSelect,
      performance: this.analyzeSelectPerformance(optimizedSelect)
    };
  }

  /**
   * Create dynamic select based on requested fields
   */
  createDynamicSelect(
    baseFields: string[],
    relations?: Record<string, any>,
    config?: SelectOptimizationConfig
  ): OptimizedSelectResult<any> {
    const select: any = {};

    // Add base fields
    baseFields.forEach(field => {
      select[field] = true;
    });

    // Add relations if specified
    if (relations && config?.includeRelations !== false) {
      Object.entries(relations).forEach(([key, value]) => {
        if (config?.maxDepth && this.getRelationDepth(value) > config.maxDepth) {
          // Skip deep relations
          return;
        }
        select[key] = value;
      });
    }

    return {
      select,
      performance: this.analyzeSelectPerformance(select)
    };
  }

  /**
   * Optimize existing select statement
   */
  optimizeExistingSelect(
    existingSelect: any,
    config?: SelectOptimizationConfig
  ): OptimizedSelectResult<any> {
    let optimized = { ...existingSelect };

    // Remove unnecessary fields based on config
    if (config?.excludeFields) {
      optimized = this.excludeSelectFields(optimized, config.excludeFields);
    }

    // Limit relation depth
    if (config?.maxDepth) {
      optimized = this.limitRelationDepth(optimized, config.maxDepth);
    }

    // Remove relations if not needed
    if (config?.includeRelations === false) {
      optimized = this.removeRelations(optimized);
    }

    return {
      select: optimized,
      performance: this.analyzeSelectPerformance(optimized)
    };
  }

  /**
   * Create select for pagination scenarios
   */
  createPaginationSelect(
    baseSelect: any,
    includeTotal: boolean = true
  ): { select: any; countSelect?: any } {
    const optimizedSelect = this.removeUnnecessaryFieldsForPagination(baseSelect);
    
    const result: any = { select: optimizedSelect };
    
    if (includeTotal) {
      result.countSelect = this.createCountOnlySelect(baseSelect);
    }

    return result;
  }

  /**
   * Analyze select performance characteristics
   */
  private analyzeSelectPerformance(select: any): {
    estimatedComplexity: 'low' | 'medium' | 'high';
    relationDepth: number;
    fieldCount: number;
    recommendations: string[];
  } {
    const fieldCount = this.countFields(select);
    const relationDepth = this.getRelationDepth(select);
    const hasComplexRelations = this.hasComplexRelations(select);

    let complexity: 'low' | 'medium' | 'high' = 'low';
    const recommendations: string[] = [];

    // Determine complexity
    if (fieldCount > 50 || relationDepth > 3 || hasComplexRelations) {
      complexity = 'high';
      recommendations.push('Consider reducing field count or relation depth');
    } else if (fieldCount > 20 || relationDepth > 2) {
      complexity = 'medium';
      recommendations.push('Monitor query performance');
    }

    // Specific recommendations
    if (relationDepth > 3) {
      recommendations.push('Deep relations detected. Consider separate queries or caching');
    }

    if (fieldCount > 30) {
      recommendations.push('High field count. Consider using specific field selection');
    }

    if (this.hasCountsWithoutLimits(select)) {
      recommendations.push('Count operations without limits detected. Consider pagination');
    }

    return {
      estimatedComplexity: complexity,
      relationDepth,
      fieldCount,
      recommendations
    };
  }

  /**
   * Helper methods for select manipulation
   */
  private filterSelectFields(select: any, fields: string[]): any {
    const filtered: any = {};
    fields.forEach(field => {
      if (select[field] !== undefined) {
        filtered[field] = select[field];
      }
    });
    return filtered;
  }

  private excludeSelectFields(select: any, excludeFields: string[]): any {
    const filtered = { ...select };
    excludeFields.forEach(field => {
      delete filtered[field];
    });
    return filtered;
  }

  private removeRelations(select: any): any {
    const filtered: any = {};
    Object.entries(select).forEach(([key, value]) => {
      if (typeof value === 'boolean' || (typeof value === 'object' && value !== null && (value as any).select)) {
        if (typeof value === 'boolean') {
          filtered[key] = value;
        }
      }
    });
    return filtered;
  }

  private removeMetadataFields(select: any): any {
    const metadataFields = ['createdAt', 'updatedAt', 'deletedAt'];
    return this.excludeSelectFields(select, metadataFields);
  }

  private removeCounts(select: any): any {
    const filtered = { ...select };
    delete filtered._count;
    return filtered;
  }

  private limitRelationDepth(select: any, maxDepth: number, currentDepth: number = 0): any {
    if (currentDepth >= maxDepth) {
      return {};
    }

    const limited: any = {};
    Object.entries(select).forEach(([key, value]) => {
      if (typeof value === 'boolean') {
        limited[key] = value;
      } else if (typeof value === 'object' && value !== null && (value as any).select) {
        limited[key] = {
          select: this.limitRelationDepth((value as any).select, maxDepth, currentDepth + 1)
        };
      } else {
        limited[key] = value;
      }
    });

    return limited;
  }

  private countFields(select: any): number {
    let count = 0;
    Object.entries(select).forEach(([key, value]) => {
      count++;
      if (typeof value === 'object' && value !== null && (value as any).select) {
        count += this.countFields((value as any).select);
      }
    });
    return count;
  }

  private getRelationDepth(select: any, currentDepth: number = 0): number {
    let maxDepth = currentDepth;
    Object.entries(select).forEach(([key, value]) => {
      if (typeof value === 'object' && value !== null && (value as any).select) {
        const depth = this.getRelationDepth((value as any).select, currentDepth + 1);
        maxDepth = Math.max(maxDepth, depth);
      }
    });
    return maxDepth;
  }

  private hasComplexRelations(select: any): boolean {
    return Object.values(select).some(value =>
      typeof value === 'object' &&
      value !== null &&
      ((value as any).include || ((value as any).select && Object.keys((value as any).select).length > 10))
    );
  }

  private hasCountsWithoutLimits(select: any): boolean {
    return Object.entries(select).some(([key, value]) => 
      key === '_count' || 
      (typeof value === 'object' && value !== null && this.hasCountsWithoutLimits(value))
    );
  }

  private removeUnnecessaryFieldsForPagination(select: any): any {
    // Remove heavy fields that aren't needed for pagination
    const heavyFields = ['content', 'description', 'actionableSteps'];
    return this.excludeSelectFields(select, heavyFields);
  }

  private createCountOnlySelect(select: any): any {
    // Create minimal select for count queries
    return { id: true };
  }
}

export const prismaSelectOptimizer = new PrismaSelectOptimizer();
export default prismaSelectOptimizer;
