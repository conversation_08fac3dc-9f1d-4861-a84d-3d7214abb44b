/**
 * Cache Invalidation Service
 * Handles intelligent cache invalidation based on data changes
 */

import { ConsolidatedCacheService } from './consolidated-cache-service';

export interface CacheInvalidationRule {
  pattern: string | RegExp;
  dependencies: string[];
  ttl?: number;
}

export interface InvalidationEvent {
  type: 'user_update' | 'skill_update' | 'profile_update' | 'analysis_update' | 'custom';
  userId?: string;
  entityId?: string;
  entityType?: string;
  metadata?: Record<string, any>;
}

/**
 * Intelligent cache invalidation service
 */
export class CacheInvalidationService {
  private cacheService: ConsolidatedCacheService;
  private invalidationRules: Map<string, CacheInvalidationRule[]>;

  constructor(cacheService?: ConsolidatedCacheService) {
    this.cacheService = cacheService || new ConsolidatedCacheService();
    this.invalidationRules = new Map();
    this.initializeDefaultRules();
  }

  /**
   * Initialize default invalidation rules
   */
  private initializeDefaultRules(): void {
    // User profile updates should invalidate related caches
    this.addRule('user_update', [
      {
        pattern: /^user:profile:\w+$/,
        dependencies: ['user_skills', 'skill_gap_analysis', 'recommendations']
      },
      {
        pattern: /^user:skills:\w+$/,
        dependencies: ['skill_gap_analysis', 'learning_recommendations']
      }
    ]);

    // Skill updates should invalidate skill gap analyses
    this.addRule('skill_update', [
      {
        pattern: /^skill_gap_analysis:\w+$/,
        dependencies: ['user_recommendations', 'learning_paths']
      },
      {
        pattern: /^user:skills:\w+$/,
        dependencies: ['skill_assessments', 'progress_tracking']
      }
    ]);

    // Profile updates should invalidate user-related caches
    this.addRule('profile_update', [
      {
        pattern: /^user:profile:\w+$/,
        dependencies: ['user_dashboard', 'progress_summary']
      }
    ]);

    // Analysis updates should invalidate recommendation caches
    this.addRule('analysis_update', [
      {
        pattern: /^skill_gap_analysis:\w+$/,
        dependencies: ['recommendations', 'learning_plan']
      }
    ]);
  }

  /**
   * Add invalidation rule for a specific event type
   */
  addRule(eventType: string, rules: CacheInvalidationRule[]): void {
    const existingRules = this.invalidationRules.get(eventType) || [];
    this.invalidationRules.set(eventType, [...existingRules, ...rules]);
  }

  /**
   * Invalidate caches based on an event
   */
  async invalidateByEvent(event: InvalidationEvent): Promise<void> {
    const rules = this.invalidationRules.get(event.type);
    if (!rules) {
      return;
    }

    const keysToInvalidate = new Set<string>();

    for (const rule of rules) {
      // Generate cache keys based on the event
      const cacheKeys = this.generateCacheKeys(event, rule);
      cacheKeys.forEach(key => keysToInvalidate.add(key));

      // Add dependency keys
      for (const dependency of rule.dependencies) {
        const dependencyKeys = this.generateDependencyKeys(event, dependency);
        dependencyKeys.forEach(key => keysToInvalidate.add(key));
      }
    }

    // Invalidate all identified keys
    await this.invalidateKeys(Array.from(keysToInvalidate));
  }

  /**
   * Generate cache keys based on event and rule
   */
  private generateCacheKeys(event: InvalidationEvent, rule: CacheInvalidationRule): string[] {
    const keys: string[] = [];

    if (typeof rule.pattern === 'string') {
      // Simple string pattern
      if (event.userId) {
        keys.push(rule.pattern.replace(/\w+$/, event.userId));
      }
    } else {
      // RegExp pattern - need to find matching keys
      // This would require scanning existing cache keys, which is expensive
      // For now, we'll generate common patterns
      if (event.userId) {
        const commonPatterns = [
          `user:profile:${event.userId}`,
          `user:skills:${event.userId}`,
          `skill_gap_analysis:${event.userId}`,
          `user:recommendations:${event.userId}`,
          `user:dashboard:${event.userId}`,
          `progress_summary:${event.userId}`
        ];

        keys.push(...commonPatterns.filter(key =>
          typeof rule.pattern === 'string' ?
            key.includes(rule.pattern) :
            rule.pattern.test(key)
        ));
      }
    }

    return keys;
  }

  /**
   * Generate dependency keys based on event
   */
  private generateDependencyKeys(event: InvalidationEvent, dependency: string): string[] {
    const keys: string[] = [];

    if (event.userId) {
      switch (dependency) {
        case 'user_skills':
          keys.push(`user:skills:${event.userId}`);
          break;
        case 'skill_gap_analysis':
          keys.push(`skill_gap_analysis:${event.userId}`);
          break;
        case 'recommendations':
          keys.push(`user:recommendations:${event.userId}`);
          break;
        case 'learning_recommendations':
          keys.push(`learning:recommendations:${event.userId}`);
          break;
        case 'learning_paths':
          keys.push(`learning:paths:${event.userId}`);
          break;
        case 'skill_assessments':
          keys.push(`assessments:${event.userId}`);
          break;
        case 'progress_tracking':
          keys.push(`progress:${event.userId}`);
          break;
        case 'user_dashboard':
          keys.push(`dashboard:${event.userId}`);
          break;
        case 'progress_summary':
          keys.push(`progress:summary:${event.userId}`);
          break;
        case 'learning_plan':
          keys.push(`learning:plan:${event.userId}`);
          break;
        default:
          // Custom dependency
          keys.push(`${dependency}:${event.userId}`);
      }
    }

    return keys;
  }

  /**
   * Invalidate specific cache keys
   */
  private async invalidateKeys(keys: string[]): Promise<void> {
    const promises = keys.map(key => this.cacheService.delete(key));
    await Promise.all(promises);
  }

  /**
   * Invalidate user-related caches
   */
  async invalidateUserCaches(userId: string): Promise<void> {
    await this.invalidateByEvent({
      type: 'user_update',
      userId
    });
  }

  /**
   * Invalidate skill-related caches
   */
  async invalidateSkillCaches(userId: string, skillId?: string): Promise<void> {
    await this.invalidateByEvent({
      type: 'skill_update',
      userId,
      entityId: skillId,
      entityType: 'skill'
    });
  }

  /**
   * Invalidate profile-related caches
   */
  async invalidateProfileCaches(userId: string): Promise<void> {
    await this.invalidateByEvent({
      type: 'profile_update',
      userId
    });
  }

  /**
   * Invalidate analysis-related caches
   */
  async invalidateAnalysisCaches(userId: string, analysisId?: string): Promise<void> {
    await this.invalidateByEvent({
      type: 'analysis_update',
      userId,
      entityId: analysisId,
      entityType: 'analysis'
    });
  }

  /**
   * Invalidate all caches for a user
   */
  async invalidateAllUserCaches(userId: string): Promise<void> {
    const userCachePatterns = [
      `user:*:${userId}`,
      `skill_gap_analysis:${userId}`,
      `recommendations:${userId}`,
      `learning:*:${userId}`,
      `assessments:${userId}`,
      `progress:*:${userId}`,
      `dashboard:${userId}`
    ];

    // Since we can't use wildcards directly, we'll invalidate known patterns
    const keysToInvalidate = [
      `user:profile:${userId}`,
      `user:skills:${userId}`,
      `skill_gap_analysis:${userId}`,
      `user:recommendations:${userId}`,
      `learning:recommendations:${userId}`,
      `learning:paths:${userId}`,
      `learning:plan:${userId}`,
      `assessments:${userId}`,
      `progress:${userId}`,
      `progress:summary:${userId}`,
      `dashboard:${userId}`
    ];

    await this.invalidateKeys(keysToInvalidate);
  }

  /**
   * Smart invalidation based on data changes
   */
  async smartInvalidate(changes: {
    table: string;
    operation: 'INSERT' | 'UPDATE' | 'DELETE';
    userId?: string;
    data?: Record<string, any>;
  }): Promise<void> {
    const { table, operation, userId, data } = changes;

    if (!userId) return;

    switch (table) {
      case 'User':
        await this.invalidateUserCaches(userId);
        break;
      
      case 'Profile':
        await this.invalidateProfileCaches(userId);
        break;
      
      case 'SkillAssessment':
        await this.invalidateSkillCaches(userId, data?.skillId);
        break;
      
      case 'SkillGapAnalysis':
        await this.invalidateAnalysisCaches(userId, data?.id);
        break;
      
      default:
        // For unknown tables, invalidate user caches as a safe fallback
        await this.invalidateUserCaches(userId);
    }
  }
}

// Singleton instance
export const cacheInvalidationService = new CacheInvalidationService();
