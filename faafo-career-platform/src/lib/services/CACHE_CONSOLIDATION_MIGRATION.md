# Cache Service Consolidation Migration Guide

## Overview

This migration consolidates multiple cache implementations into a single, unified `ConsolidatedCacheService` that eliminates code duplication and provides consistent caching behavior across the application.

## Services Being Consolidated

### 1. **cacheService.ts** - Enhanced cache with Redis/Memory fallback
- **Location**: `src/lib/services/cacheService.ts`
- **Features**: Redis with memory fallback, JSON operations, health checks
- **Usage**: Used by AI services and general caching

### 2. **redis-cache.ts** - Redis cache with memory fallback
- **Location**: `src/lib/redis-cache.ts`
- **Features**: Redis connection management, automatic fallback
- **Usage**: Used by AI optimization services

### 3. **unified-caching-service.ts** - Unified caching with memoization
- **Location**: `src/lib/unified-caching-service.ts`
- **Features**: Tag-based invalidation, memoization, LRU eviction
- **Usage**: Static caching utilities

### 4. **advanced-request-deduplication.ts** - Request deduplication
- **Location**: `src/lib/advanced-request-deduplication.ts`
- **Features**: Semantic similarity, cross-user deduplication, predictive warming
- **Usage**: AI service request optimization

### 5. **request-optimizer.ts** - Request optimization
- **Location**: `src/lib/request-optimizer.ts`
- **Features**: Request batching, deduplication, priority queuing
- **Usage**: AI service performance optimization

## Migration Strategy

### Phase 1: Update Import Statements

**Before:**
```typescript
import { cacheService } from '@/lib/services/cacheService';
import { RedisCache } from '@/lib/redis-cache';
import { UnifiedCachingService } from '@/lib/unified-caching-service';
import { AdvancedRequestDeduplicationService } from '@/lib/advanced-request-deduplication';
import { RequestOptimizer } from '@/lib/request-optimizer';
```

**After:**
```typescript
import { consolidatedCache } from '@/lib/services/consolidated-cache-service';
```

### Phase 2: Update Method Calls

#### Basic Cache Operations

**Before (cacheService):**
```typescript
await cacheService.get('key');
await cacheService.set('key', value, 3600);
await cacheService.getJSON<Type>('key');
await cacheService.setJSON('key', data, 3600);
```

**After:**
```typescript
await consolidatedCache.get<Type>('key');
await consolidatedCache.set('key', value, { ttl: 3600000 }); // TTL in milliseconds
```

#### Request Deduplication

**Before (AdvancedRequestDeduplicationService):**
```typescript
const deduplicationService = new AdvancedRequestDeduplicationService(config);
const result = await deduplicationService.deduplicateRequest(
  'key',
  requestFunction,
  { userId: 'user1', priority: 1 }
);
```

**After:**
```typescript
const result = await consolidatedCache.deduplicate(
  'key',
  requestFunction,
  { priority: 1 }
);
```

#### Memoization

**Before (UnifiedCachingService):**
```typescript
const memoizedFn = UnifiedCachingService.memoize(
  originalFunction,
  keyGenerator,
  { ttl: 3600000 }
);
```

**After:**
```typescript
const memoizedFn = consolidatedCache.memoize(
  originalFunction,
  keyGenerator,
  { ttl: 3600000 }
);
```

### Phase 3: Configuration Updates

**Before (Multiple Configurations):**
```typescript
// Different configs for different services
const redisConfig = { redisUrl: '...', timeout: 5000 };
const deduplicationConfig = { enableSemanticSimilarity: true };
const cacheConfig = { maxSize: 1000, defaultTTL: 3600 };
```

**After (Unified Configuration):**
```typescript
import { ConsolidatedCacheService } from '@/lib/services/consolidated-cache-service';

const cacheService = new ConsolidatedCacheService({
  // Redis configuration
  redisUrl: process.env.REDIS_URL,
  enableRedis: process.env.NODE_ENV === 'production',
  redisTimeout: 5000,
  
  // Memory cache configuration
  maxMemorySize: 1000,
  defaultTTL: 3600000, // 1 hour in milliseconds
  
  // Deduplication configuration
  enableDeduplication: true,
  deduplicationWindow: 30000, // 30 seconds
  
  // Performance configuration
  enableMetrics: true,
  enableCompression: true,
  compressionThreshold: 1024
});
```

## Files to Update

### High Priority (Core Services)

1. **src/lib/services/geminiService.ts**
   - Replace `cacheService` imports and usage
   - Update cache method calls

2. **src/lib/optimized-ai-service.ts**
   - Replace `advancedRequestDeduplication` usage
   - Update deduplication method calls

3. **src/lib/services/aiEnhancedAssessmentService.ts**
   - Replace cache service imports
   - Update caching patterns

### Medium Priority (API Routes)

4. **API routes using caching**
   - Update import statements
   - Replace cache method calls
   - Test functionality

### Low Priority (Utilities)

5. **Test files**
   - Update test imports
   - Adjust test expectations
   - Verify functionality

## Breaking Changes

### TTL Units
- **Before**: TTL in seconds
- **After**: TTL in milliseconds
- **Migration**: Multiply existing TTL values by 1000

### Method Signatures
- **Before**: Separate methods for JSON operations
- **After**: Generic methods with type parameters
- **Migration**: Use generic `get<T>()` and `set()` methods

### Configuration
- **Before**: Multiple service configurations
- **After**: Single unified configuration
- **Migration**: Consolidate configs into single object

## Testing Strategy

### 1. Unit Tests
```bash
npm test -- --testPathPattern="cache"
```

### 2. Integration Tests
```bash
npm test -- --testPathPattern="ai-optimization"
```

### 3. Build Verification
```bash
npm run build
```

### 4. Performance Testing
- Monitor cache hit rates
- Verify deduplication effectiveness
- Check memory usage

## Rollback Plan

If issues arise during migration:

1. **Revert import statements** to original services
2. **Restore original method calls**
3. **Keep consolidated service** for future migration
4. **Document issues** for resolution

## Benefits After Migration

### 1. **Reduced Code Duplication**
- Single cache implementation
- Consistent error handling
- Unified configuration

### 2. **Improved Performance**
- Better memory management
- Optimized Redis usage
- Enhanced deduplication

### 3. **Easier Maintenance**
- Single service to maintain
- Consistent API surface
- Centralized monitoring

### 4. **Better Testing**
- Single service to test
- Consistent behavior
- Easier mocking

## Timeline

- **Phase 1**: Update core services (1-2 hours)
- **Phase 2**: Update API routes (2-3 hours)
- **Phase 3**: Update tests and utilities (1-2 hours)
- **Phase 4**: Remove legacy services (30 minutes)

## Success Criteria

- [ ] All builds pass
- [ ] All tests pass
- [ ] Cache hit rates maintained or improved
- [ ] Memory usage optimized
- [ ] No performance regressions
- [ ] Legacy cache files removed
