/**
 * Service Interfaces
 * 
 * Defines common interfaces for services to reduce coupling and enable
 * dependency injection. This helps prevent circular dependencies by
 * establishing clear contracts between services.
 */

// Base service interface
export interface BaseService {
  readonly name: string;
  readonly version: string;
  healthCheck(): Promise<boolean>;
  getMetrics?(): Promise<Record<string, any>>;
}

// Cache service interface
export interface CacheServiceInterface extends BaseService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttlSeconds?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  mget<T>(keys: string[]): Promise<(T | null)[]>;
  mset<T>(keyValues: Record<string, T>, ttlSeconds?: number): Promise<void>;
}

// AI service interface
export interface AIServiceInterface extends BaseService {
  generateContent(prompt: string, options?: AIGenerationOptions): Promise<AIResponse>;
  analyzeSkills(data: SkillAnalysisInput): Promise<SkillAnalysisResult>;
  generateCareerRecommendations(data: CareerRecommendationInput): Promise<CareerRecommendation[]>;
  analyzeResume(resumeText: string): Promise<ResumeAnalysisResult>;
}

// Database service interface
export interface DatabaseServiceInterface extends BaseService {
  query<T>(sql: string, params?: any[]): Promise<T[]>;
  transaction<T>(callback: (tx: any) => Promise<T>): Promise<T>;
  getPerformanceMetrics(): Promise<QueryPerformanceMetrics[]>;
}

// Performance monitoring interface
export interface PerformanceMonitorInterface extends BaseService {
  startTimer(operation: string): string;
  endTimer(timerId: string): number;
  recordMetric(name: string, value: number, tags?: Record<string, string>): void;
  getMetrics(timeRange?: TimeRange): Promise<PerformanceMetrics>;
}

// Security service interface
export interface SecurityServiceInterface extends BaseService {
  validateInput(input: any, schema: ValidationSchema): ValidationResult;
  sanitizeInput(input: any): any;
  checkPermissions(userId: string, resource: string, action: string): Promise<boolean>;
  auditLog(event: SecurityEvent): Promise<void>;
}

// Common types used across interfaces
export interface AIGenerationOptions {
  maxTokens?: number;
  temperature?: number;
  timeout?: number;
  model?: string;
}

export interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
  cached?: boolean;
  metadata?: {
    model?: string;
    tokens?: number;
    responseTime?: number;
  };
}

export interface SkillAnalysisInput {
  userId: string;
  skills: string[];
  careerPath?: string;
  experience?: string;
}

export interface SkillAnalysisResult {
  skillGaps: SkillGap[];
  recommendations: LearningRecommendation[];
  careerFit: number;
  confidenceScore: number;
}

export interface SkillGap {
  skill: string;
  currentLevel: number;
  targetLevel: number;
  priority: 'high' | 'medium' | 'low';
  estimatedTimeToClose: string;
}

export interface LearningRecommendation {
  type: 'course' | 'certification' | 'project' | 'mentorship';
  title: string;
  description: string;
  provider?: string;
  duration?: string;
  cost?: string;
  priority: number;
}

export interface CareerRecommendationInput {
  userId: string;
  currentRole?: string;
  skills: string[];
  interests: string[];
  experience: string;
  location?: string;
}

export interface CareerRecommendation {
  careerPath: string;
  matchScore: number;
  reasoning: string;
  requiredSkills: string[];
  timeToTransition: string;
  salaryRange: string;
  growthPotential: string;
}

export interface ResumeAnalysisResult {
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
  skillsIdentified: string[];
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
  industryFit: string[];
  overallScore: number;
}

export interface QueryPerformanceMetrics {
  queryName: string;
  executionTime: number;
  rowsReturned: number;
  indexesUsed: string[];
  cached: boolean;
  timestamp: Date;
}

export interface TimeRange {
  start: Date;
  end: Date;
}

export interface PerformanceMetrics {
  responseTime: {
    avg: number;
    p95: number;
    p99: number;
  };
  throughput: number;
  errorRate: number;
  cacheHitRate?: number;
  memoryUsage?: number;
  cpuUsage?: number;
}

export interface ValidationSchema {
  type: string;
  properties?: Record<string, any>;
  required?: string[];
  additionalProperties?: boolean;
}

export interface ValidationResult {
  valid: boolean;
  errors?: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface SecurityEvent {
  userId?: string;
  action: string;
  resource: string;
  timestamp: Date;
  metadata?: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

// Service registry interface for dependency injection
export interface ServiceRegistry {
  register<T extends BaseService>(name: string, service: T): void;
  get<T extends BaseService>(name: string): T;
  has(name: string): boolean;
  list(): string[];
}

// Service factory interface
export interface ServiceFactory {
  createCacheService(): CacheServiceInterface;
  createAIService(): AIServiceInterface;
  createDatabaseService(): DatabaseServiceInterface;
  createPerformanceMonitor(): PerformanceMonitorInterface;
  createSecurityService(): SecurityServiceInterface;
}

// Configuration interfaces
export interface ServiceConfiguration {
  cache?: CacheConfiguration;
  ai?: AIConfiguration;
  database?: DatabaseConfiguration;
  performance?: PerformanceConfiguration;
  security?: SecurityConfiguration;
}

export interface CacheConfiguration {
  provider: 'redis' | 'memory' | 'hybrid';
  ttl: number;
  maxSize?: number;
  compression?: boolean;
}

export interface AIConfiguration {
  provider: 'gemini' | 'openai' | 'anthropic';
  model: string;
  apiKey: string;
  timeout: number;
  rateLimitPerMinute: number;
}

export interface DatabaseConfiguration {
  provider: 'postgresql' | 'mysql' | 'sqlite';
  connectionString: string;
  poolSize: number;
  timeout: number;
}

export interface PerformanceConfiguration {
  enableMetrics: boolean;
  metricsInterval: number;
  alertThresholds: Record<string, number>;
}

export interface SecurityConfiguration {
  enableAuditLog: boolean;
  encryptionKey: string;
  sessionTimeout: number;
  maxLoginAttempts: number;
}
