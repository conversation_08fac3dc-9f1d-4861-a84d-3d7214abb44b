/**
 * Security Service Interfaces
 * 
 * Core interfaces for security services to enable dependency injection
 * and reduce coupling between security components.
 */

export interface ValidationResult {
  isValid: boolean;
  errors?: string[];
  sanitizedInput?: any;
  warnings?: string[];
}

export interface SecurityScanResult {
  riskLevel: 'low' | 'medium' | 'high';
  threats: string[];
  blocked: boolean;
  sanitized?: string;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  retryAfter?: number;
}

export interface SecurityIncident {
  type: string;
  userId?: string;
  timestamp: Date;
  request: string;
  ipAddress?: string;
  userAgent?: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

export interface Session {
  user: {
    id: string;
    email: string;
    role?: string;
  };
  expires: string;
}

// Core Security Interfaces

export interface ISecurityValidator {
  validateInput(input: any, options?: ValidationOptions): Promise<ValidationResult>;
  securityScan(input: string): SecurityScanResult;
  sanitizeInput(input: any): any;
  validateSchema<T>(input: any, schema: any): ValidationResult;
}

export interface IRateLimiter {
  checkLimit(identifier: string, type?: string): Promise<RateLimitResult>;
  resetLimit(identifier: string): Promise<void>;
  incrementUsage(identifier: string): Promise<void>;
  getUsage(identifier: string): Promise<number>;
}

export interface ICSRFProtection {
  validateToken(token: string, session: any): boolean;
  generateToken(session?: any): string;
  extractToken(request: any): string | null;
  isCSRFRequired(request: any): boolean;
}

export interface IAuthProvider {
  getSession(request: any): Promise<Session | null>;
  validateSession(session: any): boolean;
  requireAuth(session: any): boolean;
  hasRole(session: any, role: string): boolean;
}

export interface ISecurityLogger {
  logIncident(incident: SecurityIncident): void;
  logValidationFailure(input: any, errors: string[]): void;
  logRateLimitExceeded(identifier: string, type: string): void;
  logCSRFFailure(request: any): void;
}

// Configuration Interfaces

export interface ValidationOptions {
  maxLength?: number;
  allowedTypes?: string[];
  strictMode?: boolean;
  sanitize?: boolean;
}

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

export interface CSRFConfig {
  secret: string;
  tokenLength: number;
  cookieName: string;
  headerName: string;
}

export interface SecurityConfig {
  rateLimits: Record<string, RateLimitConfig>;
  validation: {
    maxInputLength: number;
    maxArrayLength: number;
    enableSqlInjectionDetection: boolean;
    enableXssDetection: boolean;
  };
  csrf: CSRFConfig;
  auth: {
    sessionTimeout: number;
    requireHttps: boolean;
    secureCookies: boolean;
  };
}

// Security Middleware Options

export interface SecurityOptions {
  requireAuth?: boolean;
  requireCSRF?: boolean;
  rateLimitType?: string;
  validateInput?: boolean;
  allowedMethods?: string[];
  maxBodySize?: number;
  useEnhancedRateLimit?: boolean;
  useComprehensiveValidation?: boolean;
  enableSecureCache?: boolean;
  threatDetection?: boolean;
}

// Composite Security Service Interface

export interface ISecurityService {
  validator: ISecurityValidator;
  rateLimiter: IRateLimiter;
  csrfProtection: ICSRFProtection;
  authProvider: IAuthProvider;
  logger: ISecurityLogger;
  
  protect(request: any, handler: () => Promise<any>, options?: SecurityOptions): Promise<any>;
  secureAuth(request: any, handler: () => Promise<any>, options?: Partial<SecurityOptions>): Promise<any>;
  secureWrite(request: any, handler: () => Promise<any>, options?: Partial<SecurityOptions>): Promise<any>;
  secureRead(request: any, handler: () => Promise<any>, options?: Partial<SecurityOptions>): Promise<any>;
}

// Factory Interface

export interface ISecurityServiceFactory {
  createValidator(config?: any): ISecurityValidator;
  createRateLimiter(type: string, config?: RateLimitConfig): IRateLimiter;
  createCSRFProtection(config?: CSRFConfig): ICSRFProtection;
  createAuthProvider(): IAuthProvider;
  createLogger(): ISecurityLogger;
  createSecurityService(config?: SecurityConfig): ISecurityService;
}

// Error Types

export class SecurityError extends Error {
  constructor(
    message: string,
    public readonly type: string,
    public readonly severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'MEDIUM'
  ) {
    super(message);
    this.name = 'SecurityError';
  }
}

export class ValidationError extends SecurityError {
  constructor(message: string, public readonly field?: string) {
    super(message, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
  }
}

export class RateLimitError extends SecurityError {
  constructor(
    message: string,
    public readonly retryAfter: number,
    public readonly identifier: string
  ) {
    super(message, 'RATE_LIMIT_ERROR');
    this.name = 'RateLimitError';
  }
}

export class CSRFError extends SecurityError {
  constructor(message: string) {
    super(message, 'CSRF_ERROR', 'HIGH');
    this.name = 'CSRFError';
  }
}

export class AuthenticationError extends SecurityError {
  constructor(message: string) {
    super(message, 'AUTHENTICATION_ERROR', 'HIGH');
    this.name = 'AuthenticationError';
  }
}

// Type Guards

export function isValidationResult(obj: any): obj is ValidationResult {
  return obj && typeof obj.isValid === 'boolean';
}

export function isSecurityScanResult(obj: any): obj is SecurityScanResult {
  return obj && 
    typeof obj.riskLevel === 'string' && 
    Array.isArray(obj.threats) && 
    typeof obj.blocked === 'boolean';
}

export function isRateLimitResult(obj: any): obj is RateLimitResult {
  return obj && 
    typeof obj.allowed === 'boolean' && 
    typeof obj.remaining === 'number' && 
    obj.resetTime instanceof Date;
}

export function isSession(obj: any): obj is Session {
  return obj && 
    obj.user && 
    typeof obj.user.id === 'string' && 
    typeof obj.user.email === 'string' && 
    typeof obj.expires === 'string';
}

// Utility Types

export type SecurityMiddlewareHandler = (request: any) => Promise<any>;
export type SecurityValidationRule = (input: any) => ValidationResult;
export type SecurityEventHandler = (incident: SecurityIncident) => void;

// Constants

export const SECURITY_CONSTANTS = {
  DEFAULT_RATE_LIMIT: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100
  },
  DEFAULT_VALIDATION: {
    maxInputLength: 10000,
    maxArrayLength: 1000
  },
  CSRF_TOKEN_LENGTH: 32,
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000 // 24 hours
} as const;
