/**
 * Service Registry
 * 
 * Implements dependency injection container to reduce coupling between services
 * and prevent circular dependencies. Services register themselves and can
 * request dependencies through the registry instead of direct imports.
 */

import { 
  BaseService, 
  ServiceRegistry as IServiceRegistry,
  ServiceConfiguration 
} from '@/lib/interfaces/service-interfaces';

export class ServiceRegistry implements IServiceRegistry {
  private services = new Map<string, BaseService>();
  private singletons = new Map<string, BaseService>();
  private factories = new Map<string, () => BaseService>();
  private dependencies = new Map<string, string[]>();
  private configuration: ServiceConfiguration;

  constructor(configuration: ServiceConfiguration = {}) {
    this.configuration = configuration;
  }

  /**
   * Register a service instance
   */
  register<T extends BaseService>(name: string, service: T): void {
    if (this.services.has(name)) {
      throw new Error(`Service '${name}' is already registered`);
    }
    
    this.services.set(name, service);
    console.log(`[ServiceRegistry] Registered service: ${name}`);
  }

  /**
   * Register a service factory for lazy initialization
   */
  registerFactory<T extends BaseService>(
    name: string, 
    factory: () => T, 
    dependencies: string[] = []
  ): void {
    if (this.factories.has(name)) {
      throw new Error(`Service factory '${name}' is already registered`);
    }
    
    this.factories.set(name, factory);
    this.dependencies.set(name, dependencies);
    console.log(`[ServiceRegistry] Registered service factory: ${name} with dependencies: [${dependencies.join(', ')}]`);
  }

  /**
   * Register a singleton service factory
   */
  registerSingleton<T extends BaseService>(
    name: string, 
    factory: () => T, 
    dependencies: string[] = []
  ): void {
    this.registerFactory(name, factory, dependencies);
  }

  /**
   * Get a service instance
   */
  get<T extends BaseService>(name: string): T {
    // Check if already instantiated
    if (this.services.has(name)) {
      return this.services.get(name) as T;
    }

    // Check if singleton already exists
    if (this.singletons.has(name)) {
      return this.singletons.get(name) as T;
    }

    // Check if factory exists
    if (this.factories.has(name)) {
      const factory = this.factories.get(name)!;
      const dependencies = this.dependencies.get(name) || [];
      
      // Resolve dependencies first
      this.resolveDependencies(dependencies);
      
      // Create instance
      const instance = factory() as T;
      
      // Store as singleton
      this.singletons.set(name, instance);
      
      console.log(`[ServiceRegistry] Created service instance: ${name}`);
      return instance;
    }

    throw new Error(`Service '${name}' is not registered`);
  }

  /**
   * Check if a service is registered
   */
  has(name: string): boolean {
    return this.services.has(name) || 
           this.singletons.has(name) || 
           this.factories.has(name);
  }

  /**
   * List all registered services
   */
  list(): string[] {
    const allServices = new Set<string>();

    Array.from(this.services.keys()).forEach(name => allServices.add(name));
    Array.from(this.singletons.keys()).forEach(name => allServices.add(name));
    Array.from(this.factories.keys()).forEach(name => allServices.add(name));

    return Array.from(allServices).sort();
  }

  /**
   * Get service configuration
   */
  getConfiguration(): ServiceConfiguration {
    return { ...this.configuration };
  }

  /**
   * Update service configuration
   */
  updateConfiguration(config: Partial<ServiceConfiguration>): void {
    this.configuration = { ...this.configuration, ...config };
  }

  /**
   * Resolve dependencies for a service
   */
  private resolveDependencies(dependencies: string[]): void {
    for (const dep of dependencies) {
      if (!this.has(dep)) {
        throw new Error(`Dependency '${dep}' is not registered`);
      }
      
      // Ensure dependency is instantiated
      this.get(dep);
    }
  }

  /**
   * Check for circular dependencies
   */
  checkCircularDependencies(): string[] {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    const cycles: string[] = [];

    const dfs = (serviceName: string, path: string[]): void => {
      if (recursionStack.has(serviceName)) {
        const cycleStart = path.indexOf(serviceName);
        const cycle = path.slice(cycleStart).concat(serviceName);
        cycles.push(cycle.join(' -> '));
        return;
      }

      if (visited.has(serviceName)) {
        return;
      }

      visited.add(serviceName);
      recursionStack.add(serviceName);

      const dependencies = this.dependencies.get(serviceName) || [];
      for (const dep of dependencies) {
        dfs(dep, [...path, serviceName]);
      }

      recursionStack.delete(serviceName);
    };

    const serviceNames = Array.from(this.dependencies.keys());
    for (const serviceName of serviceNames) {
      if (!visited.has(serviceName)) {
        dfs(serviceName, []);
      }
    }

    return cycles;
  }

  /**
   * Validate service registry
   */
  validate(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check for circular dependencies
    const cycles = this.checkCircularDependencies();
    if (cycles.length > 0) {
      errors.push(`Circular dependencies detected: ${cycles.join(', ')}`);
    }

    // Check for missing dependencies
    const dependencyEntries = Array.from(this.dependencies.entries());
    for (const [serviceName, dependencies] of dependencyEntries) {
      for (const dep of dependencies) {
        if (!this.has(dep)) {
          errors.push(`Service '${serviceName}' depends on unregistered service '${dep}'`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Initialize all services
   */
  async initializeAll(): Promise<void> {
    console.log('[ServiceRegistry] Initializing all services...');
    
    const validation = this.validate();
    if (!validation.valid) {
      throw new Error(`Service registry validation failed: ${validation.errors.join(', ')}`);
    }

    // Initialize services in dependency order
    const initOrder = this.getInitializationOrder();
    
    for (const serviceName of initOrder) {
      try {
        const service = this.get(serviceName);
        
        // Perform health check if available
        if (service.healthCheck) {
          const healthy = await service.healthCheck();
          if (!healthy) {
            console.warn(`[ServiceRegistry] Service '${serviceName}' failed health check`);
          }
        }
        
        console.log(`[ServiceRegistry] Initialized service: ${serviceName}`);
      } catch (error) {
        console.error(`[ServiceRegistry] Failed to initialize service '${serviceName}':`, error);
        throw error;
      }
    }
    
    console.log('[ServiceRegistry] All services initialized successfully');
  }

  /**
   * Get initialization order based on dependencies
   */
  private getInitializationOrder(): string[] {
    const visited = new Set<string>();
    const order: string[] = [];

    const visit = (serviceName: string): void => {
      if (visited.has(serviceName)) {
        return;
      }

      visited.add(serviceName);

      // Visit dependencies first
      const dependencies = this.dependencies.get(serviceName) || [];
      for (const dep of dependencies) {
        visit(dep);
      }

      order.push(serviceName);
    };

    // Visit all services
    const serviceNames = Array.from(this.dependencies.keys());
    for (const serviceName of serviceNames) {
      visit(serviceName);
    }

    return order;
  }

  /**
   * Shutdown all services
   */
  async shutdown(): Promise<void> {
    console.log('[ServiceRegistry] Shutting down all services...');
    
    const services = [...Array.from(this.services.values()), ...Array.from(this.singletons.values())];
    
    for (const service of services) {
      try {
        // Call shutdown method if available
        if ('shutdown' in service && typeof service.shutdown === 'function') {
          await (service as any).shutdown();
        }
        
        console.log(`[ServiceRegistry] Shutdown service: ${service.name}`);
      } catch (error) {
        console.error(`[ServiceRegistry] Error shutting down service '${service.name}':`, error);
      }
    }
    
    // Clear all registrations
    this.services.clear();
    this.singletons.clear();
    this.factories.clear();
    this.dependencies.clear();
    
    console.log('[ServiceRegistry] All services shutdown complete');
  }
}

// Global service registry instance
export const serviceRegistry = new ServiceRegistry();

// Helper functions for common service registration patterns
export function registerService<T extends BaseService>(name: string, service: T): void {
  serviceRegistry.register(name, service);
}

export function registerServiceFactory<T extends BaseService>(
  name: string,
  factory: () => T,
  dependencies: string[] = []
): void {
  serviceRegistry.registerFactory(name, factory, dependencies);
}

export function getService<T extends BaseService>(name: string): T {
  return serviceRegistry.get<T>(name);
}

export function hasService(name: string): boolean {
  return serviceRegistry.has(name);
}

// Service initialization helper
export async function initializeServices(): Promise<void> {
  await serviceRegistry.initializeAll();
}

// Service validation helper
export function validateServices(): { valid: boolean; errors: string[] } {
  return serviceRegistry.validate();
}
