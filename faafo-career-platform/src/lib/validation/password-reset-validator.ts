/**
 * Password Reset Token Validation
 * Enhanced validation for password reset tokens with proper expiration checks
 */

import { CONFIG } from '@/lib/config';

export interface PasswordResetValidationResult {
  isValid: boolean;
  error?: string;
  isExpired?: boolean;
  timeRemaining?: number;
}

export interface PasswordResetTokenData {
  token: string;
  passwordResetToken?: string | null;
  passwordResetExpires?: Date | null;
  userId?: string;
}

/**
 * Enhanced password reset token validator
 */
export class PasswordResetValidator {
  
  /**
   * Validate password reset token with comprehensive checks
   */
  static validateToken(data: PasswordResetTokenData): PasswordResetValidationResult {
    // Check if token is provided
    if (!data.token || typeof data.token !== 'string') {
      return {
        isValid: false,
        error: 'Password reset token is required'
      };
    }

    // Check token format (should be UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(data.token)) {
      return {
        isValid: false,
        error: 'Invalid token format'
      };
    }

    // Check if user has a reset token
    if (!data.passwordResetToken) {
      return {
        isValid: false,
        error: 'No password reset token found for this user'
      };
    }

    // Check if token has expiration date
    if (!data.passwordResetExpires) {
      return {
        isValid: false,
        error: 'Password reset token has no expiration date'
      };
    }

    // Check if token is expired
    const now = new Date();
    const expiresAt = new Date(data.passwordResetExpires);
    
    if (expiresAt <= now) {
      const expiredMinutes = Math.floor((now.getTime() - expiresAt.getTime()) / (1000 * 60));
      return {
        isValid: false,
        error: `Password reset token expired ${expiredMinutes} minutes ago`,
        isExpired: true,
        timeRemaining: 0
      };
    }

    // Calculate time remaining
    const timeRemaining = expiresAt.getTime() - now.getTime();
    const minutesRemaining = Math.floor(timeRemaining / (1000 * 60));

    // Warn if token expires soon (less than 10 minutes)
    if (minutesRemaining < 10) {
      return {
        isValid: true,
        error: `Token expires in ${minutesRemaining} minutes`,
        timeRemaining: timeRemaining
      };
    }

    return {
      isValid: true,
      timeRemaining: timeRemaining
    };
  }

  /**
   * Validate token expiration against business rules
   */
  static validateTokenExpiration(expiresAt: Date): PasswordResetValidationResult {
    const now = new Date();
    const maxAllowedExpiry = new Date(now.getTime() + CONFIG.AUTH.PASSWORD_RESET_EXPIRY_MS);
    const minAllowedExpiry = new Date(now.getTime() + (5 * 60 * 1000)); // Minimum 5 minutes

    // Check if expiration is too far in the future
    if (expiresAt > maxAllowedExpiry) {
      return {
        isValid: false,
        error: `Token expiration exceeds maximum allowed time of ${CONFIG.AUTH.PASSWORD_RESET_EXPIRY_MS / (1000 * 60)} minutes`
      };
    }

    // Check if expiration is too soon
    if (expiresAt < minAllowedExpiry) {
      return {
        isValid: false,
        error: 'Token expiration is too soon (minimum 5 minutes required)'
      };
    }

    // Check if already expired
    if (expiresAt <= now) {
      return {
        isValid: false,
        error: 'Token expiration date is in the past',
        isExpired: true
      };
    }

    return {
      isValid: true
    };
  }

  /**
   * Generate secure expiration date for new tokens
   */
  static generateExpirationDate(): Date {
    return new Date(Date.now() + CONFIG.AUTH.PASSWORD_RESET_EXPIRY_MS);
  }

  /**
   * Check if a token should be considered stale (close to expiration)
   */
  static isTokenStale(expiresAt: Date): boolean {
    const now = new Date();
    const timeRemaining = expiresAt.getTime() - now.getTime();
    const staleThreshold = 10 * 60 * 1000; // 10 minutes

    return timeRemaining < staleThreshold;
  }

  /**
   * Validate password reset request frequency
   */
  static validateRequestFrequency(lastRequestTime?: Date): PasswordResetValidationResult {
    if (!lastRequestTime) {
      return { isValid: true };
    }

    const now = new Date();
    const timeSinceLastRequest = now.getTime() - lastRequestTime.getTime();
    const minimumInterval = 5 * 60 * 1000; // 5 minutes

    if (timeSinceLastRequest < minimumInterval) {
      const remainingTime = minimumInterval - timeSinceLastRequest;
      const remainingMinutes = Math.ceil(remainingTime / (1000 * 60));
      
      return {
        isValid: false,
        error: `Please wait ${remainingMinutes} minutes before requesting another password reset`
      };
    }

    return { isValid: true };
  }

  /**
   * Sanitize and validate token input
   */
  static sanitizeToken(token: string): string {
    if (!token || typeof token !== 'string') {
      return '';
    }

    // Remove any whitespace and convert to lowercase
    const sanitized = token.trim().toLowerCase();

    // Remove any non-UUID characters
    const cleanToken = sanitized.replace(/[^0-9a-f-]/g, '');

    return cleanToken;
  }

  /**
   * Comprehensive validation for password reset flow
   */
  static validatePasswordResetFlow(data: {
    token: string;
    newPassword: string;
    passwordResetToken?: string | null;
    passwordResetExpires?: Date | null;
    userId?: string;
  }): PasswordResetValidationResult {
    // Validate token
    const tokenValidation = this.validateToken(data);
    if (!tokenValidation.isValid) {
      return tokenValidation;
    }

    // Validate new password
    if (!data.newPassword || typeof data.newPassword !== 'string') {
      return {
        isValid: false,
        error: 'New password is required'
      };
    }

    if (data.newPassword.length < 8) {
      return {
        isValid: false,
        error: 'New password must be at least 8 characters long'
      };
    }

    if (data.newPassword.length > 128) {
      return {
        isValid: false,
        error: 'New password is too long (maximum 128 characters)'
      };
    }

    // Check password complexity
    const hasLowercase = /[a-z]/.test(data.newPassword);
    const hasUppercase = /[A-Z]/.test(data.newPassword);
    const hasNumber = /\d/.test(data.newPassword);
    const hasSpecialChar = /[@$!%*?&]/.test(data.newPassword);

    if (!hasLowercase || !hasUppercase || !hasNumber || !hasSpecialChar) {
      return {
        isValid: false,
        error: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
      };
    }

    return {
      isValid: true
    };
  }
}
