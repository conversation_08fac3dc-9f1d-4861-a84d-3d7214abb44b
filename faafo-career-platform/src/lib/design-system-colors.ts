/**
 * Design System Color Utilities
 * 
 * This file provides utilities to map semantic colors to design system variables,
 * replacing hardcoded Tailwind colors with consistent design system colors.
 * 
 * User preference: Consistent single color scheme, avoiding blue colors, 
 * black/dark styling to match overall app theme.
 */

/**
 * Semantic color mappings that replace hardcoded colors with design system variables
 */
export const semanticColors = {
  // Status colors using design system variables
  success: 'text-green-600 dark:text-green-400',
  error: 'text-destructive',
  warning: 'text-yellow-600 dark:text-yellow-400', 
  info: 'text-muted-foreground',
  
  // Background variants for status
  successBg: 'bg-green-50 dark:bg-green-950/20',
  errorBg: 'bg-destructive/10',
  warningBg: 'bg-yellow-50 dark:bg-yellow-950/20',
  infoBg: 'bg-muted',
  
  // Border variants for status
  successBorder: 'border-green-200 dark:border-green-800',
  errorBorder: 'border-destructive/20',
  warningBorder: 'border-yellow-200 dark:border-yellow-800',
  infoBorder: 'border-border',
  
  // Severity levels (replacing blue/red/orange hardcoded colors)
  severityLow: 'text-muted-foreground bg-muted',
  severityMedium: 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-950/20',
  severityHigh: 'text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-950/20',
  severityCritical: 'text-destructive bg-destructive/10',
  
  // Skill levels (neutral theme)
  beginner: 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-950/20',
  intermediate: 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-950/20',
  advanced: 'text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-950/20',
  
  // Cost indicators
  free: 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-950/20',
  freemium: 'text-muted-foreground bg-muted',
  paid: 'text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-950/20',
  
  // Trend indicators (avoiding blue)
  growing: 'text-green-600 dark:text-green-400',
  declining: 'text-destructive',
  emerging: 'text-purple-600 dark:text-purple-400',
  stable: 'text-muted-foreground',
  
  // Demand levels (neutral theme)
  demandVeryHigh: 'text-green-600 dark:text-green-400',
  demandHigh: 'text-primary',
  demandModerate: 'text-yellow-600 dark:text-yellow-400',
  demandLow: 'text-orange-600 dark:text-orange-400',
  demandVeryLow: 'text-destructive',
  
  // Question types for interview practice (neutral theme)
  behavioral: 'text-primary bg-secondary',
  technical: 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-950/20',
  situational: 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-950/20',
  leadership: 'text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-950/20',
  communication: 'text-pink-600 dark:text-pink-400 bg-pink-50 dark:bg-pink-950/20',
  
  // Responsive breakpoint indicators (neutral theme)
  xs: 'text-destructive bg-destructive/10',
  sm: 'text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-950/20',
  md: 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-950/20',
  lg: 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-950/20',
  xl: 'text-primary bg-secondary',
  '2xl': 'text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-950/20',
} as const;

/**
 * Get semantic color classes for status indicators
 */
export function getStatusColor(status: 'success' | 'error' | 'warning' | 'info'): string {
  return semanticColors[status];
}

/**
 * Get semantic color classes for severity levels
 */
export function getSeverityColor(severity: string): string {
  const normalizedSeverity = severity.toLowerCase();
  switch (normalizedSeverity) {
    case 'critical':
      return semanticColors.severityCritical;
    case 'high':
      return semanticColors.severityHigh;
    case 'medium':
      return semanticColors.severityMedium;
    case 'low':
      return semanticColors.severityLow;
    default:
      return semanticColors.severityLow;
  }
}

/**
 * Get background color for severity levels (for border styling)
 */
export function getSeverityBgColor(severity: string): string {
  const normalizedSeverity = severity.toLowerCase();
  switch (normalizedSeverity) {
    case 'critical':
      return '#ef4444'; // red-500 equivalent
    case 'high':
      return '#f97316'; // orange-500 equivalent
    case 'medium':
      return '#eab308'; // yellow-500 equivalent
    case 'low':
      return '#22c55e'; // green-500 equivalent
    default:
      return '#6b7280'; // gray-500 equivalent
  }
}

/**
 * Get semantic color classes for skill levels
 */
export function getSkillLevelColor(level: 'beginner' | 'intermediate' | 'advanced'): string {
  return semanticColors[level];
}

/**
 * Get semantic color classes for cost indicators
 */
export function getCostColor(cost: 'free' | 'freemium' | 'paid'): string {
  return semanticColors[cost];
}

/**
 * Get semantic color classes for demand levels
 */
export function getDemandColor(demand: 'VERY_HIGH' | 'HIGH' | 'MODERATE' | 'LOW' | 'VERY_LOW'): string {
  const mapping = {
    'VERY_HIGH': semanticColors.demandVeryHigh,
    'HIGH': semanticColors.demandHigh,
    'MODERATE': semanticColors.demandModerate,
    'LOW': semanticColors.demandLow,
    'VERY_LOW': semanticColors.demandVeryLow,
  };
  return mapping[demand] || semanticColors.stable;
}

/**
 * Get semantic color classes for question types
 */
export function getQuestionTypeColor(type: string): string {
  const normalizedType = type.toLowerCase();
  switch (normalizedType) {
    case 'behavioral':
      return semanticColors.behavioral;
    case 'technical':
      return semanticColors.technical;
    case 'situational':
      return semanticColors.situational;
    case 'leadership':
      return semanticColors.leadership;
    case 'communication':
      return semanticColors.communication;
    default:
      return 'text-muted-foreground bg-muted';
  }
}

/**
 * Get semantic color classes for responsive breakpoints
 */
export function getBreakpointColor(breakpoint: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'): string {
  return semanticColors[breakpoint];
}
