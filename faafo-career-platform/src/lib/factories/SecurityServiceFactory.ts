/**
 * Security Service Factory
 * 
 * Factory for creating security service instances with dependency injection.
 * Reduces coupling by providing a centralized way to create and configure
 * security services.
 */

import {
  ISecurityValidator,
  IRateLimiter,
  ICSRFProtection,
  IAuthProvider,
  ISecurityLogger,
  ISecurityService,
  ISecurityServiceFactory,
  SecurityConfig,
  RateLimitConfig,
  CSRFConfig,
  ValidationOptions,
  SecurityOptions,
  Session,
  ValidationResult,
  SecurityScanResult,
  RateLimitResult,
  SecurityIncident,
  SECURITY_CONSTANTS
} from '@/lib/interfaces/security';
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Concrete Implementations

class StandardSecurityValidator implements ISecurityValidator {
  private config: ValidationOptions;

  constructor(config: ValidationOptions = {}) {
    this.config = {
      maxLength: SECURITY_CONSTANTS.DEFAULT_VALIDATION.maxInputLength,
      allowedTypes: ['string', 'number', 'boolean', 'object'],
      strictMode: false,
      sanitize: true,
      ...config
    };
  }

  async validateInput(input: any, options?: ValidationOptions): Promise<ValidationResult> {
    const opts = { ...this.config, ...options };
    const errors: string[] = [];

    // Basic validation
    if (input === null || input === undefined) {
      return { isValid: true, sanitizedInput: input };
    }

    // Length validation for strings
    if (typeof input === 'string' && input.length > opts.maxLength!) {
      errors.push(`Input exceeds maximum length of ${opts.maxLength}`);
    }

    // Type validation
    if (opts.allowedTypes && !opts.allowedTypes.includes(typeof input)) {
      errors.push(`Invalid input type: ${typeof input}`);
    }

    const sanitizedInput = opts.sanitize ? this.sanitizeInput(input) : input;

    return {
      isValid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      sanitizedInput
    };
  }

  securityScan(input: string): SecurityScanResult {
    const threats: string[] = [];
    let riskLevel: 'low' | 'medium' | 'high' = 'low';

    // Basic security patterns
    const dangerousPatterns = [
      /<script/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /\bSELECT\b.*\bFROM\b/gi,
      /\bINSERT\b.*\bINTO\b/gi,
      /\bDELETE\b.*\bFROM\b/gi,
      /\bUPDATE\b.*\bSET\b/gi
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(input)) {
        threats.push(`Dangerous pattern detected: ${pattern.source}`);
        riskLevel = 'high';
      }
    }

    return {
      riskLevel,
      threats,
      blocked: riskLevel === 'high',
      sanitized: riskLevel === 'high' ? this.sanitizeInput(input) : undefined
    };
  }

  sanitizeInput(input: any): any {
    if (typeof input === 'string') {
      return input
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '');
    }
    return input;
  }

  validateSchema<T>(input: any, schema: any): ValidationResult {
    // Basic schema validation - in production, use a proper schema validator like Zod
    try {
      // This is a simplified implementation
      return { isValid: true, sanitizedInput: input };
    } catch (error) {
      return { 
        isValid: false, 
        errors: [`Schema validation failed: ${error}`] 
      };
    }
  }
}

class MemoryRateLimiter implements IRateLimiter {
  private limits = new Map<string, { count: number; resetTime: Date }>();
  private config: RateLimitConfig;

  constructor(config: RateLimitConfig) {
    this.config = config;
  }

  async checkLimit(identifier: string, type?: string): Promise<RateLimitResult> {
    const key = type ? `${identifier}:${type}` : identifier;
    const now = new Date();
    const limit = this.limits.get(key);

    if (!limit || now > limit.resetTime) {
      // Reset or initialize
      const resetTime = new Date(now.getTime() + this.config.windowMs);
      this.limits.set(key, { count: 0, resetTime });
      
      return {
        allowed: true,
        remaining: this.config.maxRequests - 1,
        resetTime
      };
    }

    if (limit.count >= this.config.maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: limit.resetTime,
        retryAfter: Math.ceil((limit.resetTime.getTime() - now.getTime()) / 1000)
      };
    }

    return {
      allowed: true,
      remaining: this.config.maxRequests - limit.count - 1,
      resetTime: limit.resetTime
    };
  }

  async resetLimit(identifier: string): Promise<void> {
    this.limits.delete(identifier);
  }

  async incrementUsage(identifier: string): Promise<void> {
    const limit = this.limits.get(identifier);
    if (limit) {
      limit.count++;
    }
  }

  async getUsage(identifier: string): Promise<number> {
    const limit = this.limits.get(identifier);
    return limit ? limit.count : 0;
  }
}

class SimpleCSRFProtection implements ICSRFProtection {
  private config: CSRFConfig;

  constructor(config: CSRFConfig) {
    this.config = config;
  }

  validateToken(token: string, session: any): boolean {
    // Simplified CSRF validation
    return !!(token && token.length === this.config.tokenLength);
  }

  generateToken(session?: any): string {
    // Generate a simple token
    return Array.from({ length: this.config.tokenLength }, () => 
      Math.random().toString(36).charAt(2)
    ).join('');
  }

  extractToken(request: any): string | null {
    // Extract from header or body
    return request.headers?.get?.(this.config.headerName) || null;
  }

  isCSRFRequired(request: any): boolean {
    const method = request.method?.toLowerCase();
    return ['post', 'put', 'patch', 'delete'].includes(method);
  }
}

class NextAuthProvider implements IAuthProvider {
  async getSession(request: any): Promise<Session | null> {
    try {
      const session = await getServerSession(authOptions);
      return session as Session | null;
    } catch (error) {
      return null;
    }
  }

  validateSession(session: any): boolean {
    return session && session.user && session.user.id;
  }

  requireAuth(session: any): boolean {
    return this.validateSession(session);
  }

  hasRole(session: any, role: string): boolean {
    return session?.user?.role === role;
  }
}

class ConsoleSecurityLogger implements ISecurityLogger {
  logIncident(incident: SecurityIncident): void {
    console.warn('[SECURITY]', incident);
  }

  logValidationFailure(input: any, errors: string[]): void {
    console.warn('[SECURITY] Validation failure:', { errors, inputType: typeof input });
  }

  logRateLimitExceeded(identifier: string, type: string): void {
    console.warn('[SECURITY] Rate limit exceeded:', { identifier, type });
  }

  logCSRFFailure(request: any): void {
    console.warn('[SECURITY] CSRF validation failed:', { 
      method: request.method, 
      url: request.url 
    });
  }
}

class CompositeSecurityService implements ISecurityService {
  constructor(
    public validator: ISecurityValidator,
    public rateLimiter: IRateLimiter,
    public csrfProtection: ICSRFProtection,
    public authProvider: IAuthProvider,
    public logger: ISecurityLogger
  ) {}

  async protect(
    request: NextRequest,
    handler: () => Promise<NextResponse>,
    options: SecurityOptions = {}
  ): Promise<NextResponse> {
    try {
      // Authentication check
      if (options.requireAuth) {
        const session = await this.authProvider.getSession(request);
        if (!this.authProvider.requireAuth(session)) {
          return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }
      }

      // CSRF protection
      if (options.requireCSRF && this.csrfProtection.isCSRFRequired(request)) {
        const token = this.csrfProtection.extractToken(request);
        const session = await this.authProvider.getSession(request);
        
        if (!token || !this.csrfProtection.validateToken(token, session)) {
          this.logger.logCSRFFailure(request);
          return NextResponse.json({ error: 'CSRF token invalid' }, { status: 403 });
        }
      }

      // Rate limiting
      if (options.rateLimitType) {
        const identifier = request.ip || 'anonymous';
        const limitResult = await this.rateLimiter.checkLimit(identifier, options.rateLimitType);
        
        if (!limitResult.allowed) {
          this.logger.logRateLimitExceeded(identifier, options.rateLimitType);
          return NextResponse.json(
            { error: 'Rate limit exceeded' },
            { 
              status: 429,
              headers: {
                'Retry-After': limitResult.retryAfter?.toString() || '60'
              }
            }
          );
        }
        
        await this.rateLimiter.incrementUsage(identifier);
      }

      return await handler();
    } catch (error) {
      console.error('Security middleware error:', error);
      return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
  }

  async secureAuth(request: NextRequest, handler: () => Promise<NextResponse>, options: Partial<SecurityOptions> = {}): Promise<NextResponse> {
    return this.protect(request, handler, {
      requireAuth: true,
      requireCSRF: true,
      rateLimitType: 'auth',
      ...options
    });
  }

  async secureWrite(request: NextRequest, handler: () => Promise<NextResponse>, options: Partial<SecurityOptions> = {}): Promise<NextResponse> {
    return this.protect(request, handler, {
      requireAuth: true,
      requireCSRF: true,
      rateLimitType: 'write',
      ...options
    });
  }

  async secureRead(request: NextRequest, handler: () => Promise<NextResponse>, options: Partial<SecurityOptions> = {}): Promise<NextResponse> {
    return this.protect(request, handler, {
      requireCSRF: false,
      rateLimitType: 'read',
      ...options
    });
  }
}

// Factory Implementation

export class SecurityServiceFactory implements ISecurityServiceFactory {
  createValidator(config?: any): ISecurityValidator {
    return new StandardSecurityValidator(config);
  }

  createRateLimiter(type: string, config?: RateLimitConfig): IRateLimiter {
    const defaultConfig = {
      windowMs: SECURITY_CONSTANTS.DEFAULT_RATE_LIMIT.windowMs,
      maxRequests: SECURITY_CONSTANTS.DEFAULT_RATE_LIMIT.maxRequests
    };
    
    return new MemoryRateLimiter({ ...defaultConfig, ...config });
  }

  createCSRFProtection(config?: CSRFConfig): ICSRFProtection {
    const defaultConfig = {
      secret: process.env.NEXTAUTH_SECRET || 'default-secret',
      tokenLength: SECURITY_CONSTANTS.CSRF_TOKEN_LENGTH,
      cookieName: 'csrf-token',
      headerName: 'x-csrf-token'
    };
    
    return new SimpleCSRFProtection({ ...defaultConfig, ...config });
  }

  createAuthProvider(): IAuthProvider {
    return new NextAuthProvider();
  }

  createLogger(): ISecurityLogger {
    return new ConsoleSecurityLogger();
  }

  createSecurityService(config?: SecurityConfig): ISecurityService {
    const validator = this.createValidator(config?.validation);
    const rateLimiter = this.createRateLimiter('default', config?.rateLimits?.default);
    const csrfProtection = this.createCSRFProtection(config?.csrf);
    const authProvider = this.createAuthProvider();
    const logger = this.createLogger();

    return new CompositeSecurityService(
      validator,
      rateLimiter,
      csrfProtection,
      authProvider,
      logger
    );
  }
}

// Export singleton factory
export const securityServiceFactory = new SecurityServiceFactory();
