/**
 * Compression utilities for cache optimization
 * Provides efficient compression/decompression for cache data
 */

import { gzipSync, gunzipSync } from 'zlib';

/**
 * Compress data using gzip
 */
export function compress(data: string): string {
  try {
    const buffer = Buffer.from(data, 'utf8');
    const compressed = gzipSync(buffer);
    return compressed.toString('base64');
  } catch (error) {
    console.error('Compression failed:', error);
    return data; // Return original data if compression fails
  }
}

/**
 * Decompress gzipped data
 */
export function decompress(compressedData: string): string {
  try {
    const buffer = Buffer.from(compressedData, 'base64');
    const decompressed = gunzipSync(buffer);
    return decompressed.toString('utf8');
  } catch (error) {
    console.error('Decompression failed:', error);
    return compressedData; // Return original data if decompression fails
  }
}

/**
 * Calculate compression ratio
 */
export function getCompressionRatio(original: string, compressed: string): number {
  const originalSize = Buffer.byteLength(original, 'utf8');
  const compressedSize = Buffer.byteLength(compressed, 'base64');
  return ((originalSize - compressedSize) / originalSize) * 100;
}

/**
 * Check if data should be compressed based on size threshold
 */
export function shouldCompress(data: string, threshold: number = 1024): boolean {
  return Buffer.byteLength(data, 'utf8') > threshold;
}
