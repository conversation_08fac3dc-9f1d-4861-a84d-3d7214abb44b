/**
 * Memory Leak Test for Auth State Manager
 * 
 * This test verifies that the auth state manager properly cleans up
 * intervals and event listeners to prevent memory leaks.
 */

import { getAuthStateManager, destroyAuthStateManager } from '../auth-state-manager';

// Mock next-auth
jest.mock('next-auth/react', () => ({
  getSession: jest.fn().mockResolvedValue(null)
}));

// Mock window and localStorage for testing
const mockWindow = {
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  localStorage: {
    setItem: jest.fn(),
    getItem: jest.fn(),
    removeItem: jest.fn()
  }
};

// Mock setInterval and clearInterval
const mockSetInterval = jest.fn();
const mockClearInterval = jest.fn();

describe('Auth State Manager Memory Leak Prevention', () => {
  let originalSetInterval: any;
  let originalClearInterval: any;

  beforeEach(() => {
    // Store original globals
    originalSetInterval = global.setInterval;
    originalClearInterval = global.clearInterval;

    // Mock globals
    global.setInterval = mockSetInterval;
    global.clearInterval = mockClearInterval;

    // Ensure window is available for testing
    if (typeof global.window === 'undefined') {
      (global as any).window = mockWindow;
    } else {
      // Replace window methods with mocks
      global.window.addEventListener = mockWindow.addEventListener;
      global.window.removeEventListener = mockWindow.removeEventListener;
    }

    // Reset mocks
    jest.clearAllMocks();

    // Mock setInterval to return a fake timer ID
    mockSetInterval.mockReturnValue(12345);
  });

  afterEach(() => {
    // Clean up any existing auth state manager first
    destroyAuthStateManager();

    // Restore original globals
    global.setInterval = originalSetInterval;
    global.clearInterval = originalClearInterval;
  });

  test('should set up interval and event listeners on initialization', () => {
    // Create auth state manager instance
    const authStateManager = getAuthStateManager();

    // Verify interval was set up
    expect(mockSetInterval).toHaveBeenCalledWith(
      expect.any(Function),
      10 * 60 * 1000 // 10 minutes
    );

    // Verify event listeners were added
    expect(mockWindow.addEventListener).toHaveBeenCalledWith('storage', expect.any(Function));
    expect(mockWindow.addEventListener).toHaveBeenCalledWith('focus', expect.any(Function));
    expect(mockWindow.addEventListener).toHaveBeenCalledWith('beforeunload', expect.any(Function));
  });

  test('should clean up interval and event listeners on destroy', () => {
    // Create auth state manager instance
    const authStateManager = getAuthStateManager();

    // Reset mocks to track cleanup calls
    jest.clearAllMocks();

    // Destroy the auth state manager
    destroyAuthStateManager();

    // Verify interval was cleared
    expect(mockClearInterval).toHaveBeenCalledWith(12345);

    // Verify event listeners were removed
    expect(mockWindow.removeEventListener).toHaveBeenCalledWith('storage', expect.any(Function));
    expect(mockWindow.removeEventListener).toHaveBeenCalledWith('focus', expect.any(Function));
    expect(mockWindow.removeEventListener).toHaveBeenCalledWith('beforeunload', expect.any(Function));
  });

  test('should not perform operations on destroyed instance', () => {
    // Create and destroy auth state manager
    const authStateManager = getAuthStateManager();
    destroyAuthStateManager();

    // Reset mocks
    jest.clearAllMocks();

    // Try to subscribe to a destroyed instance
    const unsubscribe = authStateManager.subscribe(() => {});

    // Should return a no-op function
    expect(typeof unsubscribe).toBe('function');
    
    // Calling unsubscribe should not throw
    expect(() => unsubscribe()).not.toThrow();
  });

  test('should prevent double initialization', () => {
    // Create auth state manager instance
    const authStateManager1 = getAuthStateManager();
    
    // Reset mocks to track second initialization
    jest.clearAllMocks();
    
    // Get the same instance again
    const authStateManager2 = getAuthStateManager();

    // Should be the same instance
    expect(authStateManager1).toBe(authStateManager2);

    // Should not set up interval again
    expect(mockSetInterval).not.toHaveBeenCalled();
  });

  test('should add beforeunload listener for activity tracking', () => {
    // Create auth state manager instance
    getAuthStateManager();

    // Find the beforeunload listener that was added by the auth manager
    const beforeUnloadCalls = mockWindow.addEventListener.mock.calls.filter(
      call => call[0] === 'beforeunload'
    );

    // Should have at least one beforeunload listener (from auth manager)
    expect(beforeUnloadCalls.length).toBeGreaterThanOrEqual(1);

    // Get the auth manager's beforeunload listener
    const activityListener = beforeUnloadCalls[0][1];

    // Should not throw when called
    expect(() => activityListener()).not.toThrow();
  });

  test('should handle automatic cleanup via destroyAuthStateManager', () => {
    // Create auth state manager instance
    getAuthStateManager();

    // Reset mocks to track cleanup
    jest.clearAllMocks();

    // Call destroy function directly (simulates what happens on page unload)
    destroyAuthStateManager();

    // Should have cleaned up the interval
    expect(mockClearInterval).toHaveBeenCalledWith(12345);
  });

  test('should not crash when destroying non-existent instance', () => {
    // Should not throw when destroying before creation
    expect(() => destroyAuthStateManager()).not.toThrow();

    // Should not call clearInterval if no instance exists
    expect(mockClearInterval).not.toHaveBeenCalled();
  });

  test('should handle interval callback safely after destruction', () => {
    // Create auth state manager instance
    getAuthStateManager();

    // Get the interval callback
    const intervalCallback = mockSetInterval.mock.calls[0][0];

    // Destroy the instance
    destroyAuthStateManager();

    // Calling the interval callback should not throw
    expect(() => intervalCallback()).not.toThrow();
  });
});
