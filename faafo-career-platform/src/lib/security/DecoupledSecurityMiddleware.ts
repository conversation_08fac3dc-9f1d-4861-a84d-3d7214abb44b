/**
 * Decoupled Security Middleware
 * 
 * Refactored security middleware using dependency injection to reduce coupling.
 * Uses interfaces and factory pattern for better testability and maintainability.
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  ISecurityService,
  SecurityOptions,
  SecurityError,
  ValidationError,
  RateLimitError,
  CSRFError,
  AuthenticationError
} from '@/lib/interfaces/security';
import { securityServiceFactory } from '@/lib/factories/SecurityServiceFactory';

/**
 * Decoupled Security Middleware Class
 * 
 * Uses dependency injection instead of direct imports to reduce coupling.
 * All security services are injected through the constructor.
 */
export class DecoupledSecurityMiddleware {
  private securityService: ISecurityService;

  constructor(securityService?: ISecurityService) {
    // Use injected service or create default one
    this.securityService = securityService || securityServiceFactory.createSecurityService();
  }

  /**
   * Main security protection method
   * Applies comprehensive security layers with configurable options
   */
  async protect(
    request: NextRequest,
    handler: () => Promise<NextResponse>,
    options: SecurityOptions = {}
  ): Promise<NextResponse> {
    try {
      return await this.securityService.protect(request, handler, options);
    } catch (error) {
      return this.handleSecurityError(error);
    }
  }

  /**
   * Enhanced security for authentication endpoints
   */
  async secureAuth(
    request: NextRequest,
    handler: () => Promise<NextResponse>,
    options: Partial<SecurityOptions> = {}
  ): Promise<NextResponse> {
    try {
      return await this.securityService.secureAuth(request, handler, options);
    } catch (error) {
      return this.handleSecurityError(error);
    }
  }

  /**
   * Security for write operations
   */
  async secureWrite(
    request: NextRequest,
    handler: () => Promise<NextResponse>,
    options: Partial<SecurityOptions> = {}
  ): Promise<NextResponse> {
    try {
      return await this.securityService.secureWrite(request, handler, options);
    } catch (error) {
      return this.handleSecurityError(error);
    }
  }

  /**
   * Security for read operations
   */
  async secureRead(
    request: NextRequest,
    handler: () => Promise<NextResponse>,
    options: Partial<SecurityOptions> = {}
  ): Promise<NextResponse> {
    try {
      return await this.securityService.secureRead(request, handler, options);
    } catch (error) {
      return this.handleSecurityError(error);
    }
  }

  /**
   * Body-aware security wrapper with validation
   */
  async secureWithBodyValidation(
    request: NextRequest,
    handler: (body?: any) => Promise<NextResponse>,
    options: Partial<SecurityOptions> = {}
  ): Promise<NextResponse> {
    return this.protect(request, async () => {
      let body;
      
      if (request.method !== 'GET' && request.headers.get('content-type')?.includes('application/json')) {
        try {
          body = await request.json();
          
          // Validate body if validation is enabled
          if (options.validateInput !== false) {
            const validation = await this.securityService.validator.validateInput(body);
            if (!validation.isValid) {
              this.securityService.logger.logValidationFailure(body, validation.errors || []);
              return NextResponse.json(
                { error: 'Invalid request body', details: validation.errors },
                { status: 400 }
              );
            }
            body = validation.sanitizedInput;
          }
        } catch (error) {
          return NextResponse.json(
            { error: 'Invalid JSON in request body' },
            { status: 400 }
          );
        }
      }

      return handler(body);
    }, options);
  }

  /**
   * Centralized error handling for security errors
   */
  private handleSecurityError(error: unknown): NextResponse {
    if (error instanceof ValidationError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.message },
        { status: 400 }
      );
    }

    if (error instanceof RateLimitError) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { 
          status: 429,
          headers: {
            'Retry-After': error.retryAfter.toString()
          }
        }
      );
    }

    if (error instanceof CSRFError) {
      return NextResponse.json(
        { error: 'CSRF token validation failed' },
        { status: 403 }
      );
    }

    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (error instanceof SecurityError) {
      const statusCode = error.severity === 'CRITICAL' ? 403 : 400;
      return NextResponse.json(
        { error: 'Security violation', type: error.type },
        { status: statusCode }
      );
    }

    // Generic error handling
    console.error('Unexpected security middleware error:', error);
    return NextResponse.json(
      { error: 'Internal security error' },
      { status: 500 }
    );
  }

  /**
   * Health check for security services
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    services: Record<string, boolean>;
    timestamp: string;
  }> {
    const services = {
      validator: true,
      rateLimiter: true,
      csrfProtection: true,
      authProvider: true,
      logger: true
    };

    try {
      // Test validator
      await this.securityService.validator.validateInput('test');
    } catch {
      services.validator = false;
    }

    try {
      // Test rate limiter
      await this.securityService.rateLimiter.checkLimit('health-check');
    } catch {
      services.rateLimiter = false;
    }

    try {
      // Test CSRF protection
      this.securityService.csrfProtection.generateToken();
    } catch {
      services.csrfProtection = false;
    }

    try {
      // Test auth provider (simplified)
      this.securityService.authProvider.validateSession(null);
    } catch {
      services.authProvider = false;
    }

    const healthyCount = Object.values(services).filter(Boolean).length;
    const totalCount = Object.keys(services).length;
    
    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (healthyCount === totalCount) {
      status = 'healthy';
    } else if (healthyCount >= totalCount * 0.7) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }

    return {
      status,
      services,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get security metrics
   */
  async getMetrics(): Promise<{
    rateLimitMetrics: Record<string, any>;
    validationMetrics: Record<string, any>;
    timestamp: string;
  }> {
    // This would be implemented based on the specific metrics each service provides
    return {
      rateLimitMetrics: {},
      validationMetrics: {},
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Factory function to create security middleware with custom configuration
 */
export function createSecurityMiddleware(options?: {
  customSecurityService?: ISecurityService;
}): DecoupledSecurityMiddleware {
  return new DecoupledSecurityMiddleware(options?.customSecurityService);
}

/**
 * Default security middleware instance
 * Uses the default security service factory configuration
 */
export const securityMiddleware = new DecoupledSecurityMiddleware();

/**
 * Convenience functions that use the default middleware instance
 * These maintain backward compatibility with existing code
 */
export const protect = (
  request: NextRequest,
  handler: () => Promise<NextResponse>,
  options?: SecurityOptions
) => securityMiddleware.protect(request, handler, options);

export const secureAuth = (
  request: NextRequest,
  handler: () => Promise<NextResponse>,
  options?: Partial<SecurityOptions>
) => securityMiddleware.secureAuth(request, handler, options);

export const secureWrite = (
  request: NextRequest,
  handler: () => Promise<NextResponse>,
  options?: Partial<SecurityOptions>
) => securityMiddleware.secureWrite(request, handler, options);

export const secureRead = (
  request: NextRequest,
  handler: () => Promise<NextResponse>,
  options?: Partial<SecurityOptions>
) => securityMiddleware.secureRead(request, handler, options);

export const secureWithBodyValidation = (
  request: NextRequest,
  handler: (body?: any) => Promise<NextResponse>,
  options?: Partial<SecurityOptions>
) => securityMiddleware.secureWithBodyValidation(request, handler, options);

/**
 * Migration helper for existing SecurityMiddleware usage
 * 
 * This class provides the same static methods as the original SecurityMiddleware
 * but uses the new decoupled implementation under the hood.
 */
export class SecurityMiddleware {
  static async protect(
    request: NextRequest,
    handler: () => Promise<NextResponse>,
    options: SecurityOptions = {}
  ): Promise<NextResponse> {
    return securityMiddleware.protect(request, handler, options);
  }

  static async secureAuth(
    request: NextRequest,
    handler: () => Promise<NextResponse>,
    options: Partial<SecurityOptions> = {}
  ): Promise<NextResponse> {
    return securityMiddleware.secureAuth(request, handler, options);
  }

  static async secureWrite(
    request: NextRequest,
    handler: () => Promise<NextResponse>,
    options: Partial<SecurityOptions> = {}
  ): Promise<NextResponse> {
    return securityMiddleware.secureWrite(request, handler, options);
  }

  static async secureRead(
    request: NextRequest,
    handler: () => Promise<NextResponse>,
    options: Partial<SecurityOptions> = {}
  ): Promise<NextResponse> {
    return securityMiddleware.secureRead(request, handler, options);
  }

  static async secureWithBodyValidation(
    request: NextRequest,
    handler: (body?: any) => Promise<NextResponse>,
    options: Partial<SecurityOptions> = {}
  ): Promise<NextResponse> {
    return securityMiddleware.secureWithBodyValidation(request, handler, options);
  }
}
