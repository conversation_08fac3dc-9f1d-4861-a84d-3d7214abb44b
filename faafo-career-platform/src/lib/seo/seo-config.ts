/**
 * Comprehensive SEO Configuration for FAAFO Career Platform
 * Provides centralized SEO metadata, structured data, and optimization utilities
 */

import { Metadata } from 'next';

export interface SEOConfig {
  title: string;
  description: string;
  keywords?: string[];
  canonical?: string;
  noindex?: boolean;
  nofollow?: boolean;
  openGraph?: {
    title?: string;
    description?: string;
    type?: 'website' | 'article' | 'profile';
    images?: Array<{
      url: string;
      width?: number;
      height?: number;
      alt?: string;
    }>;
  };
  twitter?: {
    card?: 'summary' | 'summary_large_image' | 'app' | 'player';
    title?: string;
    description?: string;
    images?: string[];
  };
  structuredData?: object[];
}

export interface BreadcrumbItem {
  name: string;
  url: string;
}

const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://faafo-career.com';
const siteName = 'FAAFO Career Platform';
const defaultImage = `${baseUrl}/images/og-default.jpg`;

/**
 * Default SEO configuration
 */
export const defaultSEO: SEOConfig = {
  title: 'FAAFO Career Platform - Find Your Path to Career Freedom',
  description: 'Empowering career transitions through personalized assessments, financial planning, and community support. Take control of your career journey with FAAFO.',
  keywords: [
    'career development',
    'career transition',
    'professional growth',
    'career assessment',
    'financial planning',
    'career freedom',
    'skill development',
    'career coaching',
    'professional development',
    'career guidance'
  ],
  openGraph: {
    title: 'FAAFO Career Platform - Find Your Path to Career Freedom',
    description: 'Empowering career transitions through personalized assessments, financial planning, and community support.',
    type: 'website',
    images: [
      {
        url: defaultImage,
        width: 1200,
        height: 630,
        alt: 'FAAFO Career Platform - Career Development Tools',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'FAAFO Career Platform - Find Your Path to Career Freedom',
    description: 'Empowering career transitions through personalized assessments, financial planning, and community support.',
    images: [defaultImage],
  },
};

/**
 * Page-specific SEO configurations
 */
export const pageSEOConfigs: Record<string, SEOConfig> = {
  home: {
    title: 'FAAFO Career Platform - Find Your Path to Career Freedom',
    description: 'Discover your ideal career path with AI-powered assessments, personalized learning resources, and financial planning tools. Join thousands taking control of their career journey.',
    keywords: [
      'career platform',
      'career assessment',
      'career transition',
      'professional development',
      'career freedom',
      'skill gap analysis',
      'career planning',
      'financial independence',
      'career coaching',
      'professional growth'
    ],
    openGraph: {
      title: 'FAAFO Career Platform - Find Your Path to Career Freedom',
      description: 'Discover your ideal career path with AI-powered assessments, personalized learning resources, and financial planning tools.',
      type: 'website',
      images: [
        {
          url: `${baseUrl}/images/home-og.jpg`,
          width: 1200,
          height: 630,
          alt: 'FAAFO Career Platform Homepage',
        },
      ],
    },
    structuredData: [
      {
        '@context': 'https://schema.org',
        '@type': 'WebSite',
        name: siteName,
        url: baseUrl,
        description: 'Empowering career transitions through personalized assessments, financial planning, and community support.',
        potentialAction: {
          '@type': 'SearchAction',
          target: `${baseUrl}/search?q={search_term_string}`,
          'query-input': 'required name=search_term_string',
        },
      },
      {
        '@context': 'https://schema.org',
        '@type': 'Organization',
        name: siteName,
        url: baseUrl,
        logo: `${baseUrl}/images/logo.png`,
        description: 'Career development platform providing AI-powered assessments and personalized career guidance.',
        sameAs: [
          'https://twitter.com/faafo_platform',
          'https://linkedin.com/company/faafo-career',
        ],
      },
    ],
  },

  dashboard: {
    title: 'Dashboard - FAAFO Career Platform',
    description: 'Your personalized career dashboard with progress tracking, learning resources, and career insights.',
    noindex: true, // Private user content
    keywords: [
      'career dashboard',
      'progress tracking',
      'personalized learning',
      'career insights',
      'professional development'
    ],
  },

  'career-paths': {
    title: 'Career Paths - Explore Your Options | FAAFO',
    description: 'Explore diverse career paths with detailed insights, requirements, and actionable steps. Find your ideal career direction with expert guidance.',
    keywords: [
      'career paths',
      'career exploration',
      'career options',
      'career guidance',
      'professional careers',
      'career change',
      'career direction',
      'job opportunities',
      'career planning',
      'career advice'
    ],
    openGraph: {
      title: 'Career Paths - Explore Your Professional Options',
      description: 'Discover diverse career paths with detailed insights, requirements, and actionable steps to achieve your goals.',
      type: 'website',
      images: [
        {
          url: `${baseUrl}/images/career-paths-og.jpg`,
          width: 1200,
          height: 630,
          alt: 'Career Paths Exploration',
        },
      ],
    },
    structuredData: [
      {
        '@context': 'https://schema.org',
        '@type': 'WebPage',
        name: 'Career Paths',
        description: 'Explore diverse career paths with detailed insights and actionable steps.',
        url: `${baseUrl}/career-paths`,
        isPartOf: {
          '@type': 'WebSite',
          name: siteName,
          url: baseUrl,
        },
      },
    ],
  },

  assessment: {
    title: 'Career Assessment - Discover Your Strengths | FAAFO',
    description: 'Take our comprehensive career assessment to discover your strengths, interests, and ideal career matches. Get personalized career recommendations.',
    keywords: [
      'career assessment',
      'career test',
      'personality assessment',
      'career quiz',
      'career evaluation',
      'strengths assessment',
      'career matching',
      'career discovery',
      'professional assessment',
      'career guidance'
    ],
    openGraph: {
      title: 'Career Assessment - Discover Your Ideal Career Path',
      description: 'Take our comprehensive assessment to discover your strengths and get personalized career recommendations.',
      type: 'website',
      images: [
        {
          url: `${baseUrl}/images/assessment-og.jpg`,
          width: 1200,
          height: 630,
          alt: 'Career Assessment Tool',
        },
      ],
    },
    structuredData: [
      {
        '@context': 'https://schema.org',
        '@type': 'WebApplication',
        name: 'FAAFO Career Assessment',
        description: 'Comprehensive career assessment tool for discovering strengths and career matches.',
        url: `${baseUrl}/assessment`,
        applicationCategory: 'EducationalApplication',
        operatingSystem: 'Web Browser',
        offers: {
          '@type': 'Offer',
          price: '0',
          priceCurrency: 'USD',
        },
      },
    ],
  },

  'freedom-fund': {
    title: 'Freedom Fund Calculator - Financial Independence | FAAFO',
    description: 'Calculate your path to financial independence with our Freedom Fund calculator. Plan your career transition with confidence.',
    keywords: [
      'freedom fund',
      'financial independence',
      'financial calculator',
      'retirement planning',
      'financial freedom',
      'career transition planning',
      'financial planning',
      'investment calculator',
      'financial goals',
      'wealth building'
    ],
    openGraph: {
      title: 'Freedom Fund Calculator - Plan Your Financial Independence',
      description: 'Calculate your path to financial independence and plan your career transition with confidence.',
      type: 'website',
      images: [
        {
          url: `${baseUrl}/images/freedom-fund-og.jpg`,
          width: 1200,
          height: 630,
          alt: 'Freedom Fund Calculator',
        },
      ],
    },
  },

  tools: {
    title: 'Career Tools - Professional Development Resources | FAAFO',
    description: 'Access powerful career development tools including salary calculators, interview prep, and resume builders. Advance your career with expert resources.',
    keywords: [
      'career tools',
      'professional tools',
      'career resources',
      'salary calculator',
      'interview preparation',
      'resume builder',
      'career development tools',
      'professional development',
      'career planning tools',
      'job search tools'
    ],
    openGraph: {
      title: 'Career Tools - Professional Development Resources',
      description: 'Access powerful career development tools to advance your professional journey.',
      type: 'website',
      images: [
        {
          url: `${baseUrl}/images/tools-og.jpg`,
          width: 1200,
          height: 630,
          alt: 'Career Development Tools',
        },
      ],
    },
  },

  resources: {
    title: 'Learning Resources - Career Development Library | FAAFO',
    description: 'Explore our comprehensive library of career development resources, courses, and guides. Accelerate your professional growth.',
    keywords: [
      'learning resources',
      'career development',
      'professional courses',
      'career guides',
      'skill development',
      'online learning',
      'career education',
      'professional training',
      'career library',
      'learning materials'
    ],
    openGraph: {
      title: 'Learning Resources - Career Development Library',
      description: 'Explore comprehensive career development resources and accelerate your professional growth.',
      type: 'website',
      images: [
        {
          url: `${baseUrl}/images/resources-og.jpg`,
          width: 1200,
          height: 630,
          alt: 'Learning Resources Library',
        },
      ],
    },
  },

  forum: {
    title: 'Community Forum - Career Discussions | FAAFO',
    description: 'Join our vibrant community forum to discuss career challenges, share experiences, and get advice from fellow professionals.',
    keywords: [
      'career forum',
      'professional community',
      'career discussions',
      'career advice',
      'professional networking',
      'career support',
      'career questions',
      'professional help',
      'career community',
      'job search support'
    ],
    openGraph: {
      title: 'Community Forum - Career Discussions and Support',
      description: 'Join our vibrant community to discuss career challenges and get advice from fellow professionals.',
      type: 'website',
      images: [
        {
          url: `${baseUrl}/images/forum-og.jpg`,
          width: 1200,
          height: 630,
          alt: 'Career Community Forum',
        },
      ],
    },
  },
};

/**
 * Generate comprehensive metadata for a page
 */
export function generatePageMetadata(
  pageKey: string,
  customConfig?: Partial<SEOConfig>
): Metadata {
  const config = {
    ...defaultSEO,
    ...pageSEOConfigs[pageKey],
    ...customConfig,
  };

  const metadata: Metadata = {
    title: config.title,
    description: config.description,
    keywords: config.keywords?.join(', '),
    robots: {
      index: !config.noindex,
      follow: !config.nofollow,
      googleBot: {
        index: !config.noindex,
        follow: !config.nofollow,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      title: config.openGraph?.title || config.title,
      description: config.openGraph?.description || config.description,
      type: config.openGraph?.type || 'website',
      url: config.canonical || `${baseUrl}/${pageKey === 'home' ? '' : pageKey}`,
      images: config.openGraph?.images || defaultSEO.openGraph?.images,
      siteName,
      locale: 'en_US',
    },
    twitter: {
      card: config.twitter?.card || 'summary_large_image',
      title: config.twitter?.title || config.title,
      description: config.twitter?.description || config.description,
      images: config.twitter?.images || [defaultImage],
      creator: '@faafo_platform',
    },
    alternates: {
      canonical: config.canonical || `${baseUrl}/${pageKey === 'home' ? '' : pageKey}`,
    },
  };

  return metadata;
}
