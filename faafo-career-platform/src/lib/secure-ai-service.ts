import { z } from 'zod';
import { geminiService } from './services/geminiService';

// Circuit Breaker for secure AI service
interface CircuitBreakerState {
  isOpen: boolean;
  failureCount: number;
  lastFailureTime: number;
  successCount: number;
}

class SecureAICircuitBreaker {
  private state: CircuitBreakerState = {
    isOpen: false,
    failureCount: 0,
    lastFailureTime: 0,
    successCount: 0
  };

  private readonly failureThreshold = 3; // More conservative for security
  private readonly recoveryTimeout = 30000; // 30 seconds

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state.isOpen) {
      if (Date.now() - this.state.lastFailureTime > this.recoveryTimeout) {
        this.state.isOpen = false;
        this.state.failureCount = 0;
      } else {
        throw new Error('Secure AI circuit breaker is open - service temporarily unavailable');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.state.successCount++;
    this.state.failureCount = 0;
  }

  private onFailure(): void {
    this.state.failureCount++;
    this.state.lastFailureTime = Date.now();

    if (this.state.failureCount >= this.failureThreshold) {
      this.state.isOpen = true;
      console.warn('Secure AI circuit breaker opened due to repeated failures');
    }
  }

  getState(): CircuitBreakerState {
    return { ...this.state };
  }
}

const secureAICircuitBreaker = new SecureAICircuitBreaker();

export interface SecureAIConfig {
  timeout: number;
  maxRetries: number;
  fallbackEnabled: boolean;
  validateResponse: boolean;
  sanitizeInput: boolean;
}

export interface AISecurityContext {
  userId?: string;
  sessionId?: string;
  endpoint: string;
  rateLimit?: {
    windowMs: number;
    maxRequests: number;
  };
}

export interface SecureAIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  fallback?: boolean;
  cached?: boolean;
  metadata?: {
    responseTime: number;
    retryCount: number;
    model: string;
    tokensUsed?: number;
  };
}

// Response validation schemas
const ResumeAnalysisSchema = z.object({
  overallScore: z.number().min(0).max(10),
  strengths: z.array(z.string().max(200)).max(10),
  improvements: z.array(z.string().max(200)).max(10),
  skillsIdentified: z.array(z.string().max(100)).max(20),
  experienceLevel: z.enum(['entry', 'mid', 'senior', 'executive']),
  recommendations: z.array(z.string().max(300)).max(5),
});

const CareerRecommendationSchema = z.object({
  recommendations: z.array(z.object({
    title: z.string().max(100),
    matchScore: z.number().min(0).max(100),
    description: z.string().max(500),
    requiredSkills: z.array(z.string().max(50)).max(10),
    salaryRange: z.object({
      min: z.number().min(0),
      max: z.number().min(0),
    }).optional(),
    growthPotential: z.enum(['low', 'medium', 'high']),
  })).max(10),
});

const InterviewAnalysisSchema = z.object({
  overallScore: z.number().min(0).max(10),
  analysis: z.object({
    clarity: z.number().min(0).max(10),
    relevance: z.number().min(0).max(10),
    structure: z.number().min(0).max(10),
    confidence: z.number().min(0).max(10),
  }),
  feedback: z.object({
    strengths: z.array(z.string().max(200)).max(5),
    improvements: z.array(z.string().max(200)).max(5),
    suggestions: z.array(z.string().max(300)).max(3),
  }),
  starMethodScore: z.number().min(0).max(10).optional(),
  communicationScore: z.number().min(0).max(10).optional(),
  technicalScore: z.number().min(0).max(10).optional(),
});

export class SecureAIService {
  private static readonly DEFAULT_CONFIG: SecureAIConfig = {
    timeout: 30000, // 30 seconds
    maxRetries: 2,
    fallbackEnabled: true,
    validateResponse: true,
    sanitizeInput: true,
  };

  private static readonly RATE_LIMITS = new Map<string, { count: number; resetTime: number }>();

  /**
   * Secure resume analysis with validation and fallback
   */
  static async analyzeResume(
    resumeText: string,
    context: AISecurityContext,
    config: Partial<SecureAIConfig> = {}
  ): Promise<SecureAIResponse> {
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
    const startTime = Date.now();

    try {
      // Rate limiting check
      if (!this.checkRateLimit(context)) {
        return {
          success: false,
          error: 'Rate limit exceeded for AI requests',
        };
      }

      // Input sanitization and validation
      const sanitizedInput = this.sanitizeInput(resumeText, 50000); // Max 50k chars
      if (!sanitizedInput || sanitizedInput.length < 50) {
        return {
          success: false,
          error: 'Resume text is too short or contains invalid content',
        };
      }

      // Execute with timeout and retry logic
      const result = await this.executeWithTimeout(
        () => geminiService.analyzeResume(sanitizedInput, context.userId),
        finalConfig.timeout,
        finalConfig.maxRetries
      );

      if (!result.success) {
        if (finalConfig.fallbackEnabled) {
          return this.getFallbackResumeAnalysis();
        }
        return result;
      }

      // Validate response structure
      if (finalConfig.validateResponse) {
        const validation = ResumeAnalysisSchema.safeParse(result.data);
        if (!validation.success) {
          console.warn('AI response validation failed:', validation.error);
          if (finalConfig.fallbackEnabled) {
            return this.getFallbackResumeAnalysis();
          }
          return {
            success: false,
            error: 'AI response format validation failed',
          };
        }
      }

      return {
        ...result,
        metadata: {
          responseTime: Date.now() - startTime,
          retryCount: 0,
          model: 'gemini-1.5-flash',
        },
      };

    } catch (error) {
      console.error('Secure AI Service Error:', error);
      
      if (finalConfig.fallbackEnabled) {
        return this.getFallbackResumeAnalysis();
      }

      return {
        success: false,
        error: 'AI service temporarily unavailable',
        metadata: {
          responseTime: Date.now() - startTime,
          retryCount: finalConfig.maxRetries,
          model: 'gemini-1.5-flash',
        },
      };
    }
  }

  /**
   * Secure career recommendations with validation
   */
  static async getCareerRecommendations(
    skills: string[],
    preferences: any,
    context: AISecurityContext,
    config: Partial<SecureAIConfig> = {}
  ): Promise<SecureAIResponse> {
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
    const startTime = Date.now();

    try {
      if (!this.checkRateLimit(context)) {
        return {
          success: false,
          error: 'Rate limit exceeded for AI requests',
        };
      }

      // Sanitize and validate inputs
      const sanitizedSkills = skills
        .map(skill => this.sanitizeInput(skill, 100))
        .filter(skill => skill && skill.length > 0)
        .slice(0, 20); // Max 20 skills

      if (sanitizedSkills.length === 0) {
        return {
          success: false,
          error: 'No valid skills provided',
        };
      }

      const result = await this.executeWithTimeout(
        () => geminiService.generateCareerRecommendations(sanitizedSkills, preferences, context.userId),
        finalConfig.timeout,
        finalConfig.maxRetries
      );

      if (!result.success) {
        if (finalConfig.fallbackEnabled) {
          return this.getFallbackCareerRecommendations(sanitizedSkills);
        }
        return result;
      }

      // Validate response
      if (finalConfig.validateResponse) {
        const validation = CareerRecommendationSchema.safeParse(result.data);
        if (!validation.success) {
          console.warn('Career recommendations validation failed:', validation.error);
          if (finalConfig.fallbackEnabled) {
            return this.getFallbackCareerRecommendations(sanitizedSkills);
          }
          return {
            success: false,
            error: 'AI response format validation failed',
          };
        }
      }

      return {
        ...result,
        metadata: {
          responseTime: Date.now() - startTime,
          retryCount: 0,
          model: 'gemini-1.5-flash',
        },
      };

    } catch (error) {
      console.error('Career recommendations error:', error);
      
      if (finalConfig.fallbackEnabled) {
        return this.getFallbackCareerRecommendations(skills.slice(0, 5));
      }

      return {
        success: false,
        error: 'AI service temporarily unavailable',
      };
    }
  }

  /**
   * Secure interview response analysis
   */
  static async analyzeInterviewResponse(
    questionText: string,
    responseText: string,
    context: AISecurityContext,
    config: Partial<SecureAIConfig> = {}
  ): Promise<SecureAIResponse> {
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
    const startTime = Date.now();

    try {
      if (!this.checkRateLimit(context)) {
        return {
          success: false,
          error: 'Rate limit exceeded for AI requests',
        };
      }

      // Sanitize inputs
      const sanitizedQuestion = this.sanitizeInput(questionText, 1000);
      const sanitizedResponse = this.sanitizeInput(responseText, 5000);

      if (!sanitizedQuestion || !sanitizedResponse) {
        return {
          success: false,
          error: 'Invalid question or response content',
        };
      }

      const result = await this.executeWithTimeout(
        () => geminiService.analyzeInterviewResponse({
          questionText: sanitizedQuestion,
          responseText: sanitizedResponse,
          questionType: 'GENERAL',
          questionCategory: 'GENERAL',
          responseTime: 0,
          expectedDuration: 300,
        }),
        finalConfig.timeout,
        finalConfig.maxRetries
      );

      if (!result.success) {
        if (finalConfig.fallbackEnabled) {
          return this.getFallbackInterviewAnalysis();
        }
        return result;
      }

      // Validate response
      if (finalConfig.validateResponse) {
        const validation = InterviewAnalysisSchema.safeParse(result.data);
        if (!validation.success) {
          console.warn('Interview analysis validation failed:', validation.error);
          if (finalConfig.fallbackEnabled) {
            return this.getFallbackInterviewAnalysis();
          }
          return {
            success: false,
            error: 'AI response format validation failed',
          };
        }
      }

      return {
        ...result,
        metadata: {
          responseTime: Date.now() - startTime,
          retryCount: 0,
          model: 'gemini-1.5-flash',
        },
      };

    } catch (error) {
      console.error('Interview analysis error:', error);
      
      if (finalConfig.fallbackEnabled) {
        return this.getFallbackInterviewAnalysis();
      }

      return {
        success: false,
        error: 'AI service temporarily unavailable',
      };
    }
  }

  /**
   * Execute function with timeout and retry logic
   */
  private static async executeWithTimeout<T>(
    fn: () => Promise<T>,
    timeout: number,
    maxRetries: number
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('AI request timeout')), timeout);
        });

        return await Promise.race([fn(), timeoutPromise]);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt < maxRetries) {
          // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }

    throw lastError || new Error('Max retries exceeded');
  }

  /**
   * Input sanitization
   */
  private static sanitizeInput(input: string, maxLength: number): string {
    if (!input || typeof input !== 'string') {
      return '';
    }

    return input
      .trim()
      .slice(0, maxLength)
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: URLs
      .replace(/data:/gi, '') // Remove data: URLs
      .replace(/vbscript:/gi, ''); // Remove vbscript: URLs
  }

  /**
   * Rate limiting check
   */
  private static checkRateLimit(context: AISecurityContext): boolean {
    const key = `${context.endpoint}_${context.userId || 'anonymous'}`;
    const now = Date.now();
    const windowMs = context.rateLimit?.windowMs || 60000; // 1 minute default
    const maxRequests = context.rateLimit?.maxRequests || 10; // 10 requests default

    const current = this.RATE_LIMITS.get(key);
    
    if (!current || now > current.resetTime) {
      this.RATE_LIMITS.set(key, { count: 1, resetTime: now + windowMs });
      return true;
    }

    if (current.count >= maxRequests) {
      return false;
    }

    current.count++;
    return true;
  }

  /**
   * Fallback responses for when AI service fails
   */
  private static getFallbackResumeAnalysis(): SecureAIResponse {
    return {
      success: true,
      fallback: true,
      data: {
        overallScore: 6.5,
        strengths: [
          'Professional experience demonstrated',
          'Clear work history progression',
          'Relevant skills mentioned'
        ],
        improvements: [
          'Consider adding more quantifiable achievements',
          'Include relevant certifications or training',
          'Optimize keywords for your target role'
        ],
        skillsIdentified: ['Communication', 'Problem Solving', 'Team Collaboration'],
        experienceLevel: 'mid',
        recommendations: [
          'Highlight specific accomplishments with metrics',
          'Tailor resume content to target job descriptions'
        ]
      }
    };
  }

  private static getFallbackCareerRecommendations(skills: string[]): SecureAIResponse {
    return {
      success: true,
      fallback: true,
      data: {
        recommendations: [
          {
            title: 'Professional Development Opportunity',
            matchScore: 75,
            description: 'Based on your skills, consider exploring roles that leverage your expertise.',
            requiredSkills: skills.slice(0, 5),
            growthPotential: 'medium' as const,
          }
        ]
      }
    };
  }

  private static getFallbackInterviewAnalysis(): SecureAIResponse {
    return {
      success: true,
      fallback: true,
      data: {
        overallScore: 7.0,
        analysis: {
          clarity: 7.0,
          relevance: 7.0,
          structure: 6.5,
          confidence: 7.5,
        },
        feedback: {
          strengths: ['Clear communication', 'Relevant examples provided'],
          improvements: ['Consider using the STAR method', 'Add more specific details'],
          suggestions: ['Practice with more examples', 'Focus on quantifiable results']
        }
      }
    };
  }
}

export default SecureAIService;
