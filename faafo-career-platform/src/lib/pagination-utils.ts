/**
 * Pagination Utilities
 * Provides consistent pagination helpers and validation
 * Part of Phase 2B: Advanced Query Optimization
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { advancedPaginationService, PaginationOptions, PaginationResult } from './services/advanced-pagination-service';

// Validation schemas
export const offsetPaginationSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  orderBy: z.string().optional(),
  orderDirection: z.enum(['asc', 'desc']).default('desc')
});

export const cursorPaginationSchema = z.object({
  cursor: z.string().optional(),
  limit: z.coerce.number().min(1).max(100).default(10),
  direction: z.enum(['forward', 'backward']).default('forward'),
  orderBy: z.string().optional(),
  orderDirection: z.enum(['asc', 'desc']).default('desc')
});

export const hybridPaginationSchema = z.object({
  // Offset-based params
  page: z.coerce.number().min(1).optional(),
  
  // Cursor-based params
  cursor: z.string().optional(),
  direction: z.enum(['forward', 'backward']).default('forward'),
  
  // Common params
  limit: z.coerce.number().min(1).max(100).default(10),
  orderBy: z.string().optional(),
  orderDirection: z.enum(['asc', 'desc']).default('desc')
});

export type OffsetPaginationParams = z.infer<typeof offsetPaginationSchema>;
export type CursorPaginationParams = z.infer<typeof cursorPaginationSchema>;
export type HybridPaginationParams = z.infer<typeof hybridPaginationSchema>;

/**
 * Extract pagination parameters from request URL
 */
export function extractPaginationParams(
  request: NextRequest,
  schema: 'offset' | 'cursor' | 'hybrid' = 'hybrid'
): PaginationOptions {
  const { searchParams } = new URL(request.url);
  const params = Object.fromEntries(searchParams.entries());

  let validated;
  switch (schema) {
    case 'offset':
      validated = offsetPaginationSchema.parse(params);
      return {
        page: validated.page,
        limit: validated.limit,
        orderBy: validated.orderBy ? { [validated.orderBy]: validated.orderDirection } : undefined
      };
    
    case 'cursor':
      validated = cursorPaginationSchema.parse(params);
      return {
        cursor: validated.cursor,
        limit: validated.limit,
        direction: validated.direction,
        orderBy: validated.orderBy ? { [validated.orderBy]: validated.orderDirection } : undefined
      };
    
    case 'hybrid':
    default:
      validated = hybridPaginationSchema.parse(params);
      return {
        page: validated.page,
        cursor: validated.cursor,
        limit: validated.limit,
        direction: validated.direction,
        orderBy: validated.orderBy ? { [validated.orderBy]: validated.orderDirection } : undefined
      };
  }
}

/**
 * Create standardized paginated API response
 */
export function createPaginatedApiResponse<T>(
  result: PaginationResult<T>,
  message?: string
): NextResponse {
  return NextResponse.json(
    advancedPaginationService.createPaginationResponse(result, message),
    { status: 200 }
  );
}

/**
 * Prisma query builder helpers for pagination
 */
export class PrismaQueryBuilder {
  /**
   * Build offset-based query parameters
   */
  static buildOffsetQuery(options: PaginationOptions) {
    const page = options.page || 1;
    const limit = options.limit || 10;
    const skip = (page - 1) * limit;

    return {
      skip,
      take: limit,
      orderBy: options.orderBy || { createdAt: 'desc' }
    };
  }

  /**
   * Build cursor-based query parameters
   */
  static buildCursorQuery(
    options: PaginationOptions,
    cursorField: string = 'id',
    uniqueField?: string
  ) {
    const limit = options.limit || 10;
    const direction = options.direction || 'forward';

    let cursor;
    let orderBy = options.orderBy || { [cursorField]: 'desc' };

    if (options.cursor) {
      try {
        const decodedCursor = advancedPaginationService.decodeCursor(options.cursor);
        cursor = { [cursorField]: decodedCursor };
      } catch (error) {
        throw new Error('Invalid cursor format');
      }
    }

    // Adjust ordering for backward pagination
    if (direction === 'backward') {
      orderBy = Object.fromEntries(
        Object.entries(orderBy).map(([key, value]) => [
          key,
          value === 'asc' ? 'desc' : 'asc'
        ])
      );
    }

    return {
      cursor,
      take: direction === 'forward' ? limit : -limit,
      orderBy,
      skip: cursor ? 1 : 0 // Skip the cursor item itself
    };
  }

  /**
   * Build where clause with cursor conditions
   */
  static buildCursorWhereClause(
    baseWhere: any,
    options: PaginationOptions,
    cursorField: string = 'id'
  ) {
    if (!options.cursor) return baseWhere;

    try {
      const decodedCursor = advancedPaginationService.decodeCursor(options.cursor);
      const direction = options.direction || 'forward';
      const orderDirection = options.orderBy?.[cursorField] || 'desc';

      let cursorCondition;
      if (direction === 'forward') {
        cursorCondition = orderDirection === 'asc' 
          ? { [cursorField]: { gt: decodedCursor } }
          : { [cursorField]: { lt: decodedCursor } };
      } else {
        cursorCondition = orderDirection === 'asc'
          ? { [cursorField]: { lt: decodedCursor } }
          : { [cursorField]: { gt: decodedCursor } };
      }

      return {
        AND: [baseWhere, cursorCondition]
      };
    } catch (error) {
      throw new Error('Invalid cursor format');
    }
  }
}

/**
 * Performance-optimized pagination for large datasets
 */
export class OptimizedPagination {
  /**
   * Estimate optimal pagination strategy based on query characteristics
   */
  static estimateOptimalStrategy(
    totalRecords: number,
    currentPage: number,
    queryComplexity: 'low' | 'medium' | 'high' = 'medium'
  ): 'offset' | 'cursor' {
    // Use cursor pagination for:
    // 1. Large datasets (>10k records)
    // 2. Deep pagination (page > 100)
    // 3. Complex queries with joins
    
    if (totalRecords > 10000) return 'cursor';
    if (currentPage > 100) return 'cursor';
    if (queryComplexity === 'high' && currentPage > 50) return 'cursor';
    
    return 'offset';
  }

  /**
   * Calculate pagination performance metrics
   */
  static calculatePerformanceMetrics(
    queryTime: number,
    recordCount: number,
    strategy: 'offset' | 'cursor'
  ) {
    const avgTimePerRecord = recordCount > 0 ? queryTime / recordCount : 0;
    const efficiency = strategy === 'cursor' ? 'high' : 
                      queryTime < 100 ? 'high' :
                      queryTime < 500 ? 'medium' : 'low';

    return {
      queryTime,
      recordCount,
      avgTimePerRecord,
      efficiency,
      strategy,
      recommendations: this.generatePerformanceRecommendations(queryTime, recordCount, strategy)
    };
  }

  /**
   * Generate performance recommendations
   */
  private static generatePerformanceRecommendations(
    queryTime: number,
    recordCount: number,
    strategy: 'offset' | 'cursor'
  ): string[] {
    const recommendations: string[] = [];

    if (queryTime > 1000) {
      recommendations.push('Query time is high. Consider adding database indexes.');
    }

    if (strategy === 'offset' && recordCount > 1000) {
      recommendations.push('Consider switching to cursor-based pagination for better performance.');
    }

    if (recordCount === 0) {
      recommendations.push('No results found. Check query filters and conditions.');
    }

    if (queryTime > 500 && recordCount < 10) {
      recommendations.push('Low result count with high query time. Optimize query or add indexes.');
    }

    return recommendations;
  }
}

/**
 * Pagination middleware for consistent error handling
 */
export function withPagination<T>(
  handler: (options: PaginationOptions) => Promise<PaginationResult<T>>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      const options = extractPaginationParams(request);
      const validatedOptions = advancedPaginationService.validatePaginationParams(options);
      const result = await handler(validatedOptions);
      
      return createPaginatedApiResponse(result);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid pagination parameters',
            details: error.errors
          },
          { status: 400 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Pagination error'
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Cache key generator for paginated queries
 */
export function generatePaginationCacheKey(
  baseKey: string,
  options: PaginationOptions,
  additionalParams?: Record<string, any>
): string {
  const params = {
    ...options,
    ...additionalParams
  };
  
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((acc, key) => {
      acc[key] = (params as any)[key];
      return acc;
    }, {} as Record<string, any>);

  return `${baseKey}:${Buffer.from(JSON.stringify(sortedParams)).toString('base64')}`;
}
