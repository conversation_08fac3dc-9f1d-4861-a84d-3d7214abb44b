/**
 * Admin Performance Monitoring Page
 * 
 * Provides comprehensive performance monitoring dashboard for administrators
 * with real-time metrics, alerts, and system health monitoring.
 */

import { Metadata } from 'next';
import { PerformanceMonitoringDashboard } from '@/components/admin/PerformanceMonitoringDashboard';

export const metadata: Metadata = {
  title: 'Performance Monitoring | Admin Dashboard',
  description: 'Real-time performance monitoring and system health dashboard for administrators',
  robots: 'noindex, nofollow', // Admin pages should not be indexed
};

export default function AdminPerformancePage() {
  return (
    <div className="container mx-auto py-6 px-4">
      <PerformanceMonitoringDashboard />
    </div>
  );
}
