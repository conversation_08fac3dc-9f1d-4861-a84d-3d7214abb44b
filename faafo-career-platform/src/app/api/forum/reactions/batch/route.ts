import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

interface BatchReactionData {
  postReactions: Record<string, {
    reactions: Array<{ type: string; userId: string }>;
    counts: Record<string, number>;
    total: number;
    userReaction?: string;
  }>;
  replyReactions: Record<string, {
    reactions: Array<{ type: string; userId: string }>;
    counts: Record<string, number>;
    total: number;
    userReaction?: string;
  }>;
}

// GET - Batch load reactions for multiple posts/replies (prevents N+1 queries)
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 100 },
    async () => {
      const { searchParams } = new URL(request.url);
      const postIds = searchParams.get('postIds')?.split(',').filter(Boolean) || [];
      const replyIds = searchParams.get('replyIds')?.split(',').filter(Boolean) || [];
      
      const session = await getServerSession(authOptions);
      const userId = session?.user?.id;

      if (postIds.length === 0 && replyIds.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'At least one postId or replyId is required'
        }, { status: 400 });
      }

      // Limit batch size to prevent abuse
      if (postIds.length > 50 || replyIds.length > 50) {
        return NextResponse.json({
          success: false,
          error: 'Batch size too large (max 50 items per type)'
        }, { status: 400 });
      }

      const result: BatchReactionData = {
        postReactions: {},
        replyReactions: {}
      };

      // Batch load post reactions
      if (postIds.length > 0) {
        const postReactions = await prisma.forumPostReaction.findMany({
          where: { 
            postId: { in: postIds }
          },
          select: {
            postId: true,
            type: true,
            userId: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        });

        // Group reactions by post
        const postReactionGroups = postReactions.reduce((acc, reaction) => {
          if (!acc[reaction.postId]) {
            acc[reaction.postId] = [];
          }
          acc[reaction.postId].push(reaction);
          return acc;
        }, {} as Record<string, typeof postReactions>);

        // Process each post's reactions
        for (const postId of postIds) {
          const reactions = postReactionGroups[postId] || [];
          
          // Count reactions by type
          const counts = reactions.reduce((acc, reaction) => {
            acc[reaction.type] = (acc[reaction.type] || 0) + 1;
            return acc;
          }, {} as Record<string, number>);

          // Find user's reaction if authenticated
          const userReaction = userId 
            ? reactions.find(r => r.userId === userId)?.type
            : undefined;

          result.postReactions[postId] = {
            reactions: reactions.map(r => ({ type: r.type, userId: r.userId })),
            counts,
            total: reactions.length,
            userReaction,
          };
        }
      }

      // Batch load reply reactions
      if (replyIds.length > 0) {
        const replyReactions = await prisma.forumReplyReaction.findMany({
          where: { 
            replyId: { in: replyIds }
          },
          select: {
            replyId: true,
            type: true,
            userId: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        });

        // Group reactions by reply
        const replyReactionGroups = replyReactions.reduce((acc, reaction) => {
          if (!acc[reaction.replyId]) {
            acc[reaction.replyId] = [];
          }
          acc[reaction.replyId].push(reaction);
          return acc;
        }, {} as Record<string, typeof replyReactions>);

        // Process each reply's reactions
        for (const replyId of replyIds) {
          const reactions = replyReactionGroups[replyId] || [];
          
          // Count reactions by type
          const counts = reactions.reduce((acc, reaction) => {
            acc[reaction.type] = (acc[reaction.type] || 0) + 1;
            return acc;
          }, {} as Record<string, number>);

          // Find user's reaction if authenticated
          const userReaction = userId 
            ? reactions.find(r => r.userId === userId)?.type
            : undefined;

          result.replyReactions[replyId] = {
            reactions: reactions.map(r => ({ type: r.type, userId: r.userId })),
            counts,
            total: reactions.length,
            userReaction,
          };
        }
      }

      return NextResponse.json({
        success: true,
        data: result
      });
    }
  );
});

// POST - Batch add/remove reactions (prevents multiple API calls)
export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 50 },
    async () => {
      const session = await getServerSession(authOptions);

      if (!session?.user?.id) {
        return NextResponse.json({
          success: false,
          error: 'Authentication required'
        }, { status: 401 });
      }

      const body = await request.json();
      const { operations } = body;

      if (!Array.isArray(operations) || operations.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'Operations array is required'
        }, { status: 400 });
      }

      // Limit batch size
      if (operations.length > 20) {
        return NextResponse.json({
          success: false,
          error: 'Too many operations (max 20)'
        }, { status: 400 });
      }

      const results: any[] = [];

      // Process operations in transaction for consistency
      await prisma.$transaction(async (tx) => {
        for (const op of operations) {
          const { action, type, postId, replyId, reactionType } = op;

          if (!['add', 'remove'].includes(action)) {
            results.push({ error: 'Invalid action', operation: op });
            continue;
          }

          if (!['post', 'reply'].includes(type)) {
            results.push({ error: 'Invalid type', operation: op });
            continue;
          }

          if (!reactionType || !['like', 'dislike', 'love', 'laugh', 'angry', 'sad'].includes(reactionType)) {
            results.push({ error: 'Invalid reaction type', operation: op });
            continue;
          }

          try {
            if (type === 'post' && postId) {
              if (action === 'add') {
                // Remove existing reaction first, then add new one
                await tx.forumPostReaction.deleteMany({
                  where: {
                    postId,
                    userId: session.user!.id!,
                  },
                });

                await tx.forumPostReaction.create({
                  data: {
                    postId,
                    userId: session.user!.id!,
                    type: reactionType,
                  },
                });
              } else {
                await tx.forumPostReaction.deleteMany({
                  where: {
                    postId,
                    userId: session.user!.id!,
                    type: reactionType,
                  },
                });
              }
            } else if (type === 'reply' && replyId) {
              if (action === 'add') {
                // Remove existing reaction first, then add new one
                await tx.forumReplyReaction.deleteMany({
                  where: {
                    replyId,
                    userId: session.user!.id!,
                  },
                });

                await tx.forumReplyReaction.create({
                  data: {
                    replyId,
                    userId: session.user!.id!,
                    type: reactionType,
                  },
                });
              } else {
                await tx.forumReplyReaction.deleteMany({
                  where: {
                    replyId,
                    userId: session.user!.id!,
                    type: reactionType,
                  },
                });
              }
            }

            results.push({ success: true, operation: op });
          } catch (error) {
            console.error('Batch reaction operation failed:', error);
            results.push({ error: 'Operation failed', operation: op });
          }
        }
      });

      return NextResponse.json({
        success: true,
        data: { results },
        message: 'Batch operations completed'
      });
    }
  );
});
