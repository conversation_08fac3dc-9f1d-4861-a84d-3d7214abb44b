import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import sharp from 'sharp';
import crypto from 'crypto';
import { put } from '@vercel/blob';
import { log } from '@/lib/logger';
import { withCSRFProtection } from '@/lib/csrf';
import { enhancedRateLimiters } from '@/lib/enhanced-rate-limiter';

// SECURITY FIX: Enhanced file upload configuration
const MAX_FILE_SIZE = 2 * 1024 * 1024; // Reduced to 2MB for security
const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];
const ALLOWED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp'];
const AVATAR_SIZES = {
  thumbnail: 64,
  small: 128,
  medium: 256,
  large: 512
};

// SECURITY FIX: File signature validation (magic numbers)
const FILE_SIGNATURES = {
  'image/jpeg': [
    [0xFF, 0xD8, 0xFF], // JPEG
  ],
  'image/png': [
    [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A], // PNG
  ],
  'image/webp': [
    [0x52, 0x49, 0x46, 0x46], // RIFF (WebP container)
  ]
};

// SECURITY FIX: Malicious file patterns to detect
const MALICIOUS_PATTERNS = [
  // PHP tags
  /<\?php/i,
  /<\?=/i,
  /<script/i,
  // Executable signatures
  /MZ/, // PE executable
  /\x7fELF/, // ELF executable
  // Archive signatures
  /PK\x03\x04/, // ZIP
  /Rar!/, // RAR
];

// SECURITY FIX: Comprehensive file validation
async function validateFileContent(buffer: Buffer, mimeType: string, fileName: string): Promise<{
  isValid: boolean;
  errors: string[];
  sanitizedBuffer?: Buffer;
}> {
  const errors: string[] = [];

  // 1. File signature validation
  const signatures = FILE_SIGNATURES[mimeType as keyof typeof FILE_SIGNATURES];
  if (signatures) {
    const isValidSignature = signatures.some(signature => {
      return signature.every((byte, index) => buffer[index] === byte);
    });

    if (!isValidSignature) {
      errors.push('File signature does not match declared MIME type');
    }
  }

  // 2. Check for malicious patterns
  const bufferString = buffer.toString('binary');
  MALICIOUS_PATTERNS.forEach(pattern => {
    if (pattern.test(bufferString)) {
      errors.push('Malicious content detected in file');
    }
  });

  // 3. File extension validation
  const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  if (!ALLOWED_EXTENSIONS.includes(fileExtension)) {
    errors.push('Invalid file extension');
  }

  // 4. MIME type vs extension consistency
  const expectedMimeTypes: Record<string, string[]> = {
    '.jpg': ['image/jpeg'],
    '.jpeg': ['image/jpeg'],
    '.png': ['image/png'],
    '.webp': ['image/webp']
  };

  const expectedTypes = expectedMimeTypes[fileExtension];
  if (expectedTypes && !expectedTypes.includes(mimeType)) {
    errors.push('File extension does not match MIME type');
  }

  // 5. Additional security checks using Sharp (validates image structure)
  try {
    const metadata = await sharp(buffer).metadata();

    // Check for reasonable image dimensions
    if (!metadata.width || !metadata.height) {
      errors.push('Invalid image: missing dimensions');
    } else if (metadata.width > 4096 || metadata.height > 4096) {
      errors.push('Image dimensions too large (max 4096x4096)');
    } else if (metadata.width < 32 || metadata.height < 32) {
      errors.push('Image dimensions too small (min 32x32)');
    }

    // Check for excessive metadata (potential for hiding malicious content)
    if (metadata.exif && Buffer.byteLength(metadata.exif) > 65536) { // 64KB limit
      errors.push('Excessive EXIF data detected');
    }

    // Sanitize the image by re-encoding it (removes any embedded content)
    const sanitizedBuffer = await sharp(buffer)
      .jpeg({ quality: 85 }) // Always convert to JPEG for consistency
      .toBuffer();

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedBuffer: errors.length === 0 ? sanitizedBuffer : undefined
    };

  } catch (error) {
    errors.push('Invalid image file structure');
    return { isValid: false, errors };
  }
}

interface ProcessedImage {
  buffer: Buffer;
  size: number;
  format: string;
}

interface PhotoUploadResponse {
  success: true;
  profilePictureUrl: string;
  sizes: Record<string, string>;
  message: string;
}

interface PhotoDeleteResponse {
  success: true;
  message: string;
}

async function processImage(buffer: Buffer, size: number): Promise<ProcessedImage> {
  const processed = await sharp(buffer)
    .resize(size, size, {
      fit: 'cover',
      position: 'center'
    })
    .jpeg({ quality: 85 })
    .toBuffer();

  return {
    buffer: processed,
    size,
    format: 'jpeg'
  };
}

function generateFileName(userId: string, size: string): string {
  const timestamp = Date.now();
  const hash = crypto.createHash('md5').update(`${userId}-${timestamp}`).digest('hex').substring(0, 8);
  return `profile-${userId}-${size}-${hash}.jpg`;
}

// Upload file to Vercel Blob storage
async function uploadToStorage(buffer: Buffer, fileName: string): Promise<string> {
  try {
    // Check if BLOB_READ_WRITE_TOKEN is available
    if (!process.env.BLOB_READ_WRITE_TOKEN) {
      log.warn('BLOB_READ_WRITE_TOKEN not found, using local storage fallback', {
        component: 'photo_upload_api',
        action: 'upload_to_storage',
        metadata: { fallback: 'local_storage' }
      });
      // Fallback to local storage for development
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      return `${baseUrl}/api/profile/photo/${fileName}`;
    }

    // Upload to Vercel Blob
    const blob = await put(`profile-photos/${fileName}`, buffer, {
      access: 'public',
      contentType: 'image/jpeg', // All images are converted to JPEG
    });

    return blob.url;
  } catch (error) {
    log.error('Error uploading to Vercel Blob', error as Error, {
      component: 'photo_upload_api',
      action: 'upload_to_storage',
      metadata: { fileName }
    });
    // Fallback to local storage
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    return `${baseUrl}/api/profile/photo/${fileName}`;
  }
}

export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    // SECURITY FIX: Apply strict rate limiting for file uploads
    const rateLimitResult = await enhancedRateLimiters.write.checkLimit(request);

    if (!rateLimitResult.allowed) {
      const error = new Error('Too many file upload attempts. Please try again later.') as any;
      error.statusCode = 429;
      error.headers = rateLimitResult.headers;
      throw error;
    }

    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      const error = new Error('Not authenticated') as any;
      error.statusCode = 401;
      throw error;
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      const error = new Error('User not found') as any;
      error.statusCode = 404;
      throw error;
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      const error = new Error('No file provided') as any;
      error.statusCode = 400;
      throw error;
    }

    // SECURITY FIX: Enhanced file validation
    if (!ALLOWED_TYPES.includes(file.type)) {
      const error = new Error('Invalid file type. Only JPEG, PNG, and WebP are allowed.') as any;
      error.statusCode = 400;
      throw error;
    }

    if (file.size > MAX_FILE_SIZE) {
      const error = new Error(`File too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB.`) as any;
      error.statusCode = 400;
      throw error;
    }

    // SECURITY FIX: Validate file name
    if (!file.name || file.name.length > 255) {
      const error = new Error('Invalid file name') as any;
      error.statusCode = 400;
      throw error;
    }

    const buffer = Buffer.from(await file.arrayBuffer());

    // SECURITY FIX: Comprehensive file content validation
    const validation = await validateFileContent(buffer, file.type, file.name);

    if (!validation.isValid) {
      log.warn('File upload security validation failed', {
        component: 'photo_upload_api',
        userId: user.id,
        metadata: { fileName: file.name, errors: validation.errors }
      });

      const error = new Error('File failed security validation: ' + validation.errors.join(', ')) as any;
      error.statusCode = 400;
      error.data = { securityErrors: validation.errors };
      throw error;
    }

    // Use the sanitized buffer from validation
    const sanitizedBuffer = validation.sanitizedBuffer!;

    // SECURITY FIX: Process sanitized image for different sizes
    const processedImages = await Promise.all(
      Object.entries(AVATAR_SIZES).map(async ([sizeName, sizePixels]) => {
        const processed = await processImage(sanitizedBuffer, sizePixels);
        const fileName = generateFileName(user.id, sizeName);
        const url = await uploadToStorage(processed.buffer, fileName);
        return { size: sizeName, url, pixels: sizePixels };
      })
    );

    // Use the medium size as the primary profile picture URL
    const primaryImageUrl = processedImages.find(img => img.size === 'medium')?.url;

    if (!primaryImageUrl) {
      throw new Error('Failed to process primary image');
    }

    // Update user profile with new image URL
    const updatedProfile = await prisma.profile.upsert({
      where: { userId: user.id },
      update: {
        profilePictureUrl: primaryImageUrl,
        lastProfileUpdate: new Date(),
        updatedAt: new Date(),
      },
      create: {
        userId: user.id,
        profilePictureUrl: primaryImageUrl,
        lastProfileUpdate: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        success: true,
        profilePictureUrl: primaryImageUrl,
        sizes: processedImages.reduce((acc, img) => {
          acc[img.size] = img.url;
          return acc;
        }, {} as Record<string, string>),
        message: 'Profile photo updated successfully'
      }
    });
  });
});

export const DELETE = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    // SECURITY FIX: Apply rate limiting to DELETE operations
    const rateLimitResult = await enhancedRateLimiters.write.checkLimit(request);

    if (!rateLimitResult.allowed) {
      const error = new Error('Too many delete attempts. Please try again later.') as any;
      error.statusCode = 429;
      error.headers = rateLimitResult.headers;
      throw error;
    }

    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      const error = new Error('Not authenticated') as any;
      error.statusCode = 401;
      throw error;
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      const error = new Error('User not found') as any;
      error.statusCode = 404;
      throw error;
    }

    // Remove profile picture URL from database
    await prisma.profile.upsert({
      where: { userId: user.id },
      update: {
        profilePictureUrl: null,
        lastProfileUpdate: new Date(),
        updatedAt: new Date(),
      },
      create: {
        userId: user.id,
        profilePictureUrl: null,
        lastProfileUpdate: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        success: true,
        message: 'Profile photo removed successfully'
      }
    });
  });
});

// GET endpoint for serving local photos (fallback when Vercel Blob is not available)
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  const { pathname } = new URL(request.url);
  const fileName = pathname.split('/').pop();

  if (!fileName) {
    const error = new Error('File not found') as any;
    error.statusCode = 404;
    throw error;
  }

  // In a real implementation, you would serve from local storage or redirect to CDN
  // For now, return a placeholder response indicating the feature needs Vercel Blob
  const error = new Error('Photo serving requires Vercel Blob storage configuration') as any;
  error.statusCode = 501;
  throw error;
});
