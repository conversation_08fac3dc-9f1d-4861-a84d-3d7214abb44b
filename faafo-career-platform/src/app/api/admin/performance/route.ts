/**
 * Advanced Query Performance Monitoring API
 * Provides access to query performance metrics, alerts, and optimization suggestions
 * Part of Phase 2B: Advanced Query Optimization
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { advancedQueryPerformanceMonitor } from '@/lib/services/advanced-query-performance-monitor';
import { z } from 'zod';

// Validation schemas
const performanceQuerySchema = z.object({
  action: z.enum(['metrics', 'alerts', 'report', 'suggestions', 'regressions']).default('metrics'),
  queryName: z.string().optional(),
  startDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  endDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  limit: z.coerce.number().min(1).max(1000).default(100),
  lookbackDays: z.coerce.number().min(1).max(30).default(7)
});

const alertActionSchema = z.object({
  action: z.enum(['acknowledge']),
  alertId: z.string()
});

// GET - Retrieve performance data
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  // Check admin authentication
  const session = await getServerSession(authOptions);
  if (!session?.user?.email || !isAdminUser(session.user.email)) {
    throw new Error('Unauthorized. Admin access required.');
  }

  const { searchParams } = new URL(request.url);
  const params = performanceQuerySchema.parse(Object.fromEntries(searchParams.entries()));

  switch (params.action) {
    case 'metrics':
      return handleMetricsRequest(params);

    case 'alerts':
      return handleAlertsRequest();

    case 'report':
      return handleReportRequest(params);

    case 'suggestions':
      return handleSuggestionsRequest(params);

    case 'regressions':
      return handleRegressionsRequest(params);

    default:
      throw new Error('Invalid action');
  }
});

// POST - Perform actions (acknowledge alerts, etc.)
export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  // Check admin authentication
  const session = await getServerSession(authOptions);
  if (!session?.user?.email || !isAdminUser(session.user.email)) {
    throw new Error('Unauthorized. Admin access required.');
  }

  const body = await request.json();
  const params = alertActionSchema.parse(body);

  switch (params.action) {
    case 'acknowledge':
      const success = advancedQueryPerformanceMonitor.acknowledgeAlert(params.alertId);
      return NextResponse.json({
        success,
        message: success ? 'Alert acknowledged' : 'Alert not found'
      });

    default:
      throw new Error('Invalid action');
  }
});

/**
 * Handler functions
 */
async function handleMetricsRequest(params: any): Promise<NextResponse> {
  if (params.queryName) {
    const metrics = advancedQueryPerformanceMonitor.getQueryMetrics(
      params.queryName,
      params.limit,
      params.startDate,
      params.endDate
    );
    
    return NextResponse.json({
      success: true,
      data: {
        queryName: params.queryName,
        metrics,
        summary: {
          totalQueries: metrics.length,
          averageExecutionTime: metrics.length > 0 
            ? metrics.reduce((sum, m) => sum + m.executionTime, 0) / metrics.length 
            : 0,
          cacheHitRate: metrics.length > 0 
            ? metrics.filter(m => m.cacheHit).length / metrics.length 
            : 0
        }
      }
    });
  } else {
    // Return recent metrics for all queries
    const endDate = params.endDate || new Date();
    const startDate = params.startDate || new Date(endDate.getTime() - (24 * 60 * 60 * 1000)); // Last 24 hours
    
    const report = advancedQueryPerformanceMonitor.generatePerformanceReport(
      startDate,
      endDate
    );
    
    return NextResponse.json({
      success: true,
      data: report
    });
  }
}

async function handleAlertsRequest(): Promise<NextResponse> {
  const alerts = advancedQueryPerformanceMonitor.getActiveAlerts();
  
  return NextResponse.json({
    success: true,
    data: {
      alerts,
      summary: {
        totalAlerts: alerts.length,
        criticalAlerts: alerts.filter(a => a.severity === 'critical').length,
        warningAlerts: alerts.filter(a => a.severity === 'warning').length
      }
    }
  });
}

async function handleReportRequest(params: any): Promise<NextResponse> {
  const endDate = params.endDate || new Date();
  const startDate = params.startDate || new Date(endDate.getTime() - (7 * 24 * 60 * 60 * 1000)); // Last 7 days
  
  const report = advancedQueryPerformanceMonitor.generatePerformanceReport(
    startDate,
    endDate,
    params.queryName ? [params.queryName] : undefined
  );
  
  return NextResponse.json({
    success: true,
    data: report
  });
}

async function handleSuggestionsRequest(params: any): Promise<NextResponse> {
  if (!params.queryName) {
    return NextResponse.json(
      { success: false, error: 'queryName parameter is required for suggestions' },
      { status: 400 }
    );
  }
  
  const suggestions = advancedQueryPerformanceMonitor.getOptimizationSuggestions(params.queryName);
  
  return NextResponse.json({
    success: true,
    data: {
      queryName: params.queryName,
      suggestions
    }
  });
}

async function handleRegressionsRequest(params: any): Promise<NextResponse> {
  if (!params.queryName) {
    return NextResponse.json(
      { success: false, error: 'queryName parameter is required for regression detection' },
      { status: 400 }
    );
  }
  
  const regression = advancedQueryPerformanceMonitor.detectRegressions(
    params.queryName,
    params.lookbackDays
  );
  
  return NextResponse.json({
    success: true,
    data: {
      queryName: params.queryName,
      lookbackDays: params.lookbackDays,
      regression
    }
  });
}

/**
 * Helper function to check admin access
 */
function isAdminUser(email: string): boolean {
  // Add your admin email check logic here
  const adminEmails = [
    '<EMAIL>',
    '<EMAIL>' // Add your admin email
  ];
  
  return adminEmails.includes(email.toLowerCase());
}
