import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { consolidatedCache } from '@/lib/services/consolidated-cache-service';
import { CacheInvalidationService } from '@/lib/services/cache-invalidation-service';
import { ConsolidatedCacheService } from '@/lib/services/consolidated-cache-service';
import { z } from 'zod';
import { withCSRFProtection } from '@/lib/csrf';

const updateProgressSchema = z.object({
  status: z.enum(['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED']),
  timeSpent: z.number().min(0).optional(),
  score: z.number().min(0).max(100).optional(),
  notes: z.string().max(1000).optional(),
});

// PUT - Update step progress
export const PUT = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string; stepId: string }> }
) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 200 }, // 200 updates per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { id: learningPathId, stepId } = await params;
        const body = await request.json();
        const validation = updateProgressSchema.safeParse(body);
        
        if (!validation.success) {
          return NextResponse.json(
            { 
              success: false, 
              error: 'Invalid request data',
              details: validation.error.errors 
            },
            { status: 400 }
          );
        }

        const { status, timeSpent, score, notes } = validation.data;

        // Verify user is enrolled in the learning path
        const enrollment = await prisma.userLearningPath.findUnique({
          where: {
            userId_learningPathId: {
              userId,
              learningPathId
            }
          }
        });

        if (!enrollment) {
          return NextResponse.json(
            { success: false, error: 'Not enrolled in this learning path' },
            { status: 404 }
          );
        }

        // Get the step and current progress
        const step = await prisma.learningPathStep.findUnique({
          where: { id: stepId },
          include: {
            learningPath: {
              select: { id: true }
            }
          }
        });

        if (!step || step.learningPath.id !== learningPathId) {
          return NextResponse.json(
            { success: false, error: 'Step not found in this learning path' },
            { status: 404 }
          );
        }

        const currentProgress = await prisma.userLearningPathProgress.findUnique({
          where: {
            userId_stepId: {
              userId,
              stepId
            }
          }
        });

        if (!currentProgress) {
          return NextResponse.json(
            { success: false, error: 'Progress record not found' },
            { status: 404 }
          );
        }

        // Prepare update data
        const updateData: any = {
          status,
          ...(timeSpent !== undefined && { timeSpent: currentProgress.timeSpent + timeSpent }),
          ...(score !== undefined && { score }),
          ...(notes !== undefined && { notes }),
        };

        // Set timestamps based on status
        if (status === 'IN_PROGRESS' && !currentProgress.startedAt) {
          updateData.startedAt = new Date();
        } else if (status === 'COMPLETED' && currentProgress.status !== 'COMPLETED') {
          updateData.completedAt = new Date();
          updateData.attempts = currentProgress.attempts + 1;
        }

        // Update step progress
        const updatedProgress = await prisma.userLearningPathProgress.update({
          where: {
            userId_stepId: {
              userId,
              stepId
            }
          },
          data: updateData
        });

        // Recalculate learning path progress
        const allStepProgress = await prisma.userLearningPathProgress.findMany({
          where: {
            userId,
            userLearningPathId: enrollment.id
          }
        });

        const completedSteps = allStepProgress.filter(p => p.status === 'COMPLETED').length;
        const totalSteps = allStepProgress.length;
        const progressPercent = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;
        const totalTimeSpent = allStepProgress.reduce((sum, p) => sum + p.timeSpent, 0);

        // Determine overall learning path status
        let pathStatus = enrollment.status;
        if (completedSteps === totalSteps && totalSteps > 0) {
          pathStatus = 'COMPLETED';
        } else if (completedSteps > 0 || allStepProgress.some(p => p.status === 'IN_PROGRESS')) {
          pathStatus = 'IN_PROGRESS';
        }

        // Find next step if current step is completed
        let currentStepId = enrollment.currentStepId;
        if (status === 'COMPLETED') {
          const nextStep = await prisma.learningPathStep.findFirst({
            where: {
              learningPathId,
              stepOrder: { gt: step.stepOrder }
            },
            orderBy: { stepOrder: 'asc' }
          });
          currentStepId = nextStep?.id || null;
        }

        // Update learning path progress
        const updatedEnrollment = await prisma.userLearningPath.update({
          where: {
            userId_learningPathId: {
              userId,
              learningPathId
            }
          },
          data: {
            status: pathStatus,
            completedSteps,
            progressPercent,
            totalTimeSpent,
            currentStepId,
            lastAccessedAt: new Date(),
            ...(pathStatus === 'COMPLETED' && !enrollment.completedAt && {
              completedAt: new Date()
            }),
            ...(pathStatus === 'IN_PROGRESS' && !enrollment.startedAt && {
              startedAt: new Date()
            }),
          }
        });

        // Update daily learning analytics
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        await prisma.learningAnalytics.upsert({
          where: {
            userId_date: {
              userId,
              date: today
            }
          },
          update: {
            timeSpent: {
              increment: timeSpent || 0
            },
            ...(status === 'COMPLETED' && {
              resourcesViewed: {
                increment: 1
              }
            }),
            date: new Date(),
          },
          create: {
            userId,
            date: today,
            timeSpent: timeSpent || 0,
            resourcesViewed: status === 'COMPLETED' ? 1 : 0,
            pathsProgressed: 0, // This was already incremented on enrollment
          }
        });

        // Update skill progress if step is completed
        if (status === 'COMPLETED' && step.learningPath) {
          const learningPath = await prisma.learningPath.findUnique({
            where: { id: learningPathId },
            include: {
              skills: true
            }
          });

          if (learningPath?.skills.length) {
            for (const skill of learningPath.skills) {
              await prisma.userSkillProgress.upsert({
                where: {
                  userId_skillId: {
                    userId,
                    skillId: skill.id
                  }
                },
                update: {
                  progressPoints: {
                    increment: 10 // Points per completed step
                  },
                  lastPracticed: new Date(),
                  practiceHours: {
                    increment: Math.round((timeSpent || 0) / 60)
                  }
                },
                create: {
                  userId,
                  skillId: skill.id,
                  currentLevel: 'BEGINNER',
                  progressPoints: 10,
                  lastPracticed: new Date(),
                  practiceHours: Math.round((timeSpent || 0) / 60),
                }
              });
            }
          }
        }

        // INTEGRATION FIX: Use new CacheInvalidationService for comprehensive cache invalidation
        try {
          const cacheService = new ConsolidatedCacheService();
          const cacheInvalidationService = new CacheInvalidationService(cacheService);

          // Invalidate learning progress and skill-related caches
          await cacheInvalidationService.smartInvalidate({
            table: 'UserLearningPathProgress',
            operation: 'UPDATE',
            userId,
            data: { stepId, learningPathId, status }
          });

          // Also invalidate skill caches if skills were updated
          if (status === 'COMPLETED' && step.learningPath) {
            const learningPath = await prisma.learningPath.findUnique({
              where: { id: learningPathId },
              include: { skills: true }
            });

            if (learningPath?.skills.length) {
              for (const skill of learningPath.skills) {
                await cacheInvalidationService.invalidateSkillCaches(userId, skill.id);
              }
            }
          }
        } catch (cacheError) {
          console.warn('Failed to invalidate learning path progress caches:', cacheError);
        }

        return NextResponse.json({
          success: true,
          data: {
            stepProgress: updatedProgress,
            pathProgress: {
              status: pathStatus,
              completedSteps,
              totalSteps,
              progressPercent,
              totalTimeSpent,
              currentStepId,
            }
          },
          message: 'Progress updated successfully'
        });
    }
  );
});

// GET - Get step progress
export const GET = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string; stepId: string }> }
) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 300 }, // 300 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { id: learningPathId, stepId } = await params;

        // Build cache key
        const cacheKey = `step_progress:${stepId}:${userId}`;
        
        // Check cache first
        const cached = await consolidatedCache.get<any>(cacheKey);
        if (cached) {
          return NextResponse.json({
            success: true,
            data: cached,
            cached: true
          });
        }

        // Get step progress with step details
        const progress = await prisma.userLearningPathProgress.findUnique({
          where: {
            userId_stepId: {
              userId,
              stepId
            }
          },
          include: {
            step: {
              include: {
                resource: {
                  select: {
                    id: true,
                    title: true,
                    description: true,
                    type: true,
                    url: true,
                    duration: true,
                  }
                }
              }
            }
          }
        });

        if (!progress) {
          return NextResponse.json(
            { success: false, error: 'Progress record not found' },
            { status: 404 }
          );
        }

        // Cache for 1 minute
        await consolidatedCache.set(cacheKey, progress, { ttl: 60000, tags: ['step_progress', stepId, userId] });

      return NextResponse.json({
        success: true,
        data: progress
      });
    }
  );
});
