import NextAuth from "next-auth";
import { authOptions } from "@/lib/auth"; // Import authOptions from the auth configuration file
import { withUnifiedErrorHandling } from "@/lib/unified-api-error-handler";

const handler = NextAuth(authOptions);

// Wrap auth handlers with unified error handling
export const GET = withUnifiedErrorHandling(async (request) => {
  return handler(request);
});

export const POST = withUnifiedErrorHandling(async (request) => {
  return handler(request);
});