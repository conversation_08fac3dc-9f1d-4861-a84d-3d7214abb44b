/**
 * Enhanced Accessibility and Responsive Design Utilities
 * Comprehensive CSS for WCAG 2.1 AA compliance and responsive design
 */

/* ===== ACCESSIBILITY UTILITIES ===== */

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Screen reader only content that becomes visible on focus */
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .high-contrast {
    border: 2px solid currentColor !important;
    background: Canvas !important;
    color: CanvasText !important;
  }
  
  .high-contrast-button {
    border: 3px solid ButtonText !important;
    background: ButtonFace !important;
    color: ButtonText !important;
  }
  
  .high-contrast-button:hover,
  .high-contrast-button:focus {
    background: Highlight !important;
    color: HighlightText !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus indicators */
.focus-visible-enhanced:focus-visible {
  outline: 3px solid hsl(var(--ring));
  outline-offset: 2px;
  border-radius: 4px;
}

/* Skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  border: 2px solid hsl(var(--border));
  z-index: 9999;
  font-weight: 600;
}

.skip-link:focus {
  top: 6px;
}

/* Touch target enhancements */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.touch-target-large {
  min-height: 48px;
  min-width: 48px;
}

/* ===== RESPONSIVE DESIGN UTILITIES ===== */

/* Container queries support */
@container (min-width: 320px) {
  .container-sm\:text-sm {
    font-size: 0.875rem;
  }
}

@container (min-width: 768px) {
  .container-md\:text-base {
    font-size: 1rem;
  }
}

/* Responsive typography scale */
.text-responsive-xs {
  font-size: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
}

.text-responsive-sm {
  font-size: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
}

.text-responsive-base {
  font-size: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
}

.text-responsive-lg {
  font-size: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
}

.text-responsive-xl {
  font-size: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
}

.text-responsive-2xl {
  font-size: clamp(1.5rem, 1.3rem + 1vw, 2rem);
}

.text-responsive-3xl {
  font-size: clamp(1.875rem, 1.6rem + 1.375vw, 2.5rem);
}

/* Responsive spacing */
.space-responsive-sm {
  gap: clamp(0.5rem, 0.4rem + 0.5vw, 0.75rem);
}

.space-responsive-md {
  gap: clamp(0.75rem, 0.6rem + 0.75vw, 1.25rem);
}

.space-responsive-lg {
  gap: clamp(1rem, 0.8rem + 1vw, 1.5rem);
}

.space-responsive-xl {
  gap: clamp(1.5rem, 1.2rem + 1.5vw, 2.5rem);
}

/* Responsive padding */
.p-responsive-sm {
  padding: clamp(0.5rem, 0.4rem + 0.5vw, 0.75rem);
}

.p-responsive-md {
  padding: clamp(0.75rem, 0.6rem + 0.75vw, 1.25rem);
}

.p-responsive-lg {
  padding: clamp(1rem, 0.8rem + 1vw, 1.5rem);
}

.p-responsive-xl {
  padding: clamp(1.5rem, 1.2rem + 1.5vw, 2.5rem);
}

/* Mobile-first responsive utilities */
.mobile-stack {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 768px) {
  .mobile-stack {
    flex-direction: row;
    align-items: center;
  }
}

.mobile-full {
  width: 100%;
}

@media (min-width: 768px) {
  .mobile-full {
    width: auto;
  }
}

/* Grid responsive utilities */
.grid-responsive-1-2-3 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .grid-responsive-1-2-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-responsive-1-2-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

.grid-responsive-1-2-4 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .grid-responsive-1-2-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-responsive-1-2-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
  
  .print-break-inside-avoid {
    page-break-inside: avoid;
  }
  
  /* Ensure good contrast for printing */
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  a[href]:after {
    content: " (" attr(href) ")";
    font-size: 0.8em;
    color: #666;
  }
}

/* ===== DARK MODE ENHANCEMENTS ===== */
@media (prefers-color-scheme: dark) {
  .dark-mode-enhanced {
    color-scheme: dark;
  }
  
  /* Ensure sufficient contrast in dark mode */
  .dark-contrast-text {
    color: #ffffff;
  }
  
  .dark-contrast-bg {
    background-color: #000000;
  }
}

/* ===== ANIMATION UTILITIES ===== */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* ===== UTILITY CLASSES ===== */
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Aspect ratio utilities for responsive media */
.aspect-video {
  aspect-ratio: 16 / 9;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-portrait {
  aspect-ratio: 3 / 4;
}

.aspect-landscape {
  aspect-ratio: 4 / 3;
}
