/**
 * Comprehensive accessibility audit utilities
 */

export interface AccessibilityIssue {
  type: 'error' | 'warning' | 'info';
  rule: string;
  message: string;
  element?: Element;
  severity: 'critical' | 'serious' | 'moderate' | 'minor';
}

export interface AccessibilityAuditResult {
  passed: boolean;
  issues: AccessibilityIssue[];
  score: number; // 0-100
  summary: {
    critical: number;
    serious: number;
    moderate: number;
    minor: number;
  };
}

/**
 * Performs a comprehensive accessibility audit of the current page
 */
export function auditAccessibility(container: Element = document.body): AccessibilityAuditResult {
  const issues: AccessibilityIssue[] = [];

  // Check for missing alt text on images
  const images = container.querySelectorAll('img');
  images.forEach(img => {
    if (!img.hasAttribute('alt') && !img.hasAttribute('aria-label') && !img.hasAttribute('aria-labelledby')) {
      issues.push({
        type: 'error',
        rule: 'img-alt',
        message: 'Image missing alt text',
        element: img,
        severity: 'serious',
      });
    }
  });

  // Check for proper heading hierarchy
  const headings = Array.from(container.querySelectorAll('h1, h2, h3, h4, h5, h6'));
  let lastLevel = 0;
  headings.forEach(heading => {
    const level = parseInt(heading.tagName.charAt(1));
    if (level > lastLevel + 1) {
      issues.push({
        type: 'warning',
        rule: 'heading-hierarchy',
        message: `Heading level ${level} follows heading level ${lastLevel}, skipping levels`,
        element: heading,
        severity: 'moderate',
      });
    }
    lastLevel = level;
  });

  // Check for form labels
  const inputs = container.querySelectorAll('input, select, textarea');
  inputs.forEach(input => {
    const hasLabel = input.hasAttribute('aria-label') || 
                    input.hasAttribute('aria-labelledby') ||
                    container.querySelector(`label[for="${input.id}"]`) ||
                    input.closest('label');
    
    if (!hasLabel) {
      issues.push({
        type: 'error',
        rule: 'form-label',
        message: 'Form control missing accessible label',
        element: input,
        severity: 'critical',
      });
    }
  });

  // Check for sufficient color contrast
  const textElements = container.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6, a, button, label');
  textElements.forEach(element => {
    const styles = window.getComputedStyle(element);
    const color = styles.color;
    const backgroundColor = styles.backgroundColor;
    
    if (color && backgroundColor && color !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'rgba(0, 0, 0, 0)') {
      const contrast = calculateColorContrast(color, backgroundColor);
      const fontSize = parseFloat(styles.fontSize);
      const fontWeight = styles.fontWeight;
      
      const isLargeText = fontSize >= 18 || (fontSize >= 14 && (fontWeight === 'bold' || parseInt(fontWeight) >= 700));
      const requiredContrast = isLargeText ? 3 : 4.5;
      
      if (contrast < requiredContrast) {
        issues.push({
          type: 'error',
          rule: 'color-contrast',
          message: `Insufficient color contrast: ${contrast.toFixed(2)} (required: ${requiredContrast})`,
          element: element,
          severity: 'serious',
        });
      }
    }
  });

  // Check for keyboard accessibility
  const interactiveElements = container.querySelectorAll('button, a, input, select, textarea, [tabindex]');
  interactiveElements.forEach(element => {
    const tabIndex = element.getAttribute('tabindex');
    if (tabIndex && parseInt(tabIndex) > 0) {
      issues.push({
        type: 'warning',
        rule: 'tabindex-positive',
        message: 'Positive tabindex values should be avoided',
        element: element,
        severity: 'moderate',
      });
    }

    // Check for focus indicators
    const styles = window.getComputedStyle(element, ':focus-visible');
    if (!styles.outline || styles.outline === 'none') {
      const hasCustomFocus = styles.boxShadow || styles.border || styles.backgroundColor;
      if (!hasCustomFocus) {
        issues.push({
          type: 'warning',
          rule: 'focus-visible',
          message: 'Interactive element lacks visible focus indicator',
          element: element,
          severity: 'moderate',
        });
      }
    }
  });

  // Check for ARIA attributes
  const elementsWithAria = container.querySelectorAll('[aria-describedby], [aria-labelledby]');
  elementsWithAria.forEach(element => {
    const describedBy = element.getAttribute('aria-describedby');
    const labelledBy = element.getAttribute('aria-labelledby');
    
    if (describedBy) {
      const ids = describedBy.split(' ');
      ids.forEach(id => {
        if (!container.querySelector(`#${id}`)) {
          issues.push({
            type: 'error',
            rule: 'aria-describedby',
            message: `aria-describedby references non-existent element: ${id}`,
            element: element,
            severity: 'serious',
          });
        }
      });
    }
    
    if (labelledBy) {
      const ids = labelledBy.split(' ');
      ids.forEach(id => {
        if (!container.querySelector(`#${id}`)) {
          issues.push({
            type: 'error',
            rule: 'aria-labelledby',
            message: `aria-labelledby references non-existent element: ${id}`,
            element: element,
            severity: 'serious',
          });
        }
      });
    }
  });

  // Check for touch targets
  const touchTargets = container.querySelectorAll('button, a, input, select, textarea, [role="button"]');
  touchTargets.forEach(element => {
    const rect = element.getBoundingClientRect();
    const minSize = 44; // WCAG AA requirement
    
    if (rect.width < minSize || rect.height < minSize) {
      issues.push({
        type: 'warning',
        rule: 'touch-target-size',
        message: `Touch target too small: ${rect.width}x${rect.height}px (minimum: ${minSize}x${minSize}px)`,
        element: element,
        severity: 'moderate',
      });
    }
  });

  // Check for semantic landmarks
  const landmarks = container.querySelectorAll('main, nav, header, footer, aside, section[aria-label], section[aria-labelledby]');
  if (landmarks.length === 0) {
    issues.push({
      type: 'warning',
      rule: 'landmarks',
      message: 'Page lacks semantic landmarks for navigation',
      severity: 'moderate',
    });
  }

  // Calculate summary
  const summary = {
    critical: issues.filter(i => i.severity === 'critical').length,
    serious: issues.filter(i => i.severity === 'serious').length,
    moderate: issues.filter(i => i.severity === 'moderate').length,
    minor: issues.filter(i => i.severity === 'minor').length,
  };

  // Calculate score (0-100)
  const totalIssues = issues.length;
  const weightedScore = summary.critical * 4 + summary.serious * 3 + summary.moderate * 2 + summary.minor * 1;
  const maxPossibleScore = 100;
  const score = Math.max(0, maxPossibleScore - (weightedScore * 2));

  return {
    passed: summary.critical === 0 && summary.serious === 0,
    issues,
    score,
    summary,
  };
}

/**
 * Calculate color contrast ratio between two colors
 */
function calculateColorContrast(color1: string, color2: string): number {
  const rgb1 = parseColor(color1);
  const rgb2 = parseColor(color2);
  
  if (!rgb1 || !rgb2) return 21; // Assume good contrast if we can't parse
  
  const l1 = getRelativeLuminance(rgb1);
  const l2 = getRelativeLuminance(rgb2);
  
  const lighter = Math.max(l1, l2);
  const darker = Math.min(l1, l2);
  
  return (lighter + 0.05) / (darker + 0.05);
}

/**
 * Parse CSS color string to RGB values
 */
function parseColor(color: string): { r: number; g: number; b: number } | null {
  const div = document.createElement('div');
  div.style.color = color;
  document.body.appendChild(div);
  
  const computedColor = window.getComputedStyle(div).color;
  document.body.removeChild(div);
  
  const match = computedColor.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
  if (match) {
    return {
      r: parseInt(match[1]),
      g: parseInt(match[2]),
      b: parseInt(match[3]),
    };
  }
  
  return null;
}

/**
 * Calculate relative luminance of an RGB color
 */
function getRelativeLuminance({ r, g, b }: { r: number; g: number; b: number }): number {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}

/**
 * Test keyboard navigation for a container
 */
export function testKeyboardNavigation(container: Element = document.body): AccessibilityIssue[] {
  const issues: AccessibilityIssue[] = [];
  const focusableElements = container.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );

  if (focusableElements.length === 0) {
    issues.push({
      type: 'warning',
      rule: 'keyboard-navigation',
      message: 'No focusable elements found',
      severity: 'moderate',
    });
    return issues;
  }

  // Test tab order
  const tabOrder: Element[] = [];
  focusableElements.forEach(element => {
    const tabIndex = element.getAttribute('tabindex');
    if (!tabIndex || parseInt(tabIndex) >= 0) {
      tabOrder.push(element);
    }
  });

  // Sort by tabindex
  tabOrder.sort((a, b) => {
    const aIndex = parseInt(a.getAttribute('tabindex') || '0');
    const bIndex = parseInt(b.getAttribute('tabindex') || '0');
    return aIndex - bIndex;
  });

  return issues;
}

/**
 * Generate accessibility report
 */
export function generateAccessibilityReport(result: AccessibilityAuditResult): string {
  const { score, summary, issues } = result;
  
  let report = `Accessibility Audit Report\n`;
  report += `==========================\n\n`;
  report += `Overall Score: ${score}/100\n`;
  report += `Status: ${result.passed ? 'PASSED' : 'FAILED'}\n\n`;
  
  report += `Issue Summary:\n`;
  report += `- Critical: ${summary.critical}\n`;
  report += `- Serious: ${summary.serious}\n`;
  report += `- Moderate: ${summary.moderate}\n`;
  report += `- Minor: ${summary.minor}\n\n`;
  
  if (issues.length > 0) {
    report += `Detailed Issues:\n`;
    report += `================\n\n`;
    
    issues.forEach((issue, index) => {
      report += `${index + 1}. [${issue.severity.toUpperCase()}] ${issue.rule}\n`;
      report += `   ${issue.message}\n`;
      if (issue.element) {
        report += `   Element: ${issue.element.tagName.toLowerCase()}`;
        if (issue.element.id) report += `#${issue.element.id}`;
        if (issue.element.className) report += `.${issue.element.className.split(' ').join('.')}`;
        report += `\n`;
      }
      report += `\n`;
    });
  }
  
  return report;
}
