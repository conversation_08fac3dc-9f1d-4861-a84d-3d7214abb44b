'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>ert<PERSON>riangle, RefreshCw, Home, Shield, Clock, Wifi, WifiOff } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Unified error boundary configuration
export interface UnifiedErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  context?: string;
  maxRetries?: number;
  retryDelay?: number;
  autoRetry?: boolean;
  enableRecovery?: boolean;
  gracefulDegradation?: ReactNode;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
  showErrorDetails?: boolean;
  enableReporting?: boolean;
}

interface UnifiedErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
  retryCount: number;
  lastRetryTime: number;
  isRecovering: boolean;
  recoveryStrategy: string | null;
}

// Error classification for different recovery strategies
enum ErrorType {
  NETWORK = 'network',
  STATE = 'state',
  MEMORY = 'memory',
  PERMISSION = 'permission',
  TIMEOUT = 'timeout',
  UNKNOWN = 'unknown'
}

interface RecoveryStrategy {
  type: ErrorType;
  condition: (error: Error) => boolean;
  action: (boundary: UnifiedErrorBoundary) => void;
  delay: number;
  maxRetries: number;
}

/**
 * Unified Error Boundary Component
 * 
 * Consolidates all error boundary functionality into a single, comprehensive component
 * that provides automatic recovery, retry logic, graceful degradation, and specialized
 * error handling for different contexts.
 */
export class UnifiedErrorBoundary extends Component<UnifiedErrorBoundaryProps, UnifiedErrorBoundaryState> {
  private retryTimeoutId: NodeJS.Timeout | null = null;
  private recoveryStrategies: RecoveryStrategy[];

  constructor(props: UnifiedErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      retryCount: 0,
      lastRetryTime: 0,
      isRecovering: false,
      recoveryStrategy: null
    };

    // Initialize recovery strategies
    this.recoveryStrategies = this.initializeRecoveryStrategies();
  }

  private initializeRecoveryStrategies(): RecoveryStrategy[] {
    return [
      {
        type: ErrorType.NETWORK,
        condition: (error) => 
          error.message.includes('fetch') || 
          error.message.includes('network') ||
          error.message.includes('Failed to fetch') ||
          error.name === 'NetworkError',
        action: (boundary) => boundary.handleNetworkErrorRecovery(),
        delay: 2000,
        maxRetries: 3
      },
      {
        type: ErrorType.STATE,
        condition: (error) =>
          error.message.includes('state') ||
          error.message.includes('undefined') ||
          error.message.includes('Cannot read property'),
        action: (boundary) => boundary.handleStateErrorRecovery(),
        delay: 1000,
        maxRetries: 2
      },
      {
        type: ErrorType.MEMORY,
        condition: (error) =>
          error.message.includes('memory') ||
          error.message.includes('heap') ||
          error.name === 'RangeError',
        action: (boundary) => boundary.handleMemoryErrorRecovery(),
        delay: 3000,
        maxRetries: 1
      },
      {
        type: ErrorType.PERMISSION,
        condition: (error) =>
          error.message.includes('permission') ||
          error.message.includes('unauthorized') ||
          error.message.includes('403') ||
          error.message.includes('401'),
        action: (boundary) => boundary.handlePermissionErrorRecovery(),
        delay: 1000,
        maxRetries: 1
      },
      {
        type: ErrorType.TIMEOUT,
        condition: (error) =>
          error.message.includes('timeout') ||
          error.message.includes('AbortError') ||
          error.name === 'TimeoutError',
        action: (boundary) => boundary.handleTimeoutErrorRecovery(),
        delay: 2000,
        maxRetries: 2
      }
    ];
  }

  static getDerivedStateFromError(error: Error): Partial<UnifiedErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { context = 'unknown', enableReporting = true, enableRecovery = true } = this.props;

    console.error(`[UnifiedErrorBoundary:${context}] Error caught:`, error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
      lastRetryTime: Date.now()
    });

    // Report error to monitoring services
    if (enableReporting) {
      this.reportError(error, errorInfo);
    }

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Attempt automatic recovery if enabled
    if (enableRecovery) {
      this.attemptRecovery(error);
    }
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    const { context = 'unknown' } = this.props;
    
    try {
      const errorReport = {
        message: error.message,
        stack: error.stack,
        name: error.name,
        componentStack: errorInfo.componentStack,
        errorId: this.state.errorId,
        context,
        timestamp: new Date().toISOString(),
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'server',
        url: typeof window !== 'undefined' ? window.location.href : 'server',
        retryCount: this.state.retryCount
      };

      // Log to console for development
      console.error('Error Report:', errorReport);
      
      // Send to monitoring service in production
      if (process.env.NODE_ENV === 'production') {
        // Sentry integration
        if (typeof window !== 'undefined' && (window as any).Sentry) {
          (window as any).Sentry.captureException(error, {
            tags: { 
              context,
              errorBoundary: 'unified',
              retryCount: this.state.retryCount.toString()
            },
            extra: errorReport
          });
        }
        
        // Custom error tracking service
        this.sendToErrorService(errorReport);
      }
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  private sendToErrorService = async (errorReport: any) => {
    try {
      // Send to custom error tracking endpoint
      await fetch('/api/error-tracking', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(errorReport)
      });
    } catch (error) {
      console.error('Failed to send error to tracking service:', error);
    }
  };

  private attemptRecovery = (error: Error) => {
    const { maxRetries = 3, enableRecovery = true } = this.props;
    
    if (!enableRecovery || this.state.retryCount >= maxRetries) {
      return;
    }

    // Find appropriate recovery strategy
    const strategy = this.recoveryStrategies.find(s => s.condition(error));
    
    if (strategy && this.state.retryCount < strategy.maxRetries) {
      console.log(`[UnifiedErrorBoundary] Attempting ${strategy.type} recovery for error: ${error.message}`);
      
      this.setState({
        isRecovering: true,
        recoveryStrategy: strategy.type
      });

      // Execute recovery strategy with delay
      this.retryTimeoutId = setTimeout(() => {
        strategy.action(this);
      }, strategy.delay * (this.state.retryCount + 1)); // Exponential backoff
    }
  };

  private handleNetworkErrorRecovery = () => {
    // Check network connectivity
    if (typeof navigator !== 'undefined' && !navigator.onLine) {
      // Wait for network to come back online
      const handleOnline = () => {
        window.removeEventListener('online', handleOnline);
        this.performRetry();
      };
      window.addEventListener('online', handleOnline);
    } else {
      this.performRetry();
    }
  };

  private handleStateErrorRecovery = () => {
    // Clear any cached state and retry
    if (typeof window !== 'undefined') {
      // Clear session storage for this component
      const storageKey = `error_recovery_${this.props.context}`;
      sessionStorage.removeItem(storageKey);
    }
    this.performRetry();
  };

  private handleMemoryErrorRecovery = () => {
    // Force garbage collection if available and retry
    if (typeof window !== 'undefined' && (window as any).gc) {
      (window as any).gc();
    }
    this.performRetry();
  };

  private handlePermissionErrorRecovery = () => {
    // For permission errors, we typically can't auto-recover
    // but we can provide user guidance
    this.setState({ isRecovering: false });
  };

  private handleTimeoutErrorRecovery = () => {
    // Retry with longer timeout
    this.performRetry();
  };

  private performRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1,
      isRecovering: false,
      recoveryStrategy: null,
      errorId: ''
    }));
  };

  private handleManualRetry = () => {
    this.setState({
      retryCount: 0
    });
    this.performRetry();
  };

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  private getErrorIcon = (errorType: string) => {
    switch (errorType) {
      case ErrorType.NETWORK:
        return <WifiOff className="h-5 w-5" />;
      case ErrorType.PERMISSION:
        return <Shield className="h-5 w-5" />;
      case ErrorType.TIMEOUT:
        return <Clock className="h-5 w-5" />;
      default:
        return <AlertTriangle className="h-5 w-5" />;
    }
  };

  private getErrorTitle = (errorType: string, context: string) => {
    const contextTitle = context.charAt(0).toUpperCase() + context.slice(1);

    switch (errorType) {
      case ErrorType.NETWORK:
        return `${contextTitle} Network Error`;
      case ErrorType.PERMISSION:
        return `${contextTitle} Permission Error`;
      case ErrorType.TIMEOUT:
        return `${contextTitle} Timeout Error`;
      case ErrorType.STATE:
        return `${contextTitle} State Error`;
      case ErrorType.MEMORY:
        return `${contextTitle} Memory Error`;
      default:
        return `${contextTitle} Error`;
    }
  };

  private getErrorMessage = (error: Error, errorType: string) => {
    switch (errorType) {
      case ErrorType.NETWORK:
        return 'Unable to connect to the service. Please check your internet connection and try again.';
      case ErrorType.PERMISSION:
        return 'You don\'t have permission to access this resource. Please contact support if this persists.';
      case ErrorType.TIMEOUT:
        return 'The request took too long to complete. Please try again.';
      case ErrorType.STATE:
        return 'There was an issue with the component state. Retrying should resolve this.';
      case ErrorType.MEMORY:
        return 'The application is running low on memory. Please close other tabs and try again.';
      default:
        return error.message || 'An unexpected error occurred. Please try again.';
    }
  };

  private classifyError = (error: Error): ErrorType => {
    for (const strategy of this.recoveryStrategies) {
      if (strategy.condition(error)) {
        return strategy.type;
      }
    }
    return ErrorType.UNKNOWN;
  };

  render() {
    if (this.state.hasError && this.state.error) {
      const {
        fallback,
        context = 'unknown',
        maxRetries = 3,
        showErrorDetails = false,
        gracefulDegradation
      } = this.props;

      // Use custom fallback if provided
      if (fallback) {
        return fallback;
      }

      // Use graceful degradation if available and retries exhausted
      if (gracefulDegradation && this.state.retryCount >= maxRetries) {
        return gracefulDegradation;
      }

      const errorType = this.classifyError(this.state.error);
      const canRetry = this.state.retryCount < maxRetries;
      const isNetworkOffline = typeof navigator !== 'undefined' && !navigator.onLine;

      return (
        <Card className="mt-6 border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-700 dark:text-red-400">
              {this.getErrorIcon(errorType)}
              {this.getErrorTitle(errorType, context)}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-red-600 dark:text-red-300">
              <p className="font-medium">
                {this.getErrorMessage(this.state.error, errorType)}
              </p>

              {showErrorDetails && (
                <details className="mt-2">
                  <summary className="text-sm cursor-pointer hover:underline">
                    Technical Details
                  </summary>
                  <div className="mt-2 p-2 bg-red-100 dark:bg-red-900/40 rounded text-xs font-mono">
                    <p><strong>Error:</strong> {this.state.error.message}</p>
                    <p><strong>Error ID:</strong> {this.state.errorId}</p>
                    <p><strong>Retry Count:</strong> {this.state.retryCount}/{maxRetries}</p>
                    {this.state.recoveryStrategy && (
                      <p><strong>Recovery Strategy:</strong> {this.state.recoveryStrategy}</p>
                    )}
                  </div>
                </details>
              )}
            </div>

            {/* Network status indicator */}
            {errorType === ErrorType.NETWORK && (
              <div className="flex items-center gap-2 text-sm">
                {isNetworkOffline ? (
                  <>
                    <WifiOff className="h-4 w-4 text-red-500" />
                    <span className="text-red-600 dark:text-red-300">
                      You appear to be offline
                    </span>
                  </>
                ) : (
                  <>
                    <Wifi className="h-4 w-4 text-green-500" />
                    <span className="text-green-600 dark:text-green-300">
                      Network connection detected
                    </span>
                  </>
                )}
              </div>
            )}

            {/* Recovery status */}
            {this.state.isRecovering && (
              <div className="flex items-center gap-2 text-sm text-blue-600 dark:text-blue-300">
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span>Attempting automatic recovery...</span>
              </div>
            )}

            {/* Action buttons */}
            <div className="flex gap-2 flex-wrap">
              {canRetry && !this.state.isRecovering && (
                <Button
                  onClick={this.handleManualRetry}
                  variant="outline"
                  size="sm"
                  className="border-red-300 text-red-700 hover:bg-red-100 dark:border-red-600 dark:text-red-300 dark:hover:bg-red-900/40"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again ({maxRetries - this.state.retryCount} left)
                </Button>
              )}

              <Link href="/">
                <Button
                  variant="outline"
                  size="sm"
                  className="border-gray-300 text-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </Button>
              </Link>

              {errorType === ErrorType.PERMISSION && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.location.href = '/auth/signin'}
                  className="border-blue-300 text-blue-700 hover:bg-blue-100 dark:border-blue-600 dark:text-blue-300 dark:hover:bg-blue-900/40"
                >
                  <Shield className="h-4 w-4 mr-2" />
                  Sign In
                </Button>
              )}
            </div>

            {/* Additional help text */}
            {this.state.retryCount >= maxRetries && (
              <div className="text-sm text-gray-600 dark:text-gray-400 border-t pt-3">
                <p>If this problem persists, please:</p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Refresh the page</li>
                  <li>Clear your browser cache</li>
                  <li>Contact support with Error ID: {this.state.errorId}</li>
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

// Hook for unified error handling in functional components
export function useUnifiedErrorHandler(context = 'component') {
  const handleError = React.useCallback((error: Error, errorInfo?: ErrorInfo) => {
    console.error(`[UnifiedErrorHandler:${context}] Error:`, error, errorInfo);

    // Report to monitoring service
    if (process.env.NODE_ENV === 'production') {
      if (typeof window !== 'undefined' && (window as any).Sentry) {
        (window as any).Sentry.captureException(error, {
          tags: {
            context,
            errorHandler: 'unified',
            component: 'hook'
          },
          extra: errorInfo
        });
      }
    }
  }, [context]);

  const recoverFromError = React.useCallback(async (
    operation: () => Promise<any>,
    fallback?: any,
    maxRetries = 2
  ): Promise<any> => {
    let retryCount = 0;

    while (retryCount <= maxRetries) {
      try {
        return await operation();
      } catch (error) {
        retryCount++;

        if (retryCount > maxRetries) {
          handleError(error instanceof Error ? error : new Error(String(error)));
          return fallback;
        }

        // Wait before retry with exponential backoff
        await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
      }
    }

    return fallback;
  }, [handleError]);

  return {
    handleError,
    recoverFromError
  };
}

// Higher-order component wrapper
export function withUnifiedErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<UnifiedErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <UnifiedErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </UnifiedErrorBoundary>
  );

  WrappedComponent.displayName = `withUnifiedErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

// Specialized error boundary presets for different contexts
export const InterviewPracticeErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <UnifiedErrorBoundary
    context="interview-practice"
    maxRetries={3}
    autoRetry={true}
    enableRecovery={true}
    showErrorDetails={false}
  >
    {children}
  </UnifiedErrorBoundary>
);

export const AIInsightsErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <UnifiedErrorBoundary
    context="ai-insights"
    maxRetries={2}
    autoRetry={false}
    enableRecovery={true}
    showErrorDetails={false}
  >
    {children}
  </UnifiedErrorBoundary>
);

export const AssessmentErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <UnifiedErrorBoundary
    context="assessment"
    maxRetries={3}
    autoRetry={true}
    enableRecovery={true}
    showErrorDetails={false}
  >
    {children}
  </UnifiedErrorBoundary>
);

export const DashboardErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <UnifiedErrorBoundary
    context="dashboard"
    maxRetries={2}
    autoRetry={false}
    enableRecovery={true}
    showErrorDetails={false}
    gracefulDegradation={
      <Card className="mt-6 border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-800">
        <CardContent className="p-6 text-center">
          <AlertTriangle className="h-8 w-8 text-yellow-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-200 mb-2">
            Dashboard Temporarily Unavailable
          </h3>
          <p className="text-yellow-700 dark:text-yellow-300 mb-4">
            Some dashboard features are currently experiencing issues. Basic functionality is still available.
          </p>
          <Link href="/profile">
            <Button variant="outline" size="sm">
              Go to Profile
            </Button>
          </Link>
        </CardContent>
      </Card>
    }
  >
    {children}
  </UnifiedErrorBoundary>
);

// Network-aware error boundary for components that depend on connectivity
export const NetworkAwareErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <UnifiedErrorBoundary
    context="network-dependent"
    maxRetries={5}
    autoRetry={true}
    enableRecovery={true}
    showErrorDetails={false}
  >
    {children}
  </UnifiedErrorBoundary>
);

export default UnifiedErrorBoundary;
