# Error Boundary Migration Guide

## Overview

This guide helps migrate from the old scattered error boundary implementations to the new unified error boundary system.

## Old vs New Components

### Before (Multiple Components)
- `ErrorBoundary.tsx` - Basic error boundary
- `EnhancedErrorBoundary.tsx` - Advanced error boundary with recovery
- `InterviewPracticeErrorBoundary.tsx` - Interview-specific error boundary
- `AIInsightsErrorBoundary.tsx` - AI insights error boundary
- `common/ErrorBoundary.tsx` - Another basic error boundary

### After (Unified System)
- `UnifiedErrorBoundary` - Single comprehensive error boundary
- Context-specific presets for different use cases
- Consistent API and behavior across all contexts

## Migration Steps

### 1. Replace Basic Error Boundaries

**Before:**
```tsx
import ErrorBoundary from '@/components/ErrorBoundary';

<ErrorBoundary>
  <MyComponent />
</ErrorBoundary>
```

**After:**
```tsx
import { UnifiedErrorBoundary } from '@/components/unified-error-boundary';

<UnifiedErrorBoundary context="my-component">
  <MyComponent />
</UnifiedErrorBoundary>
```

### 2. Replace Enhanced Error Boundaries

**Before:**
```tsx
import EnhancedErrorBoundary from '@/components/EnhancedErrorBoundary';

<EnhancedErrorBoundary 
  maxRetries={3}
  autoRetry={true}
  context="dashboard"
>
  <Dashboard />
</EnhancedErrorBoundary>
```

**After:**
```tsx
import { DashboardErrorBoundary } from '@/components/unified-error-boundary';

<DashboardErrorBoundary>
  <Dashboard />
</DashboardErrorBoundary>
```

### 3. Replace Specialized Error Boundaries

**Before:**
```tsx
import { InterviewPracticeErrorBoundary } from '@/components/error-boundary/InterviewPracticeErrorBoundary';

<InterviewPracticeErrorBoundary>
  <InterviewSession />
</InterviewPracticeErrorBoundary>
```

**After:**
```tsx
import { InterviewPracticeErrorBoundary } from '@/components/unified-error-boundary';

<InterviewPracticeErrorBoundary>
  <InterviewSession />
</InterviewPracticeErrorBoundary>
```

### 4. Replace Error Hooks

**Before:**
```tsx
import { useErrorHandler } from '@/components/ErrorBoundary';
import { useEnhancedErrorHandler } from '@/components/EnhancedErrorBoundary';

const { handleError } = useErrorHandler();
const { recoverFromError } = useEnhancedErrorHandler('context');
```

**After:**
```tsx
import { useUnifiedErrorHandler } from '@/components/unified-error-boundary';

const { handleError, recoverFromError } = useUnifiedErrorHandler('context');
```

### 5. Replace HOC Wrappers

**Before:**
```tsx
import { withErrorBoundary } from '@/components/common/ErrorBoundary';

export default withErrorBoundary(MyComponent, { context: 'my-component' });
```

**After:**
```tsx
import { withUnifiedErrorBoundary } from '@/components/unified-error-boundary';

export default withUnifiedErrorBoundary(MyComponent, { context: 'my-component' });
```

## Available Presets

The unified system provides several pre-configured error boundaries:

- `InterviewPracticeErrorBoundary` - For interview practice components
- `AIInsightsErrorBoundary` - For AI-powered components
- `AssessmentErrorBoundary` - For assessment-related components
- `DashboardErrorBoundary` - For dashboard components with graceful degradation
- `NetworkAwareErrorBoundary` - For network-dependent components

## Configuration Options

The `UnifiedErrorBoundary` supports all features from the old components:

```tsx
<UnifiedErrorBoundary
  context="my-feature"           // Error context for tracking
  maxRetries={3}                 // Maximum retry attempts
  autoRetry={true}               // Enable automatic retries
  enableRecovery={true}          // Enable smart recovery strategies
  showErrorDetails={false}       // Show technical error details
  enableReporting={true}         // Enable error reporting
  gracefulDegradation={<Fallback />} // Fallback UI when retries exhausted
  onError={(error, info) => {}}  // Custom error handler
  fallback={<CustomError />}     // Custom error UI
>
  <MyComponent />
</UnifiedErrorBoundary>
```

## Benefits of Migration

1. **Consistency** - Single error handling pattern across the app
2. **Reduced Bundle Size** - Eliminates duplicate error boundary code
3. **Better Recovery** - Improved automatic recovery strategies
4. **Enhanced Monitoring** - Better error tracking and reporting
5. **Maintainability** - Single component to maintain and update

## Files to Remove After Migration

Once migration is complete, these files can be safely removed:

- `src/components/ErrorBoundary.tsx`
- `src/components/EnhancedErrorBoundary.tsx`
- `src/components/error-boundary/InterviewPracticeErrorBoundary.tsx`
- `src/components/assessment/AIInsightsErrorBoundary.tsx`
- `src/components/common/ErrorBoundary.tsx`

## Testing

After migration, test the following scenarios:

1. **Network Errors** - Disconnect network and verify recovery
2. **Permission Errors** - Test with invalid auth tokens
3. **State Errors** - Trigger component state errors
4. **Memory Errors** - Test with large data sets
5. **Timeout Errors** - Test with slow API responses

## Support

If you encounter issues during migration, check:

1. Import paths are correct
2. Context names are meaningful
3. Error boundaries are placed at appropriate component levels
4. Custom error handlers are properly migrated
