/**
 * Unified Error Boundary Module
 * 
 * Exports all error boundary components and utilities from a single module.
 * This replaces the multiple scattered error boundary implementations.
 */

export {
  UnifiedErrorBoundary,
  type UnifiedErrorBoundaryProps,
  useUnifiedErrorHandler,
  withUnifiedErrorBoundary,
  InterviewPracticeErrorBoundary,
  AIInsightsErrorBoundary,
  AssessmentErrorBoundary,
  DashboardErrorBoundary,
  NetworkAwareErrorBoundary
} from './UnifiedErrorBoundary';

// Re-export default for convenience
export { default } from './UnifiedErrorBoundary';

// Legacy compatibility exports (to ease migration)
export { InterviewPracticeErrorBoundary as InterviewErrorBoundary } from './UnifiedErrorBoundary';
export { UnifiedErrorBoundary as ErrorBoundary } from './UnifiedErrorBoundary';
export { UnifiedErrorBoundary as EnhancedErrorBoundary } from './UnifiedErrorBoundary';
