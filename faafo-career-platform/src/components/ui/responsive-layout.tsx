'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { useResponsiveDesign, useResponsiveLayout, useResponsiveTypography } from '@/hooks/useResponsiveDesign';
import { useTouchDevice } from '@/hooks/useResponsiveDesign';

export interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  as?: keyof React.JSX.IntrinsicElements;
}

/**
 * Responsive container with automatic padding and max-width
 */
export function ResponsiveContainer({
  children,
  className,
  maxWidth = 'xl',
  padding = 'md',
  as: Component = 'div',
}: ResponsiveContainerProps) {
  const { isMobile, isTablet } = useResponsiveDesign();

  const maxWidthClass = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full',
  }[maxWidth];

  const paddingClass = React.useMemo(() => {
    if (padding === 'none') return '';
    
    const paddingMap = {
      sm: isMobile ? 'px-3 py-2' : 'px-4 py-3',
      md: isMobile ? 'px-4 py-3' : isTablet ? 'px-6 py-4' : 'px-8 py-6',
      lg: isMobile ? 'px-6 py-4' : isTablet ? 'px-8 py-6' : 'px-12 py-8',
    };
    
    return paddingMap[padding];
  }, [padding, isMobile, isTablet]);

  return (
    <Component
      className={cn(
        'mx-auto w-full',
        maxWidthClass,
        paddingClass,
        className
      )}
    >
      {children}
    </Component>
  );
}

export interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
  gap?: 'sm' | 'md' | 'lg';
  as?: keyof React.JSX.IntrinsicElements;
}

/**
 * Responsive grid with breakpoint-specific column counts
 */
export function ResponsiveGrid({
  children,
  className,
  cols = { xs: 1, sm: 2, md: 3, lg: 4 },
  gap = 'md',
  as: Component = 'div',
}: ResponsiveGridProps) {
  const { currentBreakpoint } = useResponsiveDesign();

  const gapClass = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
  }[gap];

  const gridColsClass = React.useMemo(() => {
    const breakpointOrder = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'] as const;
    
    for (const bp of breakpointOrder) {
      if (cols[bp] && (currentBreakpoint === bp || 
          (bp === '2xl' && currentBreakpoint === '2xl') ||
          (bp === 'xl' && ['xl', '2xl'].includes(currentBreakpoint)) ||
          (bp === 'lg' && ['lg', 'xl', '2xl'].includes(currentBreakpoint)) ||
          (bp === 'md' && ['md', 'lg', 'xl', '2xl'].includes(currentBreakpoint)) ||
          (bp === 'sm' && ['sm', 'md', 'lg', 'xl', '2xl'].includes(currentBreakpoint)))) {
        return `grid-cols-${cols[bp]}`;
      }
    }
    
    return 'grid-cols-1';
  }, [cols, currentBreakpoint]);

  return (
    <Component
      className={cn(
        'grid',
        gridColsClass,
        gapClass,
        className
      )}
    >
      {children}
    </Component>
  );
}

export interface ResponsiveStackProps {
  children: React.ReactNode;
  className?: string;
  direction?: {
    xs?: 'row' | 'col';
    sm?: 'row' | 'col';
    md?: 'row' | 'col';
    lg?: 'row' | 'col';
    xl?: 'row' | 'col';
    '2xl'?: 'row' | 'col';
  };
  gap?: 'sm' | 'md' | 'lg';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  as?: keyof React.JSX.IntrinsicElements;
}

/**
 * Responsive flex stack with breakpoint-specific directions
 */
export function ResponsiveStack({
  children,
  className,
  direction = { xs: 'col', md: 'row' },
  gap = 'md',
  align = 'start',
  justify = 'start',
  as: Component = 'div',
}: ResponsiveStackProps) {
  const { currentBreakpoint } = useResponsiveDesign();

  const gapClass = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
  }[gap];

  const alignClass = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch',
  }[align];

  const justifyClass = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly',
  }[justify];

  const directionClass = React.useMemo(() => {
    const breakpointOrder = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'] as const;
    
    for (const bp of breakpointOrder) {
      if (direction[bp] && (currentBreakpoint === bp || 
          (bp === '2xl' && currentBreakpoint === '2xl') ||
          (bp === 'xl' && ['xl', '2xl'].includes(currentBreakpoint)) ||
          (bp === 'lg' && ['lg', 'xl', '2xl'].includes(currentBreakpoint)) ||
          (bp === 'md' && ['md', 'lg', 'xl', '2xl'].includes(currentBreakpoint)) ||
          (bp === 'sm' && ['sm', 'md', 'lg', 'xl', '2xl'].includes(currentBreakpoint)))) {
        return direction[bp] === 'row' ? 'flex-row' : 'flex-col';
      }
    }
    
    return 'flex-col';
  }, [direction, currentBreakpoint]);

  return (
    <Component
      className={cn(
        'flex',
        directionClass,
        gapClass,
        alignClass,
        justifyClass,
        className
      )}
    >
      {children}
    </Component>
  );
}

export interface ResponsiveTextProps {
  children: React.ReactNode;
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';
  size?: {
    xs?: string;
    sm?: string;
    md?: string;
    lg?: string;
    xl?: string;
    '2xl'?: string;
  };
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  color?: 'default' | 'muted' | 'accent' | 'destructive';
}

/**
 * Responsive text with breakpoint-specific sizing
 */
export function ResponsiveText({
  children,
  className,
  as: Component = 'p',
  size = { xs: 'text-sm', md: 'text-base' },
  weight = 'normal',
  color = 'default',
}: ResponsiveTextProps) {
  const { getResponsiveValue } = useResponsiveDesign();

  const sizeClass = getResponsiveValue(size) || 'text-base';
  
  const weightClass = {
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold',
  }[weight];

  const colorClass = {
    default: 'text-foreground',
    muted: 'text-muted-foreground',
    accent: 'text-accent-foreground',
    destructive: 'text-destructive',
  }[color];

  return (
    <Component
      className={cn(
        sizeClass,
        weightClass,
        colorClass,
        className
      )}
    >
      {children}
    </Component>
  );
}

export interface TouchOptimizedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  'aria-label'?: string;
  'data-testid'?: string;
}

/**
 * Touch-optimized button with appropriate sizing for mobile devices
 */
export function TouchOptimizedButton({
  children,
  onClick,
  className,
  variant = 'default',
  size = 'md',
  disabled = false,
  'aria-label': ariaLabel,
  'data-testid': testId,
}: TouchOptimizedButtonProps) {
  const { isTouchDevice } = useTouchDevice();
  const { isMobile } = useResponsiveDesign();

  const sizeClass = React.useMemo(() => {
    // Increase touch targets on mobile/touch devices
    const touchMultiplier = isTouchDevice || isMobile ? 1.2 : 1;
    
    const sizes = {
      sm: `h-${Math.ceil(10 * touchMultiplier)} px-${Math.ceil(3 * touchMultiplier)} min-h-[${Math.ceil(44 * touchMultiplier)}px]`,
      md: `h-${Math.ceil(11 * touchMultiplier)} px-${Math.ceil(4 * touchMultiplier)} min-h-[${Math.ceil(44 * touchMultiplier)}px]`,
      lg: `h-${Math.ceil(12 * touchMultiplier)} px-${Math.ceil(6 * touchMultiplier)} min-h-[${Math.ceil(48 * touchMultiplier)}px]`,
    };
    
    return sizes[size];
  }, [size, isTouchDevice, isMobile]);

  const variantClass = {
    default: 'bg-primary text-primary-foreground hover:bg-primary/90',
    outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
  }[variant];

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      aria-label={ariaLabel}
      data-testid={testId}
      className={cn(
        'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
        'disabled:pointer-events-none disabled:opacity-50',
        sizeClass,
        variantClass,
        className
      )}
    >
      {children}
    </button>
  );
}
