'use client';

import React from 'react';
import { Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>f<PERSON><PERSON><PERSON>, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface PageLoadingStateProps {
  message?: string;
  showSpinner?: boolean;
  className?: string;
}

export function PageLoadingState({ 
  message = 'Loading...', 
  showSpinner = true, 
  className 
}: PageLoadingStateProps) {
  return (
    <div className={cn('min-h-screen flex items-center justify-center', className)}>
      <div className="text-center">
        {showSpinner && (
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
        )}
        <p className="text-muted-foreground">{message}</p>
      </div>
    </div>
  );
}

interface PageErrorStateProps {
  title?: string;
  message?: string;
  showRetry?: boolean;
  showHome?: boolean;
  onRetry?: () => void;
  className?: string;
}

export function PageErrorState({
  title = 'Something went wrong',
  message = 'There was an error loading this page. Please try again.',
  showRetry = true,
  showHome = true,
  onRetry,
  className
}: PageErrorStateProps) {
  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  return (
    <div className={cn('min-h-screen flex items-center justify-center p-6', className)}>
      <Card className="max-w-md w-full">
        <CardHeader className="text-center">
          <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <CardTitle className="text-destructive">{title}</CardTitle>
          <CardDescription>{message}</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col gap-3">
          {showRetry && (
            <Button onClick={handleRetry} variant="outline" className="w-full">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          )}
          {showHome && (
            <Button asChild variant="default" className="w-full">
              <Link href="/">
                <Home className="h-4 w-4 mr-2" />
                Go Home
              </Link>
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

interface DashboardLoadingStateProps {
  className?: string;
}

export function DashboardLoadingState({ className }: DashboardLoadingStateProps) {
  return (
    <div className={cn('max-w-7xl mx-auto p-6 space-y-6', className)}>
      {/* Header skeleton */}
      <div className="space-y-2">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-4 w-96" />
      </div>

      {/* Stats cards skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="space-y-3">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-3 w-24" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main content skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent className="space-y-3">
              {Array.from({ length: 4 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

interface FormLoadingStateProps {
  message?: string;
  className?: string;
}

export function FormLoadingState({ 
  message = 'Loading form...', 
  className 
}: FormLoadingStateProps) {
  return (
    <div className={cn('max-w-2xl mx-auto p-6', className)}>
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
        <CardContent className="space-y-6">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-10 w-full" />
            </div>
          ))}
          <div className="flex justify-end space-x-3">
            <Skeleton className="h-10 w-20" />
            <Skeleton className="h-10 w-24" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface ListLoadingStateProps {
  itemCount?: number;
  className?: string;
}

export function ListLoadingState({ 
  itemCount = 6, 
  className 
}: ListLoadingStateProps) {
  return (
    <div className={cn('space-y-4', className)}>
      {Array.from({ length: itemCount }).map((_, i) => (
        <Card key={i}>
          <CardContent className="p-4">
            <div className="flex items-start space-x-4">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
                <Skeleton className="h-3 w-1/4" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Higher-order component for wrapping pages with loading and error states
interface WithPageStatesProps {
  isLoading?: boolean;
  error?: Error | string | null;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  children: React.ReactNode;
}

export function WithPageStates({
  isLoading,
  error,
  loadingComponent,
  errorComponent,
  children
}: WithPageStatesProps) {
  if (isLoading) {
    return loadingComponent || <PageLoadingState />;
  }

  if (error) {
    const errorMessage = typeof error === 'string' ? error : error.message;
    return errorComponent || <PageErrorState message={errorMessage} />;
  }

  return <>{children}</>;
}
