'use client';

import { useEffect } from 'react';
import Head from 'next/head';
import { SEOConfig, BreadcrumbItem } from '@/lib/seo/seo-config';
import { 
  injectStructuredData, 
  generateBreadcrumbStructuredData,
  generateCanonicalUrl 
} from '@/lib/seo/seo-utils';

interface SEOHeadProps {
  config: SEOConfig;
  breadcrumbs?: BreadcrumbItem[];
  children?: React.ReactNode;
}

export default function SEOHead({ config, breadcrumbs, children }: SEOHeadProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://faafo-career.com';

  useEffect(() => {
    // Update document title dynamically
    if (config.title) {
      document.title = config.title;
    }

    // Update meta description
    if (config.description) {
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute('content', config.description);
      } else {
        const meta = document.createElement('meta');
        meta.name = 'description';
        meta.content = config.description;
        document.head.appendChild(meta);
      }
    }

    // Update keywords
    if (config.keywords) {
      const metaKeywords = document.querySelector('meta[name="keywords"]');
      const keywordsContent = Array.isArray(config.keywords) 
        ? config.keywords.join(', ') 
        : config.keywords;
      
      if (metaKeywords) {
        metaKeywords.setAttribute('content', keywordsContent);
      } else {
        const meta = document.createElement('meta');
        meta.name = 'keywords';
        meta.content = keywordsContent;
        document.head.appendChild(meta);
      }
    }

    // Update canonical URL
    if (config.canonical) {
      const canonicalLink = document.querySelector('link[rel="canonical"]');
      if (canonicalLink) {
        canonicalLink.setAttribute('href', config.canonical);
      } else {
        const link = document.createElement('link');
        link.rel = 'canonical';
        link.href = config.canonical;
        document.head.appendChild(link);
      }
    }

    // Update Open Graph tags
    if (config.openGraph) {
      const ogTags = [
        { property: 'og:title', content: config.openGraph.title || config.title },
        { property: 'og:description', content: config.openGraph.description || config.description },
        { property: 'og:type', content: config.openGraph.type || 'website' },
        { property: 'og:url', content: config.canonical || window.location.href },
        { property: 'og:site_name', content: 'FAAFO Career Platform' },
        { property: 'og:locale', content: 'en_US' },
      ];

      if (config.openGraph.images && config.openGraph.images.length > 0) {
        const image = config.openGraph.images[0];
        ogTags.push(
          { property: 'og:image', content: image.url },
          { property: 'og:image:width', content: image.width?.toString() || '1200' },
          { property: 'og:image:height', content: image.height?.toString() || '630' },
          { property: 'og:image:alt', content: image.alt || config.title }
        );
      }

      ogTags.forEach(tag => {
        if (tag.content) {
          const existingTag = document.querySelector(`meta[property="${tag.property}"]`);
          if (existingTag) {
            existingTag.setAttribute('content', tag.content);
          } else {
            const meta = document.createElement('meta');
            meta.setAttribute('property', tag.property);
            meta.setAttribute('content', tag.content);
            document.head.appendChild(meta);
          }
        }
      });
    }

    // Update Twitter Card tags
    if (config.twitter) {
      const twitterTags = [
        { name: 'twitter:card', content: config.twitter.card || 'summary_large_image' },
        { name: 'twitter:title', content: config.twitter.title || config.title },
        { name: 'twitter:description', content: config.twitter.description || config.description },
        { name: 'twitter:creator', content: '@faafo_platform' },
        { name: 'twitter:site', content: '@faafo_platform' },
      ];

      if (config.twitter.images && config.twitter.images.length > 0) {
        twitterTags.push({
          name: 'twitter:image',
          content: config.twitter.images[0]
        });
      }

      twitterTags.forEach(tag => {
        if (tag.content) {
          const existingTag = document.querySelector(`meta[name="${tag.name}"]`);
          if (existingTag) {
            existingTag.setAttribute('content', tag.content);
          } else {
            const meta = document.createElement('meta');
            meta.setAttribute('name', tag.name);
            meta.setAttribute('content', tag.content);
            document.head.appendChild(meta);
          }
        }
      });
    }

    // Update robots meta tag
    const robotsContent = [];
    if (!config.noindex) robotsContent.push('index');
    else robotsContent.push('noindex');
    
    if (!config.nofollow) robotsContent.push('follow');
    else robotsContent.push('nofollow');

    const robotsTag = document.querySelector('meta[name="robots"]');
    if (robotsTag) {
      robotsTag.setAttribute('content', robotsContent.join(', '));
    } else {
      const meta = document.createElement('meta');
      meta.name = 'robots';
      meta.content = robotsContent.join(', ');
      document.head.appendChild(meta);
    }

    // Inject structured data
    const structuredDataArray = [];
    
    if (config.structuredData) {
      structuredDataArray.push(...config.structuredData);
    }

    if (breadcrumbs && breadcrumbs.length > 0) {
      structuredDataArray.push(generateBreadcrumbStructuredData(breadcrumbs));
    }

    if (structuredDataArray.length > 0) {
      injectStructuredData(structuredDataArray);
    }

    // Track page view for analytics
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID, {
        page_title: config.title,
        page_location: window.location.href,
        custom_map: {
          custom_parameter_1: 'seo_optimized',
        }
      });
    }
  }, [config, breadcrumbs]);

  return (
    <>
      <Head>
        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* DNS prefetch for potential external resources */}
        <link rel="dns-prefetch" href="//www.google-analytics.com" />
        <link rel="dns-prefetch" href="//vercel.live" />
        
        {/* Additional meta tags for better SEO */}
        <meta name="application-name" content="FAAFO Career Platform" />
        <meta name="theme-color" content="#3b82f6" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-title" content="FAAFO Career" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="format-detection" content="telephone=no" />
        
        {/* Favicon and app icons */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        
        {/* Additional SEO meta tags */}
        <meta name="author" content="FAAFO Team" />
        <meta name="publisher" content="FAAFO Career Platform" />
        <meta name="copyright" content="© 2024 FAAFO Career Platform" />
        <meta name="language" content="en-US" />
        <meta name="revisit-after" content="7 days" />
        <meta name="distribution" content="global" />
        <meta name="rating" content="general" />
        
        {/* Structured data for organization */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'Organization',
              name: 'FAAFO Career Platform',
              url: baseUrl,
              logo: `${baseUrl}/images/logo.png`,
              description: 'Career development platform providing AI-powered assessments and personalized career guidance.',
              sameAs: [
                'https://twitter.com/faafo_platform',
                'https://linkedin.com/company/faafo-career',
              ],
            }),
          }}
        />
      </Head>
      {children}
    </>
  );
}

/**
 * Hook for updating SEO data dynamically
 */
export function useSEO(config: SEOConfig, breadcrumbs?: BreadcrumbItem[]) {
  useEffect(() => {
    // Update page title
    if (config.title) {
      document.title = config.title;
    }

    // Update meta description
    if (config.description) {
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute('content', config.description);
      }
    }

    // Inject structured data
    const structuredDataArray = [];
    
    if (config.structuredData) {
      structuredDataArray.push(...config.structuredData);
    }

    if (breadcrumbs && breadcrumbs.length > 0) {
      structuredDataArray.push(generateBreadcrumbStructuredData(breadcrumbs));
    }

    if (structuredDataArray.length > 0) {
      injectStructuredData(structuredDataArray);
    }
  }, [config, breadcrumbs]);
}

/**
 * Generate breadcrumbs for common page patterns
 */
export function generatePageBreadcrumbs(
  currentPage: string,
  customBreadcrumbs?: BreadcrumbItem[]
): BreadcrumbItem[] {
  const baseBreadcrumbs: BreadcrumbItem[] = [
    { name: 'Home', url: '/' },
  ];

  if (customBreadcrumbs) {
    return [...baseBreadcrumbs, ...customBreadcrumbs];
  }

  // Generate breadcrumbs based on current page
  const breadcrumbMap: Record<string, BreadcrumbItem[]> = {
    'career-paths': [
      { name: 'Career Paths', url: '/career-paths' },
    ],
    'assessment': [
      { name: 'Assessment', url: '/assessment' },
    ],
    'tools': [
      { name: 'Tools', url: '/tools' },
    ],
    'resources': [
      { name: 'Resources', url: '/resources' },
    ],
    'forum': [
      { name: 'Community', url: '/forum' },
    ],
    'freedom-fund': [
      { name: 'Freedom Fund', url: '/freedom-fund' },
    ],
  };

  return [...baseBreadcrumbs, ...(breadcrumbMap[currentPage] || [])];
}
