'use client';

import * as React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { auditAccessibility, generateAccessibilityReport, testKeyboardNavigation, type AccessibilityAuditResult } from '@/utils/accessibility-audit';
import { AlertTriangle, CheckCircle, Info, XCircle, Download, RefreshCw } from 'lucide-react';

/**
 * Development tool for testing accessibility compliance
 * Only renders in development mode
 */
export function AccessibilityTester() {
  const [auditResult, setAuditResult] = React.useState<AccessibilityAuditResult | null>(null);
  const [isRunning, setIsRunning] = React.useState(false);
  const [lastRunTime, setLastRunTime] = React.useState<Date | null>(null);

  // Only render in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const runAudit = React.useCallback(async () => {
    setIsRunning(true);
    try {
      // Small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const result = auditAccessibility();
      setAuditResult(result);
      setLastRunTime(new Date());
    } catch (error) {
      console.error('Accessibility audit failed:', error);
    } finally {
      setIsRunning(false);
    }
  }, []);

  const downloadReport = React.useCallback(() => {
    if (!auditResult) return;
    
    const report = generateAccessibilityReport(auditResult);
    const blob = new Blob([report], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `accessibility-report-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [auditResult]);

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'serious':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'moderate':
        return <Info className="h-4 w-4 text-yellow-500" />;
      case 'minor':
        return <Info className="h-4 w-4 text-blue-500" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'destructive';
      case 'serious':
        return 'destructive';
      case 'moderate':
        return 'secondary';
      case 'minor':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 dark:text-green-400';
    if (score >= 70) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-destructive';
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Card className="w-96 max-h-[80vh] shadow-lg border-2">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">Accessibility Tester</CardTitle>
              <CardDescription>
                Development tool for WCAG compliance
              </CardDescription>
            </div>
            <Button
              onClick={runAudit}
              disabled={isRunning}
              size="sm"
              className="ml-2"
            >
              {isRunning ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                'Run Audit'
              )}
            </Button>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {auditResult ? (
            <Tabs defaultValue="summary" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="issues">Issues</TabsTrigger>
              </TabsList>

              <TabsContent value="summary" className="space-y-4">
                <div className="text-center">
                  <div className={`text-3xl font-bold ${getScoreColor(auditResult.score)}`}>
                    {auditResult.score}/100
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Accessibility Score
                  </div>
                  <div className="mt-2">
                    {auditResult.passed ? (
                      <Badge variant="default" className="bg-green-600">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Passed
                      </Badge>
                    ) : (
                      <Badge variant="destructive">
                        <XCircle className="h-3 w-3 mr-1" />
                        Failed
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div className="text-center p-2 bg-red-50 dark:bg-red-950 rounded">
                    <div className="text-lg font-semibold text-red-600">
                      {auditResult.summary.critical}
                    </div>
                    <div className="text-xs text-red-600">Critical</div>
                  </div>
                  <div className="text-center p-2 bg-orange-50 dark:bg-orange-950 rounded">
                    <div className="text-lg font-semibold text-orange-600">
                      {auditResult.summary.serious}
                    </div>
                    <div className="text-xs text-orange-600">Serious</div>
                  </div>
                  <div className="text-center p-2 bg-yellow-50 dark:bg-yellow-950 rounded">
                    <div className="text-lg font-semibold text-yellow-600">
                      {auditResult.summary.moderate}
                    </div>
                    <div className="text-xs text-yellow-600">Moderate</div>
                  </div>
                  <div className="text-center p-2 bg-blue-50 dark:bg-blue-950 rounded">
                    <div className="text-lg font-semibold text-blue-600">
                      {auditResult.summary.minor}
                    </div>
                    <div className="text-xs text-blue-600">Minor</div>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={downloadReport}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Download Report
                  </Button>
                  <Button
                    onClick={runAudit}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <RefreshCw className="h-4 w-4 mr-1" />
                    Re-run
                  </Button>
                </div>

                {lastRunTime && (
                  <div className="text-xs text-muted-foreground text-center">
                    Last run: {lastRunTime.toLocaleTimeString()}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="issues">
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {auditResult.issues.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
                        No accessibility issues found!
                      </div>
                    ) : (
                      auditResult.issues.map((issue, index) => (
                        <Card key={index} className="p-3">
                          <div className="flex items-start gap-2">
                            {getSeverityIcon(issue.severity)}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <Badge 
                                  variant={getSeverityColor(issue.severity) as any}
                                  className="text-xs"
                                >
                                  {issue.severity}
                                </Badge>
                                <code className="text-xs bg-muted px-1 rounded">
                                  {issue.rule}
                                </code>
                              </div>
                              <p className="text-sm text-foreground mb-1">
                                {issue.message}
                              </p>
                              {issue.element && (
                                <div className="text-xs text-muted-foreground font-mono">
                                  {issue.element.tagName.toLowerCase()}
                                  {issue.element.id && `#${issue.element.id}`}
                                  {issue.element.className && 
                                    `.${issue.element.className.split(' ').slice(0, 2).join('.')}`
                                  }
                                </div>
                              )}
                            </div>
                          </div>
                        </Card>
                      ))
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>
            </Tabs>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <Info className="h-8 w-8 mx-auto mb-2" />
              <p>Click "Run Audit" to test accessibility compliance</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
