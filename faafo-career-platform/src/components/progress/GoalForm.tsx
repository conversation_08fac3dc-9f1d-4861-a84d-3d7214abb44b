'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { GoalType, GoalCategory, UserGoal } from '@prisma/client';
import { Calendar, Target, Save, X } from 'lucide-react';

interface GoalFormProps {
  goal?: UserGoal | null;
  onSubmit: (goalData: GoalFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export interface GoalFormData {
  title: string;
  description?: string;
  type: GoalType;
  category: GoalCategory;
  targetValue: number;
  targetDate?: Date;
  isPublic: boolean;
}

interface GoalFormErrors {
  title?: string;
  description?: string;
  type?: string;
  category?: string;
  targetValue?: string;
  targetDate?: string;
  isPublic?: string;
}

const goalTypes: { value: GoalType; label: string; description: string }[] = [
  { value: 'DAILY', label: 'Daily', description: 'Complete every day' },
  { value: 'WEEKLY', label: 'Weekly', description: 'Complete every week' },
  { value: 'MONTHLY', label: 'Monthly', description: 'Complete every month' },
  { value: 'YEARLY', label: 'Yearly', description: 'Complete within a year' },
  { value: 'CUSTOM', label: 'Custom', description: 'Set your own timeline' },
];

const goalCategories: { value: GoalCategory; label: string; icon: string }[] = [
  { value: 'LEARNING_RESOURCES', label: 'Learning Resources', icon: '📚' },
  { value: 'SKILLS', label: 'Skills Development', icon: '🎯' },
  { value: 'CERTIFICATIONS', label: 'Certifications', icon: '📜' },
  { value: 'PROJECTS', label: 'Projects', icon: '🚀' },
  { value: 'CAREER_MILESTONES', label: 'Career Milestones', icon: '🏆' },
  { value: 'NETWORKING', label: 'Networking', icon: '🤝' },
];

export default function GoalForm({ goal, onSubmit, onCancel, isLoading = false }: GoalFormProps) {
  const [formData, setFormData] = useState<GoalFormData>({
    title: goal?.title || '',
    description: goal?.description || '',
    type: goal?.type || 'MONTHLY',
    category: goal?.category || 'LEARNING_RESOURCES',
    targetValue: goal?.targetValue || 1,
    targetDate: goal?.targetDate ? new Date(goal.targetDate) : undefined,
    isPublic: goal?.isPublic || false,
  });

  const [errors, setErrors] = useState<GoalFormErrors>({});

  const validateForm = useCallback((): boolean => {
    const newErrors: GoalFormErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (formData.targetValue <= 0) {
      newErrors.targetValue = 'Target value must be greater than 0';
    }

    if (formData.type === 'CUSTOM' && !formData.targetDate) {
      newErrors.targetDate = 'Target date is required for custom goals';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData.title, formData.targetValue, formData.type, formData.targetDate]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting goal:', error);
    }
  }, [validateForm, onSubmit, formData]);

  const handleInputChange = useCallback((field: keyof GoalFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  }, [errors]);

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="h-5 w-5" />
          {goal ? 'Edit Goal' : 'Create New Goal'}
        </CardTitle>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Goal Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="e.g., Complete 10 Python courses"
              className={errors.title ? 'border-red-500' : ''}
            />
            {errors.title && (
              <p className="text-sm text-red-600">{errors.title}</p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe your goal in more detail..."
              rows={3}
            />
          </div>

          {/* Type and Category */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Goal Type *</Label>
              <Select
                value={formData.type}
                onValueChange={(value: GoalType) => handleInputChange('type', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {goalTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div>
                        <div className="font-medium">{type.label}</div>
                        <div className="text-sm text-gray-500">{type.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Category *</Label>
              <Select
                value={formData.category}
                onValueChange={(value: GoalCategory) => handleInputChange('category', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {goalCategories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      <div className="flex items-center gap-2">
                        <span>{category.icon}</span>
                        <span>{category.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Target Value */}
          <div className="space-y-2">
            <Label htmlFor="targetValue">Target Value *</Label>
            <Input
              id="targetValue"
              type="number"
              min="1"
              value={formData.targetValue}
              onChange={(e) => handleInputChange('targetValue', parseInt(e.target.value) || 0)}
              placeholder="e.g., 10"
              className={errors.targetValue ? 'border-red-500' : ''}
            />
            {errors.targetValue && (
              <p className="text-sm text-red-600">{errors.targetValue}</p>
            )}
          </div>

          {/* Target Date (for custom goals) */}
          {formData.type === 'CUSTOM' && (
            <div className="space-y-2">
              <Label htmlFor="targetDate">Target Date *</Label>
              <Input
                id="targetDate"
                type="date"
                value={formData.targetDate ? formData.targetDate.toISOString().split('T')[0] : ''}
                onChange={(e) => handleInputChange('targetDate', e.target.value ? new Date(e.target.value) : undefined)}
                className={errors.targetDate ? 'border-red-500' : ''}
              />
              {errors.targetDate && (
                <p className="text-sm text-red-600">{errors.targetDate}</p>
              )}
            </div>
          )}

          {/* Public/Private */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="isPublic"
              checked={formData.isPublic}
              onCheckedChange={(checked) => handleInputChange('isPublic', checked)}
            />
            <Label htmlFor="isPublic" className="text-sm">
              Make this goal public (visible to other users)
            </Label>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              {isLoading ? 'Saving...' : goal ? 'Update Goal' : 'Create Goal'}
            </Button>
            
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
