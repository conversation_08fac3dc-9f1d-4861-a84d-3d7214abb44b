'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { Heart, ThumbsUp, Lightbulb, Laugh, X, AlertTriangle } from 'lucide-react';

interface Reaction {
  type: string;
  userId: string;
  user: {
    id: string;
    name?: string;
    email: string;
  };
}

interface ReactionSummary {
  [key: string]: {
    count: number;
    users: Array<{
      id: string;
      name?: string;
      email: string;
    }>;
  };
}

interface ReactionButtonsProps {
  postId?: string;
  replyId?: string;
  initialReactions?: Reaction[];
  onReactionChange?: (summary: ReactionSummary) => void;
  size?: 'small' | 'medium';
}

const reactionTypes = [
  { type: 'LIKE', icon: ThumbsUp, label: 'Like', emoji: '👍' },
  { type: 'HELPFUL', icon: Lightbulb, label: 'Helpful', emoji: '💡' },
  { type: 'INSIGHTFUL', icon: Heart, label: 'Insightful', emoji: '🧠' },
  { type: 'FUNNY', icon: Laugh, label: 'Funny', emoji: '😄' },
  { type: 'LOVE', icon: Heart, label: 'Love', emoji: '❤️' },
  { type: 'DISAGREE', icon: X, label: 'Disagree', emoji: '👎' },
];

export default function ReactionButtons({ 
  postId, 
  replyId, 
  initialReactions = [], 
  onReactionChange,
  size = 'medium' 
}: ReactionButtonsProps) {
  const { data: session } = useSession();
  const [reactions, setReactions] = useState<Reaction[]>(initialReactions);
  const [reactionSummary, setReactionSummary] = useState<ReactionSummary>({});
  const [userReaction, setUserReaction] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    updateReactionSummary(reactions);
  }, [reactions]);

  useEffect(() => {
    if (session?.user?.id) {
      const userReact = reactions.find(r => r.userId === session.user?.id);
      setUserReaction(userReact?.type || null);
    }
  }, [reactions, session]);

  const updateReactionSummary = useCallback((reactionList: Reaction[]) => {
    const summary = reactionList.reduce((acc, reaction) => {
      if (!acc[reaction.type]) {
        acc[reaction.type] = {
          count: 0,
          users: [],
        };
      }
      acc[reaction.type].count++;
      acc[reaction.type].users.push(reaction.user);
      return acc;
    }, {} as ReactionSummary);

    setReactionSummary(summary);
    onReactionChange?.(summary);
  }, [onReactionChange]);

  const handleReaction = useCallback(async (type: string) => {
    if (!session?.user?.id) {
      // Could show login prompt here
      return;
    }

    if (loading) return;

    setLoading(true);

    try {
      const endpoint = postId 
        ? `/api/forum/posts/${postId}/reactions`
        : `/api/forum/replies/${replyId}/reactions`;

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ type }),
      });

      if (!response.ok) {
        throw new Error('Failed to update reaction');
      }

      // Optimistically update the UI
      if (userReaction === type) {
        // Remove reaction
        setReactions(prev => prev.filter(r => r.userId !== session.user?.id));
        setUserReaction(null);
      } else {
        // Add or update reaction
        const newReaction: Reaction = {
          type,
          userId: session.user?.id!,
          user: {
            id: session.user?.id!,
            name: session.user?.name || undefined,
            email: session.user?.email || '',
          },
        };

        setReactions(prev => {
          const filtered = prev.filter(r => r.userId !== session.user?.id);
          return [...filtered, newReaction];
        });
        setUserReaction(type);
      }
    } catch (error) {
      console.error('Error updating reaction:', error);
      // Could show error toast here
    } finally {
      setLoading(false);
    }
  }, [session?.user?.id, postId, replyId, reactions, updateReactionSummary]);

  const getTooltipText = useCallback((type: string) => {
    const summary = reactionSummary[type];
    if (!summary || summary.count === 0) return '';

    const users = summary.users.slice(0, 3).map(u => u.name || u.email.split('@')[0]);
    const remaining = summary.count - users.length;

    let text = users.join(', ');
    if (remaining > 0) {
      text += ` and ${remaining} other${remaining > 1 ? 's' : ''}`;
    }

    return text;
  }, [reactionSummary]);

  const buttonSize = size === 'small' ? 'min-h-[44px] min-w-[44px]' : 'min-h-[44px] min-w-[44px]';
  const iconSize = size === 'small' ? 'h-3 w-3' : 'h-4 w-4';
  const textSize = size === 'small' ? 'text-xs' : 'text-sm';

  return (
    <div className="flex items-center gap-1 flex-wrap">
      {reactionTypes.map(({ type, icon: Icon, label, emoji }) => {
        const count = reactionSummary[type]?.count || 0;
        const isActive = userReaction === type;
        const tooltipText = getTooltipText(type);

        return (
          <button
            key={type}
            onClick={() => handleReaction(type)}
            disabled={loading || !session}
            className={`
              inline-flex items-center gap-1 px-2 py-1 rounded-full transition-all duration-200 min-h-[44px] min-w-[44px] justify-center
              ${isActive
                ? 'bg-gray-100 text-gray-700 border border-gray-300 dark:bg-gray-900 dark:text-gray-300 dark:border-gray-700'
                : 'bg-gray-100 text-gray-600 border border-gray-200 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-600'
              }
              ${!session ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              ${loading ? 'opacity-50' : ''}
            `}
            title={tooltipText}
          >
            <span className="text-sm" role="img" aria-label={label}>
              {emoji}
            </span>
            {count > 0 && (
              <span className={`${textSize} font-medium`}>
                {count}
              </span>
            )}
          </button>
        );
      })}

      {/* Show total reaction count if there are reactions */}
      {Object.values(reactionSummary).reduce((total, { count }) => total + count, 0) > 0 && (
        <div className={`${textSize} text-gray-500 dark:text-gray-400 ml-2`}>
          {Object.values(reactionSummary).reduce((total, { count }) => total + count, 0)} reactions
        </div>
      )}
    </div>
  );
}
