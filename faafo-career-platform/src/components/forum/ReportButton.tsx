'use client';

import React, { useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { Flag, X } from 'lucide-react';
import { useCSRFToken } from '@/hooks/useCSRFToken';

interface ReportButtonProps {
  postId?: string;
  replyId?: string;
  size?: 'small' | 'medium';
}

const reportReasons = [
  { value: 'SPAM', label: 'Spam' },
  { value: 'INAPPROPRIATE_CONTENT', label: 'Inappropriate Content' },
  { value: 'HARASSMENT', label: 'Harassment' },
  { value: 'OFF_TOPIC', label: 'Off Topic' },
  { value: 'MISINFORMATION', label: 'Misinformation' },
  { value: 'COPYRIGHT_VIOLATION', label: 'Copyright Violation' },
  { value: 'OTHER', label: 'Other' },
];

export default function ReportButton({ postId, replyId, size = 'medium' }: ReportButtonProps) {
  const { data: session } = useSession();
  const { csrfFetch } = useCSRFToken();
  const [showModal, setShowModal] = useState(false);
  const [selectedReason, setSelectedReason] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleReport = useCallback(async () => {
    if (!session?.user?.id || !selectedReason) return;

    setLoading(true);

    try {
      const response = await csrfFetch('/api/forum/reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          postId,
          replyId,
          reason: selectedReason,
          description: description.trim(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit report');
      }

      setSubmitted(true);
      setTimeout(() => {
        setShowModal(false);
        setSubmitted(false);
        setSelectedReason('');
        setDescription('');
      }, 2000);
    } catch (error) {
      console.error('Error submitting report:', error);
      alert(error instanceof Error ? error.message : 'Failed to submit report');
    } finally {
      setLoading(false);
    }
  }, [session?.user?.id, selectedReason, description, postId, replyId, csrfFetch]);

  const iconSize = size === 'small' ? 'h-3 w-3' : 'h-4 w-4';

  if (!session) {
    return null; // Don't show report button for non-authenticated users
  }

  return (
    <>
      <button
        onClick={() => setShowModal(true)}
        className="inline-flex items-center gap-1 px-2 py-1 text-gray-500 hover:text-red-600 transition-colors"
        title="Report this content"
      >
        <Flag className={iconSize} />
        {size === 'medium' && <span className="text-sm">Report</span>}
      </button>

      {/* Report Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Report Content
              </h3>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {submitted ? (
              <div className="text-center py-8">
                <div className="text-green-600 dark:text-green-400 mb-2">
                  <Flag className="h-12 w-12 mx-auto" />
                </div>
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Report Submitted
                </h4>
                <p className="text-gray-600 dark:text-gray-400">
                  Thank you for helping keep our community safe. We'll review your report.
                </p>
              </div>
            ) : (
              <>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Help us understand what's wrong with this content.
                </p>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Reason for reporting *
                    </label>
                    <select
                      value={selectedReason}
                      onChange={(e) => setSelectedReason(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      required
                    >
                      <option value="">Select a reason...</option>
                      {reportReasons.map((reason) => (
                        <option key={reason.value} value={reason.value}>
                          {reason.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Additional details (optional)
                    </label>
                    <textarea
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      placeholder="Provide any additional context that might help us understand the issue..."
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      maxLength={500}
                    />
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {description.length}/500 characters
                    </div>
                  </div>
                </div>

                <div className="flex gap-3 mt-6">
                  <button
                    onClick={handleReport}
                    disabled={!selectedReason || loading}
                    className="flex-1 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {loading ? 'Submitting...' : 'Submit Report'}
                  </button>
                  <button
                    onClick={() => setShowModal(false)}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </>
  );
}
