'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { Bookmark, BookmarkCheck } from 'lucide-react';

interface BookmarkButtonProps {
  postId: string;
  initialBookmarked?: boolean;
  onBookmarkChange?: (bookmarked: boolean) => void;
  size?: 'small' | 'medium';
  showLabel?: boolean;
}

export default function BookmarkButton({ 
  postId, 
  initialBookmarked = false, 
  onBookmarkChange,
  size = 'medium',
  showLabel = false
}: BookmarkButtonProps) {
  const { data: session } = useSession();
  const [isBookmarked, setIsBookmarked] = useState(initialBookmarked);
  const [loading, setLoading] = useState(false);

  const handleBookmark = useCallback(async () => {
    if (!session?.user?.id) {
      // Could show login prompt here
      return;
    }

    if (loading) return;

    setLoading(true);

    try {
      const response = await fetch('/api/forum/bookmarks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ postId }),
      });

      if (!response.ok) {
        throw new Error('Failed to update bookmark');
      }

      const data = await response.json();
      const newBookmarkedState = data.bookmarked;

      setIsBookmarked(newBookmarkedState);
      onBookmarkChange?.(newBookmarkedState);
    } catch (error) {
      console.error('Error updating bookmark:', error);
      // Could show error toast here
    } finally {
      setLoading(false);
    }
  }, [session?.user?.id, loading, postId, onBookmarkChange]);

  const buttonSize = size === 'small' ? 'min-h-[44px] min-w-[44px]' : 'min-h-[44px] min-w-[44px]';
  const iconSize = size === 'small' ? 'h-3 w-3' : 'h-4 w-4';
  const textSize = size === 'small' ? 'text-xs' : 'text-sm';

  return (
    <button
      onClick={handleBookmark}
      disabled={loading || !session}
      className={`
        inline-flex items-center gap-2 px-3 py-1.5 rounded-md transition-all duration-200
        ${isBookmarked 
          ? 'bg-yellow-100 text-yellow-700 border border-yellow-300 dark:bg-yellow-900 dark:text-yellow-300 dark:border-yellow-700' 
          : 'bg-gray-100 text-gray-600 border border-gray-200 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-600'
        }
        ${!session ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        ${loading ? 'opacity-50' : ''}
      `}
      title={isBookmarked ? 'Remove bookmark' : 'Bookmark this post'}
    >
      {isBookmarked ? (
        <BookmarkCheck className={iconSize} />
      ) : (
        <Bookmark className={iconSize} />
      )}
      
      {showLabel && (
        <span className={textSize}>
          {isBookmarked ? 'Bookmarked' : 'Bookmark'}
        </span>
      )}
    </button>
  );
}
