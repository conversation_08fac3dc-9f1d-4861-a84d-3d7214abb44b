require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

async function createTestUser() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🧪 Creating test user: <EMAIL>');
    console.log('🔗 Database URL:', process.env.DATABASE_URL ? 'Set (Neon PostgreSQL)' : 'Not set');
    
    const testEmail = '<EMAIL>';
    const testPassword = 'TestPassword123!';
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: testEmail }
    });
    
    if (existingUser) {
      console.log('✅ Test user already exists:', existingUser.id);
      console.log(`   Email: ${existingUser.email}`);
      console.log(`   Email Verified: ${existingUser.emailVerified}`);
      console.log(`   Created: ${existingUser.createdAt}`);
      return existingUser;
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(testPassword, 12);
    
    // Create user directly in database (bypassing email verification for testing)
    const newUser = await prisma.user.create({
      data: {
        email: testEmail,
        password: hashedPassword,
        name: 'Test User',
        emailVerified: new Date(), // Mark as verified for testing
        failedLoginAttempts: 0,
      }
    });
    
    console.log('✅ Test user created successfully');
    console.log(`   ID: ${newUser.id}`);
    console.log(`   Email: ${newUser.email}`);
    console.log(`   Name: ${newUser.name}`);
    console.log(`   Email Verified: ${newUser.emailVerified}`);
    console.log(`   Password: ${testPassword}`);
    
    // Also create the other test user credentials mentioned in memories
    const testEmail2 = '<EMAIL>';
    const testPassword2 = 'testpassword';
    
    const existingUser2 = await prisma.user.findUnique({
      where: { email: testEmail2 }
    });
    
    if (!existingUser2) {
      const hashedPassword2 = await bcrypt.hash(testPassword2, 12);
      
      const newUser2 = await prisma.user.create({
        data: {
          email: testEmail2,
          password: hashedPassword2,
          name: 'Test User 2',
          emailVerified: new Date(),
          failedLoginAttempts: 0,
        }
      });
      
      console.log('✅ Second test user created successfully');
      console.log(`   ID: ${newUser2.id}`);
      console.log(`   Email: ${newUser2.email}`);
      console.log(`   Password: ${testPassword2}`);
    } else {
      console.log('✅ Second test user already exists:', existingUser2.id);
    }
    
    return newUser;
    
  } catch (error) {
    console.error('❌ Error creating test user:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the creation
createTestUser()
  .then((user) => {
    console.log('\n🎉 Test users ready for testing');
    console.log('Use these credentials:');
    console.log('1. Email: <EMAIL>, Password: TestPassword123!');
    console.log('2. Email: <EMAIL>, Password: testpassword');
  })
  .catch((error) => {
    console.error('❌ Failed to create test users:', error);
    process.exit(1);
  });
