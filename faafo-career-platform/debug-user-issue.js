require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

async function debugUserIssue() {
  console.log('🔗 Database URL:', process.env.DATABASE_URL ? 'Set (Neon PostgreSQL)' : 'Not set');
  console.log('🔗 Using database:', process.env.DATABASE_URL?.includes('neon.tech') ? 'Neon PostgreSQL (Production)' : 'Local/Other');

  const prisma = new PrismaClient();
  
  try {
    console.log('=== Debugging User/Session Issue ===');
    
    // Check all users in the database
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        createdAt: true,
        emailVerified: true
      }
    });
    
    console.log('\n📋 All Users in Database:');
    users.forEach((user, index) => {
      console.log(`${index + 1}. ID: ${user.id}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Name: ${user.name}`);
      console.log(`   Created: ${user.createdAt}`);
      console.log(`   Email Verified: ${user.emailVerified}`);
      console.log('');
    });
    
    // Check sessions
    const sessions = await prisma.session.findMany({
      select: {
        id: true,
        userId: true,
        sessionToken: true,
        expires: true
      }
    });
    
    console.log('\n🔐 All Sessions in Database:');
    sessions.forEach((session, index) => {
      console.log(`${index + 1}. Session ID: ${session.id}`);
      console.log(`   User ID: ${session.userId}`);
      console.log(`   Token: ${session.sessionToken.substring(0, 20)}...`);
      console.log(`   Expires: ${session.expires}`);
      console.log('');
    });
    
    // Check accounts
    const accounts = await prisma.account.findMany({
      select: {
        id: true,
        userId: true,
        provider: true,
        providerAccountId: true
      }
    });
    
    console.log('\n🔗 All Accounts in Database:');
    accounts.forEach((account, index) => {
      console.log(`${index + 1}. Account ID: ${account.id}`);
      console.log(`   User ID: ${account.userId}`);
      console.log(`   Provider: ${account.provider}`);
      console.log(`   Provider Account ID: ${account.providerAccountId}`);
      console.log('');
    });
    
    // Check assessments
    const assessments = await prisma.assessment.findMany({
      select: {
        id: true,
        userId: true,
        status: true,
        createdAt: true
      }
    });
    
    console.log('\n📝 All Assessments in Database:');
    assessments.forEach((assessment, index) => {
      console.log(`${index + 1}. Assessment ID: ${assessment.id}`);
      console.log(`   User ID: ${assessment.userId}`);
      console.log(`   Status: ${assessment.status}`);
      console.log(`   Created: ${assessment.createdAt}`);
      console.log('');
    });
    
    // Check for orphaned sessions (sessions with non-existent user IDs)
    const userIds = users.map(u => u.id);
    const orphanedSessions = sessions.filter(s => !userIds.includes(s.userId));
    
    if (orphanedSessions.length > 0) {
      console.log('\n⚠️  ORPHANED SESSIONS (sessions with non-existent user IDs):');
      orphanedSessions.forEach((session, index) => {
        console.log(`${index + 1}. Session ID: ${session.id}`);
        console.log(`   User ID: ${session.userId} (USER NOT FOUND)`);
        console.log(`   Token: ${session.sessionToken.substring(0, 20)}...`);
        console.log('');
      });
    }
    
    // Check for orphaned accounts
    const orphanedAccounts = accounts.filter(a => !userIds.includes(a.userId));
    
    if (orphanedAccounts.length > 0) {
      console.log('\n⚠️  ORPHANED ACCOUNTS (accounts with non-existent user IDs):');
      orphanedAccounts.forEach((account, index) => {
        console.log(`${index + 1}. Account ID: ${account.id}`);
        console.log(`   User ID: ${account.userId} (USER NOT FOUND)`);
        console.log(`   Provider: ${account.provider}`);
        console.log('');
      });
    }
    
    console.log('\n=== Summary ===');
    console.log(`Total Users: ${users.length}`);
    console.log(`Total Sessions: ${sessions.length}`);
    console.log(`Total Accounts: ${accounts.length}`);
    console.log(`Total Assessments: ${assessments.length}`);
    console.log(`Orphaned Sessions: ${orphanedSessions.length}`);
    console.log(`Orphaned Accounts: ${orphanedAccounts.length}`);
    
  } catch (error) {
    console.error('Error debugging user issue:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugUserIssue();
