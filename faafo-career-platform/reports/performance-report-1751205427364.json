{"summary": {"monitoringDuration": "0m 42s", "totalAlerts": 8, "criticalAlerts": 0, "warningAlerts": 8}, "metrics": {"responseTime": {"average": 666.9567695681427, "min": 305.2475467635623, "max": 1010.2762421553522, "latest": 563.8147343245153, "dataPoints": 8}, "throughput": {"average": 35.97746508754051, "min": 11.097494263370146, "max": 59.14062876554599, "latest": 47.87340712337053, "dataPoints": 8}, "errorRate": {"average": 2.022367436742342, "min": 0.05995276160485852, "max": 2.767883042556173, "latest": 2.280051601619551, "dataPoints": 8}, "memoryUsage": {"average": 97.75127172470093, "min": 95.51410675048828, "max": 99.578857421875, "latest": 97.32389450073242, "dataPoints": 8}, "cpuUsage": {"average": 20, "min": 20, "max": 20, "latest": 20, "dataPoints": 8}, "cacheHitRate": {"average": 86.41334422412264, "min": 78.57774921002988, "max": 92.74466246689006, "latest": 92.74466246689006, "dataPoints": 8}, "aiServiceLatency": {"average": 1659.1078934713707, "min": 599.7740587332237, "max": 2903.795702679577, "latest": 636.5516773741529, "dataPoints": 8}}, "alerts": [{"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 97.38% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751205390138}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 95.51% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751205395138}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 97.05% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751205400139}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 99.23% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751205405140}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 99.58% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751205410142}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 99.55% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751205415144}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 96.38% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751205420146}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 97.32% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751205425147}], "recommendations": [{"type": "RELIABILITY", "priority": "CRITICAL", "message": "Error rate is elevated. Review error logs and implement better error handling."}, {"type": "RESOURCE", "priority": "MEDIUM", "message": "Memory usage is high. Consider optimizing memory usage or increasing available memory."}]}