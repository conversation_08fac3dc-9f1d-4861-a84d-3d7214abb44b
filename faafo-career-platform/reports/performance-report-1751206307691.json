{"summary": {"monitoringDuration": "1m 6s", "totalAlerts": 13, "criticalAlerts": 0, "warningAlerts": 13}, "metrics": {"responseTime": {"average": 614.4866572412465, "min": 241.48280782172066, "max": 1034.6118731088964, "latest": 449.94005467188373, "dataPoints": 13}, "throughput": {"average": 31.89016560883621, "min": 11.615495484804875, "max": 57.250302964576676, "latest": 48.10036923932324, "dataPoints": 13}, "errorRate": {"average": 1.6854708683352158, "min": 0.30755093141424217, "max": 2.8893341834311084, "latest": 2.151440368225356, "dataPoints": 13}, "memoryUsage": {"average": 96.05051920964168, "min": 90.30208587646484, "max": 99.87497329711914, "latest": 99.87497329711914, "dataPoints": 13}, "cpuUsage": {"average": 20, "min": 20, "max": 20, "latest": 20, "dataPoints": 13}, "cacheHitRate": {"average": 86.43338944046225, "min": 75.66459417163391, "max": 94.87078496876002, "latest": 86.57450885175069, "dataPoints": 13}, "aiServiceLatency": {"average": 2337.9525378966605, "min": 973.4351193312942, "max": 3138.9731015778134, "latest": 1320.187913553536, "dataPoints": 13}}, "alerts": [{"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 94.08% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206246321}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 94.91% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206251321}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 92.09% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206256323}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 99.05% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206261325}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 99.61% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206266327}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 93.33% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206271328}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 90.30% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206276330}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 93.45% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206281331}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 94.81% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206286333}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 98.04% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206291334}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 99.48% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206296334}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 99.63% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206301335}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 99.87% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206306338}], "recommendations": [{"type": "RESOURCE", "priority": "MEDIUM", "message": "Memory usage is high. Consider optimizing memory usage or increasing available memory."}]}