{"summary": {"monitoringDuration": "0m 36s", "totalAlerts": 7, "criticalAlerts": 0, "warningAlerts": 7}, "metrics": {"responseTime": {"average": 929.8824166644896, "min": 429.4339453098089, "max": 1185.4316162618018, "latest": 1047.7470332231844, "dataPoints": 7}, "throughput": {"average": 36.041415748045516, "min": 18.95939850959936, "max": 47.00802798813445, "latest": 45.72510384278965, "dataPoints": 7}, "errorRate": {"average": 1.7366622231030873, "min": 0.6917525899907766, "max": 2.665968505093558, "latest": 1.1565195508709707, "dataPoints": 7}, "memoryUsage": {"average": 94.49115480695453, "min": 93.1222915649414, "max": 96.68159484863281, "latest": 96.68159484863281, "dataPoints": 7}, "cpuUsage": {"average": 20, "min": 20, "max": 20, "latest": 20, "dataPoints": 7}, "cacheHitRate": {"average": 87.43859417615955, "min": 76.58609223187638, "max": 93.61933882571856, "latest": 91.38507133247536, "dataPoints": 7}, "aiServiceLatency": {"average": 2017.025505477059, "min": 712.0965036974098, "max": 2705.4370854356134, "latest": 2705.4370854356134, "dataPoints": 7}}, "alerts": [{"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 94.52% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206817440}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 93.12% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206822440}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 93.53% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206827441}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 94.22% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206832442}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 94.66% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206837442}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 94.72% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206842443}, {"type": "HIGH_MEMORY_USAGE", "message": "Memory usage 96.68% exceeds threshold 80%", "severity": "WARNING", "timestamp": 1751206847444}], "recommendations": [{"type": "RESOURCE", "priority": "MEDIUM", "message": "Memory usage is high. Consider optimizing memory usage or increasing available memory."}]}