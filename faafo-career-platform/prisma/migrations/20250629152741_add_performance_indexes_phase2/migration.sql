-- CreateIndex
CREATE INDEX "Assessment_userId_status_completedAt_idx" ON "Assessment"("userId", "status", "completedAt");

-- CreateIndex
CREATE INDEX "CareerPath_name_isActive_idx" ON "CareerPath"("name", "isActive");

-- CreateIndex
CREATE INDEX "CareerPath_slug_isActive_idx" ON "CareerPath"("slug", "isActive");

-- CreateIndex
CREATE INDEX "SkillAssessment_userId_isActive_assessmentDate_idx" ON "SkillAssessment"("userId", "isActive", "assessmentDate");

-- CreateIndex
CREATE INDEX "SkillGapAnalysis_userId_status_lastUpdated_idx" ON "SkillGapAnalysis"("userId", "status", "lastUpdated");

-- CreateIndex
CREATE INDEX "SkillMarketData_isActive_dataDate_demandLevel_idx" ON "SkillMarketData"("isActive", "dataDate", "demandLevel");
