#!/usr/bin/env node

/**
 * Direct Service Testing for Phase 1 Optimizations
 * Tests the optimization services directly without API authentication
 */

const path = require('path');

// Mock environment for testing
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = process.env.DATABASE_URL || 'postgresql://test:test@localhost:5432/test';

/**
 * Test Request Batching Service
 */
async function testRequestBatchingService() {
  console.log('\n🔄 Testing Request Batching Service...');
  
  try {
    // Import the service
    const { RequestBatchingService } = require('./src/lib/services/request-batching-service.ts');
    
    // Create service instance
    const batchingService = new RequestBatchingService({
      batchSize: 3,
      maxWaitTimeMs: 1000,
      maxConcurrentBatches: 2,
      deduplicationEnabled: true
    });
    
    console.log('✅ Request Batching Service imported successfully');
    
    // Test configuration
    const config = batchingService.getConfig();
    console.log(`   - Batch size: ${config.batchSize}`);
    console.log(`   - Max wait time: ${config.maxWaitTimeMs}ms`);
    console.log(`   - Deduplication: ${config.deduplicationEnabled ? 'enabled' : 'disabled'}`);
    
    // Test metrics
    const metrics = batchingService.getMetrics();
    console.log(`   - Total requests processed: ${metrics.totalRequestsProcessed}`);
    console.log(`   - Current queue size: ${metrics.currentQueueSize}`);
    
    return {
      success: true,
      config,
      metrics
    };
    
  } catch (error) {
    console.error(`❌ Request Batching Service test failed: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Test Enhanced Cache Service
 */
async function testEnhancedCacheService() {
  console.log('\n💾 Testing Enhanced Cache Service...');
  
  try {
    // Import the service
    const { EnhancedCacheService } = require('./src/lib/services/enhanced-cache-service.ts');
    
    // Create service instance
    const cacheService = new EnhancedCacheService({
      l1MaxSize: 100,
      l1TTL: 300,
      l2TTL: 3600,
      compressionThreshold: 1024
    });
    
    console.log('✅ Enhanced Cache Service imported successfully');
    
    // Test basic cache operations
    const testKey = 'test-key-123';
    const testData = { message: 'Hello, Cache!', timestamp: Date.now() };
    
    // Test set operation
    await cacheService.set(testKey, testData, 300);
    console.log('   ✅ Cache set operation successful');
    
    // Test get operation
    const retrievedData = await cacheService.get(testKey);
    const cacheHit = retrievedData !== null;
    console.log(`   ✅ Cache get operation: ${cacheHit ? 'HIT' : 'MISS'}`);
    
    if (cacheHit) {
      console.log(`   - Retrieved data matches: ${JSON.stringify(retrievedData) === JSON.stringify(testData)}`);
    }
    
    // Test cache metrics
    const metrics = cacheService.getMetrics();
    console.log(`   - L1 cache size: ${metrics.l1CacheSize}`);
    console.log(`   - Cache hits: ${metrics.hits}`);
    console.log(`   - Cache misses: ${metrics.misses}`);
    console.log(`   - Hit rate: ${metrics.hitRate.toFixed(2)}%`);
    
    // Test compression
    const largeData = 'x'.repeat(2000); // Data larger than compression threshold
    await cacheService.set('large-key', largeData, 300);
    const retrievedLargeData = await cacheService.get('large-key');
    const compressionWorking = retrievedLargeData === largeData;
    console.log(`   - Compression test: ${compressionWorking ? 'PASSED' : 'FAILED'}`);
    
    return {
      success: true,
      cacheHit,
      metrics,
      compressionWorking
    };
    
  } catch (error) {
    console.error(`❌ Enhanced Cache Service test failed: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Test Concurrent Database Service
 */
async function testConcurrentDatabaseService() {
  console.log('\n🗄️  Testing Concurrent Database Service...');
  
  try {
    // Import the service
    const { ConcurrentDatabaseService } = require('./src/lib/services/concurrent-database-service.ts');
    
    // Create service instance
    const dbService = new ConcurrentDatabaseService({
      maxConcurrentOperations: 5,
      operationTimeoutMs: 10000,
      retryAttempts: 3
    });
    
    console.log('✅ Concurrent Database Service imported successfully');
    
    // Test configuration
    const config = dbService.getConfig();
    console.log(`   - Max concurrent operations: ${config.maxConcurrentOperations}`);
    console.log(`   - Operation timeout: ${config.operationTimeoutMs}ms`);
    console.log(`   - Retry attempts: ${config.retryAttempts}`);
    
    // Test metrics
    const metrics = dbService.getMetrics();
    console.log(`   - Total operations: ${metrics.totalOperations}`);
    console.log(`   - Successful operations: ${metrics.successfulOperations}`);
    console.log(`   - Failed operations: ${metrics.failedOperations}`);
    console.log(`   - Average operation time: ${metrics.averageOperationTime.toFixed(2)}ms`);
    
    return {
      success: true,
      config,
      metrics
    };
    
  } catch (error) {
    console.error(`❌ Concurrent Database Service test failed: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Test Compression Utilities
 */
async function testCompressionUtils() {
  console.log('\n🗜️  Testing Compression Utilities...');
  
  try {
    // Import compression utilities
    const { compress, decompress } = require('./src/lib/utils/compression.ts');
    
    console.log('✅ Compression utilities imported successfully');
    
    // Test data
    const originalData = JSON.stringify({
      message: 'This is a test message for compression',
      data: Array(100).fill('test data item'),
      timestamp: Date.now()
    });
    
    console.log(`   - Original data size: ${originalData.length} bytes`);
    
    // Test compression
    const compressedData = compress(originalData);
    console.log(`   - Compressed data size: ${compressedData.length} bytes`);
    
    const compressionRatio = (1 - (compressedData.length / originalData.length)) * 100;
    console.log(`   - Compression ratio: ${compressionRatio.toFixed(2)}%`);
    
    // Test decompression
    const decompressedData = decompress(compressedData);
    const decompressionSuccessful = decompressedData === originalData;
    console.log(`   - Decompression successful: ${decompressionSuccessful ? 'YES' : 'NO'}`);
    
    return {
      success: true,
      originalSize: originalData.length,
      compressedSize: compressedData.length,
      compressionRatio,
      decompressionSuccessful
    };
    
  } catch (error) {
    console.error(`❌ Compression utilities test failed: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Test Service Integration
 */
async function testServiceIntegration() {
  console.log('\n🔗 Testing Service Integration...');
  
  try {
    // Test that all services can be imported together
    const services = {
      RequestBatchingService: require('./src/lib/services/request-batching-service.ts').RequestBatchingService,
      EnhancedCacheService: require('./src/lib/services/enhanced-cache-service.ts').EnhancedCacheService,
      ConcurrentDatabaseService: require('./src/lib/services/concurrent-database-service.ts').ConcurrentDatabaseService
    };
    
    console.log('✅ All services imported successfully');
    
    // Test service instantiation
    const instances = {
      batching: new services.RequestBatchingService(),
      cache: new services.EnhancedCacheService(),
      database: new services.ConcurrentDatabaseService()
    };
    
    console.log('✅ All services instantiated successfully');
    
    // Test that services have expected methods
    const expectedMethods = {
      batching: ['batchComprehensiveAnalysis', 'getMetrics', 'getConfig'],
      cache: ['get', 'set', 'invalidate', 'getMetrics'],
      database: ['fetchUserAssessmentsOptimized', 'getMetrics', 'getConfig']
    };
    
    let methodsValid = true;
    for (const [serviceName, methods] of Object.entries(expectedMethods)) {
      for (const method of methods) {
        if (typeof instances[serviceName][method] !== 'function') {
          console.error(`   ❌ Missing method ${method} in ${serviceName} service`);
          methodsValid = false;
        }
      }
    }
    
    if (methodsValid) {
      console.log('✅ All expected methods are available');
    }
    
    return {
      success: methodsValid,
      servicesLoaded: Object.keys(services).length,
      instancesCreated: Object.keys(instances).length
    };
    
  } catch (error) {
    console.error(`❌ Service integration test failed: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Generate test report
 */
function generateTestReport(results) {
  console.log('\n📊 PHASE 1 OPTIMIZATION SERVICES TEST REPORT');
  console.log('=============================================');
  
  const testResults = [
    { name: 'Request Batching Service', result: results.batching },
    { name: 'Enhanced Cache Service', result: results.cache },
    { name: 'Concurrent Database Service', result: results.database },
    { name: 'Compression Utilities', result: results.compression },
    { name: 'Service Integration', result: results.integration }
  ];
  
  let passedTests = 0;
  let totalTests = testResults.length;
  
  console.log('\n🧪 TEST RESULTS:');
  testResults.forEach(test => {
    const status = test.result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`   ${status} - ${test.name}`);
    if (test.result.success) passedTests++;
  });
  
  console.log(`\n📈 SUMMARY:`);
  console.log(`   - Tests passed: ${passedTests}/${totalTests}`);
  console.log(`   - Success rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  // Detailed results
  if (results.cache.success && results.cache.cacheHit) {
    console.log(`   ✅ Cache functionality: WORKING (hit rate: ${results.cache.metrics.hitRate.toFixed(1)}%)`);
  }
  
  if (results.compression.success) {
    console.log(`   ✅ Compression: WORKING (${results.compression.compressionRatio.toFixed(1)}% reduction)`);
  }
  
  if (results.integration.success) {
    console.log(`   ✅ Service integration: WORKING (${results.integration.servicesLoaded} services loaded)`);
  }
  
  console.log('\n🎯 READINESS ASSESSMENT:');
  if (passedTests === totalTests) {
    console.log('   ✅ Phase 1 optimizations are ready for production testing');
    console.log('   ✅ All core services are functional and integrated');
    console.log('   ✅ Ready to proceed with Phase 2 implementation');
  } else {
    console.log('   ⚠️  Some services need attention before proceeding');
    console.log('   ⚠️  Review failed tests and fix issues');
  }
}

/**
 * Main test execution
 */
async function runServiceTests() {
  console.log('🚀 STARTING PHASE 1 OPTIMIZATION SERVICES TESTING');
  console.log('================================================');
  
  const results = {};
  
  try {
    // Run all service tests
    results.batching = await testRequestBatchingService();
    results.cache = await testEnhancedCacheService();
    results.database = await testConcurrentDatabaseService();
    results.compression = await testCompressionUtils();
    results.integration = await testServiceIntegration();
    
    // Generate comprehensive report
    generateTestReport(results);
    
    console.log('\n✅ ALL SERVICE TESTS COMPLETED');
    
    return results;
    
  } catch (error) {
    console.error(`\n❌ SERVICE TESTING FAILED: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runServiceTests().catch(console.error);
}

module.exports = { runServiceTests };
