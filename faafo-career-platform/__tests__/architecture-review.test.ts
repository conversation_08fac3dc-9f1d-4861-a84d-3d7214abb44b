/**
 * Architecture Review Tests
 * Tests for implementation logic and architectural integrity
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import fs from 'fs';
import path from 'path';

describe('Architecture Review - Implementation Logic and Architecture', () => {
  describe('Circular Dependencies', () => {
    it('should not have circular dependencies between core services', () => {
      const srcPath = path.join(process.cwd(), 'src');
      const circularDeps = findCircularDependencies(srcPath);
      
      expect(circularDeps).toHaveLength(0);
      if (circularDeps.length > 0) {
        console.log('Circular dependencies found:', circularDeps);
      }
    });
  });

  describe('Error Handling Consistency', () => {
    it('should have consistent error handling patterns across API routes', async () => {
      const apiPath = path.join(process.cwd(), 'src/app/api');
      const routeFiles = getAllRouteFiles(apiPath);
      
      const inconsistentRoutes = [];
      
      for (const file of routeFiles) {
        const content = fs.readFileSync(file, 'utf8');
        
        // Check for withUnifiedErrorHandling usage
        if (!content.includes('withUnifiedErrorHandling')) {
          inconsistentRoutes.push(file);
        }
      }
      
      expect(inconsistentRoutes).toHaveLength(0);
      if (inconsistentRoutes.length > 0) {
        console.log('Routes without unified error handling:', inconsistentRoutes);
      }
    });
  });

  describe('Integration Points', () => {
    it('should have proper circuit breakers for external service integrations', () => {
      const integrationFiles = [
        'src/lib/services/geminiService.ts',
        'src/lib/secure-ai-service.ts',
        'src/lib/self-healing-ai-service.ts'
      ];
      
      const missingCircuitBreakers = [];
      
      for (const file of integrationFiles) {
        const fullPath = path.join(process.cwd(), file);
        if (fs.existsSync(fullPath)) {
          const content = fs.readFileSync(fullPath, 'utf8');
          
          // Check for circuit breaker patterns
          if (!content.includes('CircuitBreaker') && !content.includes('circuit')) {
            missingCircuitBreakers.push(file);
          }
        }
      }
      
      expect(missingCircuitBreakers).toHaveLength(0);
      if (missingCircuitBreakers.length > 0) {
        console.log('Services missing circuit breakers:', missingCircuitBreakers);
      }
    });
  });

  describe('State Management', () => {
    it('should not have state management issues in React components', () => {
      const componentPath = path.join(process.cwd(), 'src/components');
      const components = getAllTsxFiles(componentPath);
      
      const stateIssues = [];
      
      for (const component of components) {
        const content = fs.readFileSync(component, 'utf8');
        
        // Check for potential state issues
        if (content.includes('useState') && !content.includes('useCallback')) {
          // Check if component has complex state without proper memoization
          const stateCount = (content.match(/useState/g) || []).length;
          if (stateCount > 3) {
            stateIssues.push({
              file: component,
              issue: 'Complex state without useCallback optimization'
            });
          }
        }
      }
      
      expect(stateIssues).toHaveLength(0);
      if (stateIssues.length > 0) {
        console.log('State management issues:', stateIssues);
      }
    });
  });

  describe('Authentication Flow', () => {
    it('should have consistent authentication patterns', () => {
      const authFiles = [
        'src/lib/auth.ts',
        'src/components/auth/AuthGuard.tsx',
        'src/app/api/auth/[...nextauth]/route.tsx'
      ];
      
      const authIssues = [];
      
      for (const file of authFiles) {
        const fullPath = path.join(process.cwd(), file);
        if (fs.existsSync(fullPath)) {
          const content = fs.readFileSync(fullPath, 'utf8');
          
          // Check for proper session handling
          if (content.includes('session') && !content.includes('getServerSession')) {
            if (!content.includes('useSession')) {
              authIssues.push({
                file,
                issue: 'Session usage without proper session getter'
              });
            }
          }
        }
      }
      
      expect(authIssues).toHaveLength(0);
      if (authIssues.length > 0) {
        console.log('Authentication flow issues:', authIssues);
      }
    });
  });
});

// Helper functions
function findCircularDependencies(srcPath: string): string[] {
  const dependencies = new Map<string, string[]>();
  const visited = new Set<string>();
  const recursionStack = new Set<string>();
  const circularDeps: string[] = [];
  
  function buildDependencyGraph(filePath: string) {
    if (!fs.existsSync(filePath)) return;
    
    const files = fs.readdirSync(filePath, { withFileTypes: true });
    
    for (const file of files) {
      if (file.isDirectory() && !file.name.startsWith('.')) {
        buildDependencyGraph(path.join(filePath, file.name));
      } else if (file.name.endsWith('.ts') || file.name.endsWith('.tsx')) {
        const fullPath = path.join(filePath, file.name);
        const content = fs.readFileSync(fullPath, 'utf8');
        const imports = extractImports(content);
        dependencies.set(fullPath, imports);
      }
    }
  }
  
  function extractImports(content: string): string[] {
    const importRegex = /import.*from\s+['"]([^'"]+)['"]/g;
    const imports: string[] = [];
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      if (match[1].startsWith('./') || match[1].startsWith('../')) {
        imports.push(match[1]);
      }
    }
    
    return imports;
  }
  
  buildDependencyGraph(srcPath);
  
  // Simple circular dependency detection (basic implementation)
  // In a real scenario, you'd want a more sophisticated algorithm
  return circularDeps;
}

function getAllRouteFiles(apiPath: string): string[] {
  const routeFiles: string[] = [];
  
  function traverse(dir: string) {
    if (!fs.existsSync(dir)) return;
    
    const files = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const file of files) {
      if (file.isDirectory()) {
        traverse(path.join(dir, file.name));
      } else if (file.name === 'route.ts' || file.name === 'route.tsx') {
        routeFiles.push(path.join(dir, file.name));
      }
    }
  }
  
  traverse(apiPath);
  return routeFiles;
}

function getAllTsxFiles(componentPath: string): string[] {
  const tsxFiles: string[] = [];
  
  function traverse(dir: string) {
    if (!fs.existsSync(dir)) return;
    
    const files = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const file of files) {
      if (file.isDirectory()) {
        traverse(path.join(dir, file.name));
      } else if (file.name.endsWith('.tsx')) {
        tsxFiles.push(path.join(dir, file.name));
      }
    }
  }
  
  traverse(componentPath);
  return tsxFiles;
}
