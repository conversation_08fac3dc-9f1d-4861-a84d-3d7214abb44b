/**
 * Test suite for Forum Reaction Race Condition Fix
 * 
 * This test verifies that the forum reactions API properly handles
 * concurrent requests without creating duplicate reactions or losing updates.
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { PrismaClient } from '@prisma/client';
import { NextRequest } from 'next/server';
import { POST } from '@/app/api/forum/reactions/route';

// Mock NextAuth
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(),
}));

// Mock CSRF and Rate Limiting
jest.mock('@/lib/csrf', () => ({
  withCSRFProtection: jest.fn((req, handler) => handler()),
}));

jest.mock('@/lib/rateLimit', () => ({
  withRateLimit: jest.fn((req, config, handler) => handler()),
}));

const { getServerSession } = require('next-auth/next');

const prisma = new PrismaClient();

describe('Forum Reaction Race Condition Tests', () => {
  let testUser: any;
  let testPost: any;
  let testReply: any;

  beforeAll(async () => {
    try {
      // Use existing test user instead of creating new one
      testUser = await prisma.user.findFirst({
        where: { email: '<EMAIL>' },
      });

      if (!testUser) {
        // Create test user if it doesn't exist
        testUser = await prisma.user.create({
          data: {
            email: '<EMAIL>',
            name: 'Race Test User',
            emailVerified: new Date(),
          },
        });
      }

      console.log('Using test user:', testUser?.id || 'undefined');

      // Create test post
      testPost = await prisma.forumPost.create({
        data: {
          title: 'Race Condition Test Post',
          content: 'Testing concurrent reactions',
          authorId: testUser.id,
        },
      });

      console.log('Created test post:', testPost.id);

      // Create test reply
      testReply = await prisma.forumReply.create({
        data: {
          content: 'Test reply for race condition',
          authorId: testUser.id,
          postId: testPost.id,
        },
      });

      console.log('Created test reply:', testReply.id);
    } catch (error) {
      console.error('Error in beforeAll:', error);
      throw error;
    }
  });

  afterAll(async () => {
    try {
      // Clean up test data
      if (testUser?.id) {
        await prisma.forumPostReaction.deleteMany({
          where: { userId: testUser.id },
        });
        await prisma.forumReplyReaction.deleteMany({
          where: { userId: testUser.id },
        });
      }
      if (testReply?.id) {
        await prisma.forumReply.deleteMany({
          where: { id: testReply.id },
        });
      }
      if (testPost?.id) {
        await prisma.forumPost.deleteMany({
          where: { id: testPost.id },
        });
      }
      if (testUser?.id) {
        await prisma.user.deleteMany({
          where: { id: testUser.id },
        });
      }
    } catch (error) {
      console.error('Error in afterAll cleanup:', error);
    } finally {
      await prisma.$disconnect();
    }
  });

  beforeEach(async () => {
    // Clean up reactions before each test
    if (testUser?.id) {
      await prisma.forumPostReaction.deleteMany({
        where: { userId: testUser.id },
      });
      await prisma.forumReplyReaction.deleteMany({
        where: { userId: testUser.id },
      });

      // Mock session
      getServerSession.mockResolvedValue({
        user: { id: testUser.id, email: testUser.email },
      });
    }
  });

  describe('Post Reaction Race Conditions', () => {
    it('should handle concurrent post reactions without duplicates', async () => {
      const createRequest = (type: string) => {
        const request = new NextRequest('http://localhost:3000/api/forum/reactions', {
          method: 'POST',
          body: JSON.stringify({
            postId: testPost.id,
            type,
          }),
          headers: {
            'Content-Type': 'application/json',
          },
        });
        return request;
      };

      // Simulate concurrent requests with the same reaction type
      const concurrentRequests = Array.from({ length: 5 }, () =>
        POST(createRequest('LIKE'))
      );

      const results = await Promise.allSettled(concurrentRequests);

      // All requests should succeed
      results.forEach((result) => {
        expect(result.status).toBe('fulfilled');
      });

      // Check that only one reaction exists in the database
      const reactions = await prisma.forumPostReaction.findMany({
        where: {
          userId: testUser.id,
          postId: testPost.id,
        },
      });

      expect(reactions).toHaveLength(1);
      expect(reactions[0].type).toBe('LIKE');
    });

    it('should handle concurrent different reaction types correctly', async () => {
      const createRequest = (type: string) => {
        const request = new NextRequest('http://localhost:3000/api/forum/reactions', {
          method: 'POST',
          body: JSON.stringify({
            postId: testPost.id,
            type,
          }),
          headers: {
            'Content-Type': 'application/json',
          },
        });
        return request;
      };

      // Simulate concurrent requests with different reaction types
      const concurrentRequests = [
        POST(createRequest('LIKE')),
        POST(createRequest('LOVE')),
        POST(createRequest('DISLIKE')),
      ];

      const results = await Promise.allSettled(concurrentRequests);

      // All requests should succeed
      results.forEach((result) => {
        expect(result.status).toBe('fulfilled');
      });

      // Check that only one reaction exists (the last one processed)
      const reactions = await prisma.forumPostReaction.findMany({
        where: {
          userId: testUser.id,
          postId: testPost.id,
        },
      });

      expect(reactions).toHaveLength(1);
      // The reaction type should be one of the requested types
      expect(['LIKE', 'LOVE', 'DISLIKE']).toContain(reactions[0].type);
    });

    it('should handle toggle operations correctly under concurrency', async () => {
      // First, create a reaction
      const initialRequest = new NextRequest('http://localhost:3000/api/forum/reactions', {
        method: 'POST',
        body: JSON.stringify({
          postId: testPost.id,
          type: 'LIKE',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      await POST(initialRequest);

      // Verify reaction was created
      let reactions = await prisma.forumPostReaction.findMany({
        where: {
          userId: testUser.id,
          postId: testPost.id,
        },
      });
      expect(reactions).toHaveLength(1);

      // Now simulate concurrent toggle requests (same reaction type)
      const createToggleRequest = () => {
        return new NextRequest('http://localhost:3000/api/forum/reactions', {
          method: 'POST',
          body: JSON.stringify({
            postId: testPost.id,
            type: 'LIKE',
          }),
          headers: {
            'Content-Type': 'application/json',
          },
        });
      };

      const concurrentToggleRequests = Array.from({ length: 3 }, () =>
        POST(createToggleRequest())
      );

      const results = await Promise.allSettled(concurrentToggleRequests);

      // All requests should succeed
      results.forEach((result) => {
        expect(result.status).toBe('fulfilled');
      });

      // Check final state - should be either 0 or 1 reaction
      reactions = await prisma.forumPostReaction.findMany({
        where: {
          userId: testUser.id,
          postId: testPost.id,
        },
      });

      expect(reactions.length).toBeLessThanOrEqual(1);
    });
  });

  describe('Reply Reaction Race Conditions', () => {
    it('should handle concurrent reply reactions without duplicates', async () => {
      const createRequest = (type: string) => {
        const request = new NextRequest('http://localhost:3000/api/forum/reactions', {
          method: 'POST',
          body: JSON.stringify({
            replyId: testReply.id,
            type,
          }),
          headers: {
            'Content-Type': 'application/json',
          },
        });
        return request;
      };

      // Simulate concurrent requests with the same reaction type
      const concurrentRequests = Array.from({ length: 5 }, () =>
        POST(createRequest('LIKE'))
      );

      const results = await Promise.allSettled(concurrentRequests);

      // All requests should succeed
      results.forEach((result) => {
        expect(result.status).toBe('fulfilled');
      });

      // Check that only one reaction exists in the database
      const reactions = await prisma.forumReplyReaction.findMany({
        where: {
          userId: testUser.id,
          replyId: testReply.id,
        },
      });

      expect(reactions).toHaveLength(1);
      expect(reactions[0].type).toBe('LIKE');
    });
  });
});
