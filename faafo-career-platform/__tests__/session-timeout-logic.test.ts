/**
 * Session Timeout Logic Tests
 * 
 * Tests that session timeout logic in auth.tsx correctly uses the configured
 * session maxAge instead of hardcoded values, ensuring consistency across
 * the authentication system.
 */

import { authOptions } from '@/lib/auth';
import { JWT } from 'next-auth/jwt';

// Mock crypto for testing
const mockCrypto = {
  randomUUID: jest.fn(() => 'test-uuid-123')
};
global.crypto = mockCrypto as any;

describe('Session Timeout Logic', () => {
  const mockToken: JWT = {
    sub: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60),
    jti: 'test-jti'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Session Configuration Consistency', () => {
    it('should have consistent session maxAge values', () => {
      // Verify that session.maxAge, jwt.maxAge, and cookie maxAge are all consistent
      const sessionMaxAge = authOptions.session?.maxAge;
      const jwtMaxAge = authOptions.jwt?.maxAge;
      const cookieMaxAge = authOptions.cookies?.sessionToken?.options?.maxAge;

      expect(sessionMaxAge).toBe(30 * 24 * 60 * 60); // 30 days
      expect(jwtMaxAge).toBe(30 * 24 * 60 * 60); // 30 days
      expect(cookieMaxAge).toBe(30 * 24 * 60 * 60); // 30 days

      // All should be equal
      expect(sessionMaxAge).toBe(jwtMaxAge);
      expect(sessionMaxAge).toBe(cookieMaxAge);
    });

    it('should have proper session strategy configuration', () => {
      expect(authOptions.session?.strategy).toBe('jwt');
      expect(authOptions.session?.updateAge).toBe(24 * 60 * 60); // 24 hours
    });
  });

  describe('JWT Callback Session Timeout Logic', () => {
    it('should allow valid sessions within timeout period', async () => {
      const now = Math.floor(Date.now() / 1000);
      const validToken = {
        ...mockToken,
        iat: now - (15 * 24 * 60 * 60), // 15 days ago (within 30-day limit)
        lastActivity: now - (1 * 60 * 60) // 1 hour ago
      };

      const jwtCallback = authOptions.callbacks?.jwt;
      expect(jwtCallback).toBeDefined();

      if (jwtCallback) {
        // Should not throw an error for valid session
        await expect(
          jwtCallback({ token: validToken, trigger: 'update' })
        ).resolves.toBeDefined();
      }
    });

    it('should reject expired sessions beyond maxAge', async () => {
      const now = Math.floor(Date.now() / 1000);
      const expiredToken = {
        ...mockToken,
        iat: now - (31 * 24 * 60 * 60), // 31 days ago (beyond 30-day limit)
        lastActivity: now - (31 * 24 * 60 * 60)
      };

      const jwtCallback = authOptions.callbacks?.jwt;
      expect(jwtCallback).toBeDefined();

      if (jwtCallback) {
        // Should throw an error for expired session
        await expect(
          jwtCallback({ token: expiredToken, trigger: 'update' })
        ).rejects.toThrow('Session expired');
      }
    });

    it('should generate new session ID on sign in', async () => {
      const jwtCallback = authOptions.callbacks?.jwt;
      expect(jwtCallback).toBeDefined();

      if (jwtCallback) {
        const result = await jwtCallback({ 
          token: mockToken, 
          trigger: 'signIn',
          user: { id: 'test-user', email: '<EMAIL>', name: 'Test User' }
        });

        expect(result.sessionId).toBeDefined();
        expect(typeof result.sessionId).toBe('string');
        expect(result.iat).toBeDefined();
        expect(result.lastActivity).toBeDefined();
      }
    });

    it('should handle session regeneration interval correctly', async () => {
      const now = Math.floor(Date.now() / 1000);
      const tokenNeedingRegeneration = {
        ...mockToken,
        iat: now - (1 * 24 * 60 * 60), // 1 day ago
        lastActivity: now - (8 * 24 * 60 * 60), // 8 days ago (beyond 7-day regeneration)
        sessionId: 'old-session-id'
      };

      const jwtCallback = authOptions.callbacks?.jwt;
      expect(jwtCallback).toBeDefined();

      if (jwtCallback) {
        const result = await jwtCallback({ 
          token: tokenNeedingRegeneration, 
          trigger: 'update' 
        });

        // Should generate new session ID
        expect(result.sessionId).toBeDefined();
        expect(typeof result.sessionId).toBe('string');
        expect(result.lastActivity).toBeDefined();
      }
    });
  });

  describe('Session Timeout Edge Cases', () => {
    it('should handle missing iat gracefully', async () => {
      const tokenWithoutIat = {
        ...mockToken,
        iat: undefined
      };

      const jwtCallback = authOptions.callbacks?.jwt;
      expect(jwtCallback).toBeDefined();

      if (jwtCallback) {
        // Should not throw error when iat is missing
        await expect(
          jwtCallback({ token: tokenWithoutIat, trigger: 'update' })
        ).resolves.toBeDefined();
      }
    });

    it('should handle invalid iat type gracefully', async () => {
      const tokenWithInvalidIat = {
        ...mockToken,
        iat: 'invalid-timestamp' as any
      };

      const jwtCallback = authOptions.callbacks?.jwt;
      expect(jwtCallback).toBeDefined();

      if (jwtCallback) {
        // Should not throw error when iat is invalid type
        await expect(
          jwtCallback({ token: tokenWithInvalidIat, trigger: 'update' })
        ).resolves.toBeDefined();
      }
    });

    it('should handle exactly at timeout boundary', async () => {
      const now = Math.floor(Date.now() / 1000);
      const boundaryToken = {
        ...mockToken,
        iat: now - (30 * 24 * 60 * 60) - 1, // Just over 30 days ago
        lastActivity: now - (30 * 24 * 60 * 60) - 1
      };

      const jwtCallback = authOptions.callbacks?.jwt;
      expect(jwtCallback).toBeDefined();

      if (jwtCallback) {
        // Should throw error when just over boundary
        await expect(
          jwtCallback({ token: boundaryToken, trigger: 'update' })
        ).rejects.toThrow('Session expired');
      }
    });
  });

  describe('Session Configuration Constants', () => {
    it('should use centralized session configuration', () => {
      // This test ensures that the session timeout logic uses the same
      // constants as the session configuration, preventing the bug where
      // hardcoded values could get out of sync

      const sessionConfig = authOptions.session;
      const jwtConfig = authOptions.jwt;
      const cookieConfig = authOptions.cookies?.sessionToken?.options;

      // All timeout values should be consistent
      expect(sessionConfig?.maxAge).toBe(jwtConfig?.maxAge);
      expect(sessionConfig?.maxAge).toBe(cookieConfig?.maxAge);

      // Verify the specific values are correct
      expect(sessionConfig?.maxAge).toBe(30 * 24 * 60 * 60); // 30 days
      expect(sessionConfig?.updateAge).toBe(24 * 60 * 60); // 24 hours
    });
  });

  describe('Cookie Configuration', () => {
    it('should have secure cookie settings for production', () => {
      // Note: The secure setting is evaluated at module load time,
      // so we test the current configuration
      const cookieConfig = authOptions.cookies?.sessionToken?.options;

      expect(cookieConfig?.httpOnly).toBe(true);
      expect(cookieConfig?.sameSite).toBe('lax');
      expect(cookieConfig?.path).toBe('/');
      // Secure setting depends on NODE_ENV at module load time
      expect(typeof cookieConfig?.secure).toBe('boolean');
    });

    it('should have appropriate cookie settings for development', () => {
      // Note: The domain setting is evaluated at module load time,
      // so we test the current configuration
      const cookieConfig = authOptions.cookies?.sessionToken?.options;

      expect(cookieConfig?.httpOnly).toBe(true);
      expect(cookieConfig?.sameSite).toBe('lax');
      expect(cookieConfig?.path).toBe('/');
      // Domain setting depends on NODE_ENV at module load time
      // In development it should be 'localhost' or undefined
      expect(cookieConfig?.domain === 'localhost' || cookieConfig?.domain === undefined).toBe(true);
    });
  });
});

describe('Session Timeout Integration', () => {
  it('should maintain consistency across all session-related configurations', () => {
    // This is the key test that verifies the bug fix
    // All session timeout values should reference the same source of truth

    const sessionMaxAge = authOptions.session?.maxAge;
    const jwtMaxAge = authOptions.jwt?.maxAge;
    const cookieMaxAge = authOptions.cookies?.sessionToken?.options?.maxAge;

    // Verify all are defined
    expect(sessionMaxAge).toBeDefined();
    expect(jwtMaxAge).toBeDefined();
    expect(cookieMaxAge).toBeDefined();

    // Verify all are equal (the main bug fix)
    expect(sessionMaxAge).toBe(jwtMaxAge);
    expect(sessionMaxAge).toBe(cookieMaxAge);
    expect(jwtMaxAge).toBe(cookieMaxAge);

    // Verify the actual value is correct
    expect(sessionMaxAge).toBe(30 * 24 * 60 * 60); // 30 days in seconds
  });
});
