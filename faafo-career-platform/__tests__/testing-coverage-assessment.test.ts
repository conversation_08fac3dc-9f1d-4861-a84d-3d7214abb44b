/**
 * Phase 6: Testing Coverage and Quality Assessment
 * 
 * This test suite analyzes the testing infrastructure and identifies gaps
 * in test coverage, quality, and reliability following VDD protocol.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

describe('Testing Coverage and Quality Assessment', () => {
  describe('Test Infrastructure Analysis', () => {
    it('should have comprehensive test coverage for critical components', async () => {
      console.log('\n🔍 ANALYZING TEST COVERAGE');
      console.log('============================');

      // Critical components that must have tests
      const criticalComponents = [
        'src/components/LoginForm.tsx',
        'src/components/SignupForm.tsx',
        'src/components/assessment/AssessmentResults.tsx',
        'src/components/dashboard/PersonalDashboard.tsx',
        'src/lib/auth.ts',
        'src/lib/db.ts',
        'src/lib/services/geminiService.ts',
        'src/app/api/auth/[...nextauth]/route.tsx',
        'src/app/api/assessment/route.ts',
        'src/app/api/profile/route.ts'
      ];

      const missingTests: string[] = [];
      const testCoverageIssues: Array<{file: string, issue: string}> = [];

      for (const component of criticalComponents) {
        const componentPath = path.join(process.cwd(), component);
        if (!fs.existsSync(componentPath)) {
          continue; // Skip if component doesn't exist
        }

        // Check for corresponding test file
        const testPaths = [
          component.replace('src/', '__tests__/').replace('.tsx', '.test.tsx').replace('.ts', '.test.ts'),
          component.replace('src/', '__tests__/').replace('.tsx', '.test.ts').replace('.ts', '.test.ts'),
          component.replace('src/components/', '__tests__/components/').replace('.tsx', '.test.tsx'),
          component.replace('src/lib/', '__tests__/lib/').replace('.ts', '.test.ts'),
          component.replace('src/app/', '__tests__/api/').replace('/route.ts', '.api.test.ts').replace('/route.tsx', '.api.test.ts')
        ];

        let hasTest = false;
        for (const testPath of testPaths) {
          if (fs.existsSync(path.join(process.cwd(), testPath))) {
            hasTest = true;
            break;
          }
        }

        if (!hasTest) {
          missingTests.push(component);
        }
      }

      // Analyze existing test quality
      const testFiles = execSync('find __tests__ -name "*.test.ts" -o -name "*.test.tsx"', { encoding: 'utf8' })
        .split('\n')
        .filter(file => file.trim());

      for (const testFile of testFiles) {
        if (!testFile.trim()) continue;
        
        try {
          const testContent = fs.readFileSync(testFile, 'utf8');
          
          // Check for common test quality issues
          if (!testContent.includes('describe(')) {
            testCoverageIssues.push({
              file: testFile,
              issue: 'Missing test structure (no describe blocks)'
            });
          }

          if (!testContent.includes('expect(')) {
            testCoverageIssues.push({
              file: testFile,
              issue: 'Missing assertions (no expect statements)'
            });
          }

          // Check for mock usage that might hide real issues
          const mockCount = (testContent.match(/\.mock/g) || []).length;
          const realApiCallCount = (testContent.match(/fetch\(/g) || []).length;
          
          if (mockCount > realApiCallCount * 2) {
            testCoverageIssues.push({
              file: testFile,
              issue: 'Over-reliance on mocks - may hide integration issues'
            });
          }

          // Check for edge case testing
          if (!testContent.includes('edge case') && !testContent.includes('boundary') && !testContent.includes('error')) {
            testCoverageIssues.push({
              file: testFile,
              issue: 'Missing edge case and error handling tests'
            });
          }
        } catch (error) {
          testCoverageIssues.push({
            file: testFile,
            issue: `Cannot analyze test file: ${error}`
          });
        }
      }

      console.log(`\n📊 Test Coverage Analysis:`);
      console.log(`- Total test files: ${testFiles.length}`);
      console.log(`- Critical components: ${criticalComponents.length}`);
      console.log(`- Missing tests: ${missingTests.length}`);
      console.log(`- Quality issues: ${testCoverageIssues.length}`);

      if (missingTests.length > 0) {
        console.log('\n❌ Missing Tests:');
        missingTests.forEach(component => console.log(`  - ${component}`));
      }

      if (testCoverageIssues.length > 0) {
        console.log('\n⚠️ Test Quality Issues:');
        testCoverageIssues.slice(0, 10).forEach(issue => 
          console.log(`  - ${issue.file}: ${issue.issue}`)
        );
      }

      // Fail if critical gaps exist
      expect(missingTests.length).toBeLessThan(3); // Allow some missing tests but not too many
      expect(testCoverageIssues.length).toBeLessThan(20); // Allow some issues but not excessive
    });

    it('should have reliable test execution without flaky tests', async () => {
      console.log('\n🔄 ANALYZING TEST RELIABILITY');
      console.log('==============================');

      const flakyTestIndicators: Array<{file: string, issue: string}> = [];
      
      // Check for common flaky test patterns
      const testFiles = execSync('find __tests__ -name "*.test.ts" -o -name "*.test.tsx"', { encoding: 'utf8' })
        .split('\n')
        .filter(file => file.trim());

      for (const testFile of testFiles) {
        if (!testFile.trim()) continue;
        
        try {
          const testContent = fs.readFileSync(testFile, 'utf8');
          
          // Check for timing-dependent tests
          if (testContent.includes('setTimeout') || testContent.includes('setInterval')) {
            flakyTestIndicators.push({
              file: testFile,
              issue: 'Uses setTimeout/setInterval - potential timing issues'
            });
          }

          // Check for hardcoded waits
          if (testContent.includes('sleep(') || testContent.includes('delay(')) {
            flakyTestIndicators.push({
              file: testFile,
              issue: 'Uses hardcoded delays - unreliable timing'
            });
          }

          // Check for missing waitFor usage
          if (testContent.includes('async') && !testContent.includes('waitFor') && testContent.includes('expect')) {
            flakyTestIndicators.push({
              file: testFile,
              issue: 'Async test without proper waiting - may be flaky'
            });
          }

          // Check for network-dependent tests without proper mocking
          if (testContent.includes('fetch(') && !testContent.includes('mock')) {
            flakyTestIndicators.push({
              file: testFile,
              issue: 'Network calls without mocking - environment dependent'
            });
          }
        } catch (error) {
          // Skip files that can't be read
        }
      }

      console.log(`\n📊 Test Reliability Analysis:`);
      console.log(`- Potential flaky test indicators: ${flakyTestIndicators.length}`);

      if (flakyTestIndicators.length > 0) {
        console.log('\n⚠️ Flaky Test Indicators:');
        flakyTestIndicators.slice(0, 10).forEach(indicator => 
          console.log(`  - ${indicator.file}: ${indicator.issue}`)
        );
      }

      expect(flakyTestIndicators.length).toBeLessThan(15); // Allow some but not excessive
    });

    it('should have proper test organization and structure', async () => {
      console.log('\n📁 ANALYZING TEST ORGANIZATION');
      console.log('===============================');

      const organizationIssues: Array<{issue: string, details: string}> = [];

      // Check test directory structure
      const testDirs = ['__tests__', 'e2e', 'tests'];
      const existingTestDirs = testDirs.filter(dir => fs.existsSync(dir));

      if (existingTestDirs.length > 2) {
        organizationIssues.push({
          issue: 'Multiple test directories',
          details: `Found: ${existingTestDirs.join(', ')} - should consolidate`
        });
      }

      // Check for Playwright/Jest mixing issues
      try {
        const e2eFiles = execSync('find . -name "*.spec.ts" -o -name "*.spec.tsx" 2>/dev/null || true', { encoding: 'utf8' })
          .split('\n')
          .filter(file => file.trim());

        if (e2eFiles.length > 0) {
          organizationIssues.push({
            issue: 'Playwright tests in Jest directory',
            details: `Found ${e2eFiles.length} .spec files that should be separated from Jest tests`
          });
        }
      } catch (error) {
        // Ignore if find command fails
      }

      // Check for test naming consistency
      const testFiles = execSync('find __tests__ -name "*.test.ts" -o -name "*.test.tsx"', { encoding: 'utf8' })
        .split('\n')
        .filter(file => file.trim());

      const namingInconsistencies = testFiles.filter(file => {
        const basename = path.basename(file);
        return !basename.includes('.test.') && !basename.includes('.spec.');
      });

      if (namingInconsistencies.length > 0) {
        organizationIssues.push({
          issue: 'Inconsistent test file naming',
          details: `${namingInconsistencies.length} files don't follow .test.* pattern`
        });
      }

      console.log(`\n📊 Test Organization Analysis:`);
      console.log(`- Test directories: ${existingTestDirs.join(', ')}`);
      console.log(`- Organization issues: ${organizationIssues.length}`);

      if (organizationIssues.length > 0) {
        console.log('\n⚠️ Organization Issues:');
        organizationIssues.forEach(issue => 
          console.log(`  - ${issue.issue}: ${issue.details}`)
        );
      }

      expect(organizationIssues.length).toBeLessThan(5); // Allow some minor issues
    });
  });

  describe('Test Quality Metrics', () => {
    it('should have adequate test coverage for business logic', async () => {
      console.log('\n📈 ANALYZING BUSINESS LOGIC COVERAGE');
      console.log('====================================');

      const businessLogicFiles = [
        'src/lib/auth.ts',
        'src/lib/db.ts',
        'src/lib/services/',
        'src/lib/skills/',
        'src/lib/security/',
        'src/app/api/'
      ];

      const coverageGaps: Array<{file: string, issue: string}> = [];

      for (const logicPath of businessLogicFiles) {
        const fullPath = path.join(process.cwd(), logicPath);
        
        if (fs.existsSync(fullPath)) {
          const stats = fs.statSync(fullPath);
          
          if (stats.isDirectory()) {
            // Check directory for files
            const files = fs.readdirSync(fullPath)
              .filter(file => file.endsWith('.ts') || file.endsWith('.tsx'))
              .filter(file => !file.includes('.test.') && !file.includes('.spec.'));

            for (const file of files) {
              const filePath = path.join(logicPath, file);
              const testPath = filePath.replace('src/', '__tests__/').replace('.ts', '.test.ts');
              
              if (!fs.existsSync(testPath)) {
                coverageGaps.push({
                  file: filePath,
                  issue: 'No corresponding test file'
                });
              }
            }
          } else {
            // Single file
            const testPath = logicPath.replace('src/', '__tests__/').replace('.ts', '.test.ts');
            if (!fs.existsSync(testPath)) {
              coverageGaps.push({
                file: logicPath,
                issue: 'No corresponding test file'
              });
            }
          }
        }
      }

      console.log(`\n📊 Business Logic Coverage:`);
      console.log(`- Coverage gaps found: ${coverageGaps.length}`);

      if (coverageGaps.length > 0) {
        console.log('\n❌ Coverage Gaps:');
        coverageGaps.slice(0, 10).forEach(gap => 
          console.log(`  - ${gap.file}: ${gap.issue}`)
        );
      }

      expect(coverageGaps.length).toBeLessThan(10); // Allow some gaps but not excessive
    });
  });
});
