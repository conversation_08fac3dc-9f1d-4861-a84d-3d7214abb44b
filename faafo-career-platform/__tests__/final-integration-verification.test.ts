/**
 * Phase 7: Final Integration and Delivery Verification
 * 
 * This test suite performs final verification of all fixes and ensures
 * the system is ready for delivery following VDD protocol.
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

describe('Final Integration and Delivery Verification', () => {
  describe('Build and Compilation Verification', () => {
    it('should build successfully without errors', async () => {
      console.log('\n🔨 VERIFYING BUILD PROCESS');
      console.log('===========================');

      let buildResult: string;
      let buildSuccess = false;

      try {
        buildResult = execSync('npm run build', { 
          encoding: 'utf8',
          timeout: 120000 // 2 minutes timeout
        });
        buildSuccess = true;
        console.log('✅ Build completed successfully');
      } catch (error: any) {
        buildResult = error.stdout + error.stderr;
        console.log('❌ Build failed');
        console.log('Build output:', buildResult);
      }

      // Analyze build output for specific issues
      const buildIssues: string[] = [];
      
      if (buildResult.includes('Type error')) {
        buildIssues.push('TypeScript compilation errors');
      }
      if (buildResult.includes('Module not found')) {
        buildIssues.push('Missing module dependencies');
      }
      if (buildResult.includes('Cannot resolve')) {
        buildIssues.push('Module resolution issues');
      }
      if (buildResult.includes('Syntax error')) {
        buildIssues.push('JavaScript/TypeScript syntax errors');
      }

      console.log(`\n📊 Build Analysis:`);
      console.log(`- Build successful: ${buildSuccess}`);
      console.log(`- Issues found: ${buildIssues.length}`);

      if (buildIssues.length > 0) {
        console.log('\n❌ Build Issues:');
        buildIssues.forEach(issue => console.log(`  - ${issue}`));
      }

      expect(buildSuccess).toBe(true);
      expect(buildIssues.length).toBe(0);
    });

    it('should have no TypeScript errors', async () => {
      console.log('\n📝 VERIFYING TYPESCRIPT COMPILATION');
      console.log('====================================');

      let typeCheckResult: string;
      let typeCheckSuccess = false;

      try {
        typeCheckResult = execSync('npx tsc --noEmit', { 
          encoding: 'utf8',
          timeout: 60000
        });
        typeCheckSuccess = true;
        console.log('✅ TypeScript compilation successful');
      } catch (error: any) {
        typeCheckResult = error.stdout + error.stderr;
        console.log('❌ TypeScript compilation failed');
      }

      const typeErrors = typeCheckResult.split('\n')
        .filter(line => line.includes('error TS'))
        .slice(0, 10); // Show first 10 errors

      console.log(`\n📊 TypeScript Analysis:`);
      console.log(`- Type check successful: ${typeCheckSuccess}`);
      console.log(`- Type errors found: ${typeErrors.length}`);

      if (typeErrors.length > 0) {
        console.log('\n❌ Type Errors:');
        typeErrors.forEach(error => console.log(`  - ${error.trim()}`));
      }

      expect(typeCheckSuccess).toBe(true);
      expect(typeErrors.length).toBe(0);
    });
  });

  describe('Critical Functionality Verification', () => {
    it('should have all critical API routes accessible', async () => {
      console.log('\n🌐 VERIFYING API ROUTES');
      console.log('========================');

      const criticalRoutes = [
        'src/app/api/auth/[...nextauth]/route.tsx',
        'src/app/api/assessment/route.ts',
        'src/app/api/profile/route.ts',
        'src/app/api/skills/route.ts',
        'src/app/api/analytics/route.ts'
      ];

      const routeIssues: Array<{route: string, issue: string}> = [];

      for (const route of criticalRoutes) {
        const routePath = path.join(process.cwd(), route);
        
        if (!fs.existsSync(routePath)) {
          routeIssues.push({
            route,
            issue: 'Route file does not exist'
          });
          continue;
        }

        try {
          const routeContent = fs.readFileSync(routePath, 'utf8');
          
          // Check for required exports
          if (!routeContent.includes('export') || 
              (!routeContent.includes('GET') && !routeContent.includes('POST'))) {
            routeIssues.push({
              route,
              issue: 'Missing required HTTP method exports'
            });
          }

          // Check for error handling
          if (!routeContent.includes('withUnifiedErrorHandling') && 
              !routeContent.includes('try') && 
              !routeContent.includes('catch')) {
            routeIssues.push({
              route,
              issue: 'Missing error handling'
            });
          }
        } catch (error) {
          routeIssues.push({
            route,
            issue: `Cannot read route file: ${error}`
          });
        }
      }

      console.log(`\n📊 API Routes Analysis:`);
      console.log(`- Critical routes checked: ${criticalRoutes.length}`);
      console.log(`- Route issues found: ${routeIssues.length}`);

      if (routeIssues.length > 0) {
        console.log('\n❌ Route Issues:');
        routeIssues.forEach(issue => 
          console.log(`  - ${issue.route}: ${issue.issue}`)
        );
      }

      expect(routeIssues.length).toBe(0);
    });

    it('should have security measures in place', async () => {
      console.log('\n🔒 VERIFYING SECURITY MEASURES');
      console.log('===============================');

      const securityChecks: Array<{check: string, passed: boolean, details?: string}> = [];

      // Check for authentication middleware
      const middlewarePath = path.join(process.cwd(), 'src/middleware.ts');
      if (fs.existsSync(middlewarePath)) {
        const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');
        securityChecks.push({
          check: 'Authentication middleware exists',
          passed: middlewareContent.includes('auth') || middlewareContent.includes('session'),
          details: 'Middleware file found and contains auth logic'
        });
      } else {
        securityChecks.push({
          check: 'Authentication middleware exists',
          passed: false,
          details: 'No middleware.ts file found'
        });
      }

      // Check for CSRF protection
      const nextConfigPath = path.join(process.cwd(), 'next.config.js');
      if (fs.existsSync(nextConfigPath)) {
        const configContent = fs.readFileSync(nextConfigPath, 'utf8');
        securityChecks.push({
          check: 'CSRF protection configured',
          passed: configContent.includes('csrf') || configContent.includes('security'),
          details: 'Next.js config contains security settings'
        });
      }

      // Check for input validation
      const validationFiles = [
        'src/lib/security/SecurityValidator.ts',
        'src/lib/validation.ts'
      ];
      
      let hasValidation = false;
      for (const validationFile of validationFiles) {
        if (fs.existsSync(path.join(process.cwd(), validationFile))) {
          hasValidation = true;
          break;
        }
      }

      securityChecks.push({
        check: 'Input validation system',
        passed: hasValidation,
        details: hasValidation ? 'Validation system found' : 'No validation system found'
      });

      const passedChecks = securityChecks.filter(check => check.passed).length;

      console.log(`\n📊 Security Analysis:`);
      console.log(`- Security checks: ${securityChecks.length}`);
      console.log(`- Passed checks: ${passedChecks}`);

      securityChecks.forEach(check => {
        const status = check.passed ? '✅' : '❌';
        console.log(`  ${status} ${check.check}: ${check.details || ''}`);
      });

      expect(passedChecks).toBeGreaterThanOrEqual(2); // At least 2 out of 3 security measures
    });
  });

  describe('Performance and Quality Verification', () => {
    it('should have optimized bundle size', async () => {
      console.log('\n📦 VERIFYING BUNDLE OPTIMIZATION');
      console.log('=================================');

      // Check if build artifacts exist
      const buildDir = path.join(process.cwd(), '.next');
      const buildExists = fs.existsSync(buildDir);

      if (!buildExists) {
        console.log('⚠️ No build artifacts found - running build first');
        try {
          execSync('npm run build', { encoding: 'utf8', timeout: 120000 });
        } catch (error) {
          console.log('❌ Build failed during bundle analysis');
        }
      }

      const bundleIssues: string[] = [];

      // Check for common bundle optimization issues
      const packageJsonPath = path.join(process.cwd(), 'package.json');
      if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        
        // Check for large dependencies that might bloat bundle
        const largeDependencies = ['lodash', 'moment', 'jquery'];
        const foundLargeDeps = largeDependencies.filter(dep => 
          packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]
        );

        if (foundLargeDeps.length > 0) {
          bundleIssues.push(`Large dependencies found: ${foundLargeDeps.join(', ')}`);
        }
      }

      // Check Next.js config for optimization settings
      const nextConfigPath = path.join(process.cwd(), 'next.config.js');
      if (fs.existsSync(nextConfigPath)) {
        const configContent = fs.readFileSync(nextConfigPath, 'utf8');
        
        if (!configContent.includes('compress') && !configContent.includes('optimization')) {
          bundleIssues.push('Missing bundle optimization configuration');
        }
      }

      console.log(`\n📊 Bundle Analysis:`);
      console.log(`- Build artifacts exist: ${buildExists}`);
      console.log(`- Bundle issues: ${bundleIssues.length}`);

      if (bundleIssues.length > 0) {
        console.log('\n⚠️ Bundle Issues:');
        bundleIssues.forEach(issue => console.log(`  - ${issue}`));
      }

      expect(bundleIssues.length).toBeLessThan(3); // Allow some minor issues
    });
  });

  describe('Delivery Readiness', () => {
    it('should pass all delivery checklist items', async () => {
      console.log('\n✅ FINAL DELIVERY CHECKLIST');
      console.log('============================');

      const deliveryChecklist: Array<{item: string, passed: boolean, details: string}> = [];

      // 1. Build success
      let buildPassed = false;
      try {
        execSync('npm run build', { encoding: 'utf8', timeout: 60000 });
        buildPassed = true;
      } catch (error) {
        // Build failed
      }

      deliveryChecklist.push({
        item: 'Build Success',
        passed: buildPassed,
        details: buildPassed ? 'Build completes without errors' : 'Build fails with errors'
      });

      // 2. Critical tests pass
      let criticalTestsPassed = false;
      try {
        const testResult = execSync('npm test -- __tests__/architecture-review.test.ts --passWithNoTests', { 
          encoding: 'utf8', 
          timeout: 30000 
        });
        criticalTestsPassed = !testResult.includes('FAIL');
      } catch (error) {
        // Tests failed
      }

      deliveryChecklist.push({
        item: 'Critical Tests Pass',
        passed: criticalTestsPassed,
        details: criticalTestsPassed ? 'Architecture tests pass' : 'Architecture tests fail'
      });

      // 3. No TypeScript errors
      let typeScriptPassed = false;
      try {
        execSync('npx tsc --noEmit', { encoding: 'utf8', timeout: 30000 });
        typeScriptPassed = true;
      } catch (error) {
        // TypeScript errors exist
      }

      deliveryChecklist.push({
        item: 'TypeScript Compilation',
        passed: typeScriptPassed,
        details: typeScriptPassed ? 'No TypeScript errors' : 'TypeScript errors exist'
      });

      // 4. Security measures
      const hasAuth = fs.existsSync(path.join(process.cwd(), 'src/middleware.ts'));
      const hasValidation = fs.existsSync(path.join(process.cwd(), 'src/lib/security/SecurityValidator.ts'));
      const securityPassed = hasAuth && hasValidation;

      deliveryChecklist.push({
        item: 'Security Measures',
        passed: securityPassed,
        details: securityPassed ? 'Auth and validation in place' : 'Missing security components'
      });

      const passedItems = deliveryChecklist.filter(item => item.passed).length;
      const totalItems = deliveryChecklist.length;

      console.log(`\n📊 Delivery Readiness: ${passedItems}/${totalItems} items passed`);
      console.log('');

      deliveryChecklist.forEach(item => {
        const status = item.passed ? '✅' : '❌';
        console.log(`  ${status} ${item.item}: ${item.details}`);
      });

      console.log('\n🎯 DELIVERY STATUS:');
      if (passedItems === totalItems) {
        console.log('✅ READY FOR DELIVERY - All checklist items passed');
      } else {
        console.log(`⚠️ NOT READY - ${totalItems - passedItems} items need attention`);
      }

      expect(passedItems).toBeGreaterThanOrEqual(3); // At least 3 out of 4 must pass
    });
  });
});
