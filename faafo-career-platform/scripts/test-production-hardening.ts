#!/usr/bin/env tsx

/**
 * Production Hardening Test Script
 * 
 * Demonstrates and tests the comprehensive production hardening system
 * including edge case testing, performance monitoring, and security scanning.
 */

import { productionHardeningOrchestrator } from '../src/lib/production-hardening-orchestrator';
import { edgeCaseTestingFramework } from '../src/lib/edge-case-testing';
import { performanceMonitor } from '../src/lib/performance-monitoring';
import { securityHardening } from '../src/lib/security-hardening';
import { consolidatedCache } from '../src/lib/services/consolidated-cache-service';

async function main() {
  console.log('🚀 FAAFO Production Hardening Test Suite');
  console.log('=========================================\n');

  try {
    // Test 1: Unified Caching Service
    console.log('📋 Test 1: Unified Caching Service Functionality');
    console.log('------------------------------------------------');
    
    // Test basic caching operations
    UnifiedCachingService.set('test-key-1', 'test-value-1');
    UnifiedCachingService.set('test-key-2', { data: 'complex-object', timestamp: Date.now() });
    UnifiedCachingService.set('test-key-3', 'tagged-value', { tags: ['test', 'demo'] });

    const value1 = UnifiedCachingService.get('test-key-1');
    const value2 = UnifiedCachingService.get('test-key-2');
    const value3 = UnifiedCachingService.get('test-key-3');

    console.log(`✅ Basic caching: ${value1 === 'test-value-1' ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Object caching: ${value2 && typeof value2 === 'object' ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Tagged caching: ${value3 === 'tagged-value' ? 'PASSED' : 'FAILED'}`);

    // Test cache statistics
    const stats = UnifiedCachingService.getStats();
    console.log(`📊 Cache Stats: ${stats.size} entries, ${(stats.hitRate * 100).toFixed(1)}% hit rate`);

    // Test interview practice specific caching
    UnifiedCachingService.cacheInterviewSession('test-session', {
      id: 'test-session',
      status: 'IN_PROGRESS',
      currentQuestionIndex: 0
    });

    UnifiedCachingService.cacheInterviewQuestions('test-session', [
      { id: 'q1', text: 'Tell me about yourself', type: 'BEHAVIORAL' },
      { id: 'q2', text: 'What is your greatest strength?', type: 'BEHAVIORAL' }
    ]);

    const cachedSession = UnifiedCachingService.getCachedInterviewSession('test-session');
    const cachedQuestions = UnifiedCachingService.getCachedInterviewQuestions('test-session');

    console.log(`✅ Interview session caching: ${cachedSession ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Interview questions caching: ${cachedQuestions && cachedQuestions.length === 2 ? 'PASSED' : 'FAILED'}`);

    console.log('\n');

    // Test 2: Edge Case Testing Framework
    console.log('📋 Test 2: Edge Case Testing Framework');
    console.log('--------------------------------------');

    const edgeCaseResults = await edgeCaseTestingFramework.runAllTests();
    
    console.log(`📊 Edge Case Test Results:`);
    console.log(`   Total Tests: ${edgeCaseResults.totalTests}`);
    console.log(`   Passed: ${edgeCaseResults.passedTests}`);
    console.log(`   Failed: ${edgeCaseResults.failedTests}`);
    console.log(`   Recovery Success Rate: ${edgeCaseResults.recoverySuccessRate.toFixed(1)}%`);
    console.log(`   Execution Time: ${edgeCaseResults.totalExecutionTime}ms`);

    const passRate = (edgeCaseResults.passedTests / edgeCaseResults.totalTests) * 100;
    console.log(`✅ Edge Case Testing: ${passRate >= 80 ? 'PASSED' : 'NEEDS IMPROVEMENT'} (${passRate.toFixed(1)}%)`);

    console.log('\n');

    // Test 3: Performance Monitoring
    console.log('📋 Test 3: Performance Monitoring System');
    console.log('----------------------------------------');

    // Start monitoring
    performanceMonitor.startMonitoring(5000); // 5 second intervals

    // Simulate some operations
    for (let i = 0; i < 50; i++) {
      const startTime = Date.now();
      UnifiedCachingService.set(`perf-test-${i}`, `value-${i}`);
      const value = UnifiedCachingService.get(`perf-test-${i}`);
      const responseTime = Date.now() - startTime;
      
      performanceMonitor.recordOperation('cache-operation', responseTime, value !== null);
    }

    // Wait a moment for monitoring to collect data
    await new Promise(resolve => setTimeout(resolve, 2000));

    const performanceStatus = performanceMonitor.getPerformanceStatus();
    console.log(`📊 Performance Status:`);
    console.log(`   Health Score: ${performanceStatus.healthScore}/100`);
    console.log(`   Is Healthy: ${performanceStatus.isHealthy ? 'YES' : 'NO'}`);
    console.log(`   Recent Alerts: ${performanceStatus.recentAlerts.length}`);
    console.log(`   Recent Optimizations: ${performanceStatus.recentOptimizations.length}`);

    if (performanceStatus.currentMetrics) {
      const metrics = performanceStatus.currentMetrics;
      console.log(`   Cache Hit Rate: ${metrics.cacheHitRate.toFixed(1)}%`);
      console.log(`   Average Response Time: ${metrics.averageResponseTime.toFixed(1)}ms`);
      console.log(`   Memory Usage: ${(metrics.memoryUsage / 1024 / 1024).toFixed(1)}MB`);
    }

    console.log(`✅ Performance Monitoring: ${performanceStatus.isHealthy ? 'PASSED' : 'NEEDS ATTENTION'}`);

    console.log('\n');

    // Test 4: Security Hardening
    console.log('📋 Test 4: Security Hardening System');
    console.log('------------------------------------');

    const securityResults = await securityHardening.performSecurityScan('ALL');
    
    console.log(`📊 Security Scan Results:`);
    console.log(`   Scan ID: ${securityResults.scanId}`);
    console.log(`   Vulnerabilities Found: ${securityResults.vulnerabilities.length}`);
    console.log(`   Risk Score: ${securityResults.riskScore}/100`);
    console.log(`   Scan Duration: ${securityResults.scanDuration}ms`);

    if (securityResults.vulnerabilities.length > 0) {
      console.log(`   Vulnerability Breakdown:`);
      const severityCounts = securityResults.vulnerabilities.reduce((acc, vuln) => {
        acc[vuln.severity] = (acc[vuln.severity] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      Object.entries(severityCounts).forEach(([severity, count]) => {
        console.log(`     ${severity}: ${count}`);
      });
    }

    // Test input sanitization
    const maliciousInputs = [
      '<script>alert("xss")</script>',
      "'; DROP TABLE users; --",
      '../../../etc/passwd'
    ];

    console.log(`   Input Sanitization Tests:`);
    maliciousInputs.forEach(input => {
      const sanitized = securityHardening.sanitizeInput(input);
      const isSafe = !sanitized.includes('<script>') && !sanitized.includes('DROP TABLE');
      console.log(`     ${input.substring(0, 30)}... → ${isSafe ? 'SAFE' : 'UNSAFE'}`);
    });

    const securityStatus = securityHardening.getSecurityStatus();
    console.log(`✅ Security Hardening: ${securityStatus.riskLevel === 'LOW' ? 'PASSED' : 'NEEDS ATTENTION'} (Risk: ${securityStatus.riskLevel})`);

    console.log('\n');

    // Test 5: Comprehensive Production Hardening
    console.log('📋 Test 5: Comprehensive Production Hardening');
    console.log('---------------------------------------------');

    const hardeningReport = await productionHardeningOrchestrator.executePhase3Hardening();

    console.log(`📊 Production Hardening Report:`);
    console.log(`   Phase: ${hardeningReport.phase}`);
    console.log(`   Overall Status: ${hardeningReport.overallStatus}`);
    console.log(`   Health Score: ${hardeningReport.healthScore}/100`);
    console.log(`   Timestamp: ${new Date(hardeningReport.timestamp).toISOString()}`);

    console.log(`   Quality Gates:`);
    console.log(`     Edge Cases: ${hardeningReport.qualityGates.edgeCasesPassed ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`     Performance: ${hardeningReport.qualityGates.performanceThresholds ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`     Security: ${hardeningReport.qualityGates.securityCompliant ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`     Accessibility: ${hardeningReport.qualityGates.accessibilityCompliant ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`     Cross-Platform: ${hardeningReport.qualityGates.crossPlatformCompatible ? '✅ PASSED' : '❌ FAILED'}`);

    if (hardeningReport.recommendations.length > 0) {
      console.log(`   Recommendations:`);
      hardeningReport.recommendations.forEach(rec => {
        console.log(`     - ${rec}`);
      });
    }

    if (hardeningReport.nextSteps.length > 0) {
      console.log(`   Next Steps:`);
      hardeningReport.nextSteps.forEach(step => {
        console.log(`     - ${step}`);
      });
    }

    console.log(`✅ Production Hardening: ${hardeningReport.overallStatus === 'HEALTHY' ? 'PASSED' : 'NEEDS ATTENTION'}`);

    console.log('\n');

    // Generate comprehensive status report
    console.log('📋 Comprehensive Status Report');
    console.log('==============================');
    const statusReport = productionHardeningOrchestrator.generateStatusReport();
    console.log(statusReport);

    // Test Summary
    console.log('\n📋 TEST SUMMARY');
    console.log('===============');

    const allTestsPassed = 
      passRate >= 80 &&
      performanceStatus.isHealthy &&
      securityStatus.riskLevel === 'LOW' &&
      hardeningReport.overallStatus === 'HEALTHY';

    console.log(`Overall Test Result: ${allTestsPassed ? '✅ ALL TESTS PASSED' : '⚠️ SOME TESTS NEED ATTENTION'}`);
    
    if (allTestsPassed) {
      console.log('🎉 Production hardening system is fully operational!');
      console.log('✅ Ready for Phase 4: Enterprise Deployment Readiness');
    } else {
      console.log('⚠️ Some components need attention before proceeding to Phase 4');
      console.log('📝 Review the recommendations and next steps above');
    }

  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  } finally {
    // Cleanup
    performanceMonitor.stopMonitoring();
    productionHardeningOrchestrator.cleanup();
    console.log('\n🧹 Cleanup completed');
  }
}

// Run the test suite
if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { main as testProductionHardening };
