#!/usr/bin/env node

/**
 * Bundle Size Analysis Script
 * Analyzes the Next.js bundle to identify optimization opportunities
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class BundleAnalyzer {
  constructor() {
    this.issues = [];
    this.recommendations = [];
    this.duplicates = new Map();
    this.largeChunks = [];
    this.unusedDependencies = [];
  }

  /**
   * Analyze package.json for potential optimizations
   */
  analyzePackageJson() {
    console.log('📦 Analyzing package.json dependencies...');
    
    const packagePath = path.join(process.cwd(), 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    const dependencies = packageJson.dependencies || {};
    const devDependencies = packageJson.devDependencies || {};
    
    // Check for potential duplicate utilities
    const potentialDuplicates = [
      { packages: ['bcrypt', 'bcryptjs'], reason: 'Both bcrypt libraries present' },
      { packages: ['dompurify', 'isomorphic-dompurify'], reason: 'Multiple DOMPurify implementations' },
      { packages: ['@types/node-fetch', 'node-fetch'], reason: 'Node-fetch with types (consider native fetch)' }
    ];
    
    potentialDuplicates.forEach(({ packages, reason }) => {
      const found = packages.filter(pkg => dependencies[pkg] || devDependencies[pkg]);
      if (found.length > 1) {
        this.issues.push({
          type: 'DUPLICATE_DEPENDENCY',
          packages: found,
          reason,
          severity: 'MEDIUM'
        });
      }
    });
    
    // Check for large dependencies that could be optimized
    const largeDependencies = [
      'swagger-ui-react',
      'recharts',
      'playwright',
      'mammoth',
      'pdf-parse',
      'prismjs'
    ];
    
    largeDependencies.forEach(dep => {
      if (dependencies[dep]) {
        this.issues.push({
          type: 'LARGE_DEPENDENCY',
          package: dep,
          reason: 'Large dependency that could be dynamically imported',
          severity: 'LOW'
        });
      }
    });
    
    // Check for outdated or unnecessary dependencies
    const potentiallyUnnecessary = [
      { pkg: 'helmet', reason: 'Next.js has built-in security headers' },
      { pkg: 'js-yaml', reason: 'Check if YAML parsing is actually needed' },
      { pkg: 'jsdom', reason: 'Should be dev dependency if used for testing' }
    ];
    
    potentiallyUnnecessary.forEach(({ pkg, reason }) => {
      if (dependencies[pkg]) {
        this.issues.push({
          type: 'POTENTIALLY_UNNECESSARY',
          package: pkg,
          reason,
          severity: 'LOW'
        });
      }
    });
  }

  /**
   * Analyze import patterns in the codebase
   */
  analyzeImportPatterns() {
    console.log('🔍 Analyzing import patterns...');
    
    const srcDir = path.join(process.cwd(), 'src');
    const files = this.getAllTsxFiles(srcDir);
    
    const importCounts = new Map();
    const heavyImports = [];
    
    files.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      const imports = this.extractImports(content);
      
      imports.forEach(imp => {
        importCounts.set(imp, (importCounts.get(imp) || 0) + 1);
        
        // Check for heavy imports that should be dynamic
        if (this.isHeavyImport(imp)) {
          heavyImports.push({ file, import: imp });
        }
      });
    });
    
    // Find frequently imported heavy libraries
    const heavyLibraries = ['recharts', 'swagger-ui-react', 'mammoth', 'pdf-parse'];
    heavyLibraries.forEach(lib => {
      const count = importCounts.get(lib) || 0;
      if (count > 0) {
        this.issues.push({
          type: 'HEAVY_IMPORT',
          library: lib,
          count,
          reason: `Heavy library imported ${count} times - consider dynamic imports`,
          severity: count > 3 ? 'HIGH' : 'MEDIUM'
        });
      }
    });
    
    // Check for barrel imports that could be optimized
    const barrelImports = Array.from(importCounts.entries())
      .filter(([imp]) => imp.includes('lucide-react') || imp.includes('@radix-ui'))
      .filter(([, count]) => count > 5);
    
    barrelImports.forEach(([imp, count]) => {
      this.issues.push({
        type: 'BARREL_IMPORT',
        import: imp,
        count,
        reason: `Barrel import used ${count} times - consider specific imports`,
        severity: 'LOW'
      });
    });
  }

  /**
   * Check for unused dependencies
   */
  checkUnusedDependencies() {
    console.log('🗑️ Checking for unused dependencies...');
    
    try {
      // Use depcheck to find unused dependencies
      const result = execSync('npx depcheck --json', { encoding: 'utf8' });
      const depcheckResult = JSON.parse(result);
      
      if (depcheckResult.dependencies && depcheckResult.dependencies.length > 0) {
        this.unusedDependencies = depcheckResult.dependencies;
        
        depcheckResult.dependencies.forEach(dep => {
          this.issues.push({
            type: 'UNUSED_DEPENDENCY',
            package: dep,
            reason: 'Dependency appears to be unused',
            severity: 'LOW'
          });
        });
      }
    } catch (error) {
      console.warn('Could not run depcheck:', error.message);
    }
  }

  /**
   * Generate optimization recommendations
   */
  generateRecommendations() {
    console.log('💡 Generating optimization recommendations...');
    
    // Group issues by type for better recommendations
    const issuesByType = new Map();
    this.issues.forEach(issue => {
      if (!issuesByType.has(issue.type)) {
        issuesByType.set(issue.type, []);
      }
      issuesByType.get(issue.type).push(issue);
    });
    
    // Generate specific recommendations
    if (issuesByType.has('DUPLICATE_DEPENDENCY')) {
      this.recommendations.push({
        title: 'Remove Duplicate Dependencies',
        priority: 'HIGH',
        actions: issuesByType.get('DUPLICATE_DEPENDENCY').map(issue => 
          `Remove one of: ${issue.packages.join(', ')} - ${issue.reason}`
        )
      });
    }
    
    if (issuesByType.has('HEAVY_IMPORT')) {
      this.recommendations.push({
        title: 'Implement Dynamic Imports',
        priority: 'MEDIUM',
        actions: [
          'Convert heavy library imports to dynamic imports',
          'Use React.lazy() for heavy components',
          'Implement code splitting for large features'
        ]
      });
    }
    
    if (issuesByType.has('UNUSED_DEPENDENCY')) {
      this.recommendations.push({
        title: 'Remove Unused Dependencies',
        priority: 'LOW',
        actions: this.unusedDependencies.map(dep => `Remove unused dependency: ${dep}`)
      });
    }
    
    // General optimization recommendations
    this.recommendations.push({
      title: 'Bundle Optimization Strategies',
      priority: 'MEDIUM',
      actions: [
        'Configure webpack bundle analyzer for detailed analysis',
        'Implement tree shaking for unused code elimination',
        'Use Next.js built-in optimizations (Image, Script components)',
        'Consider replacing large libraries with lighter alternatives',
        'Implement proper code splitting boundaries'
      ]
    });
  }

  /**
   * Helper methods
   */
  getAllTsxFiles(dir) {
    const files = [];
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        files.push(...this.getAllTsxFiles(fullPath));
      } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
        files.push(fullPath);
      }
    });
    
    return files;
  }

  extractImports(content) {
    const imports = [];
    const importRegex = /import\s+.*?\s+from\s+['"`]([^'"`]+)['"`]/g;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      imports.push(match[1]);
    }
    
    return imports;
  }

  isHeavyImport(importPath) {
    const heavyLibraries = [
      'recharts',
      'swagger-ui-react',
      'mammoth',
      'pdf-parse',
      'prismjs',
      '@react-email'
    ];
    
    return heavyLibraries.some(lib => importPath.includes(lib));
  }

  /**
   * Generate comprehensive report
   */
  generateReport() {
    console.log('\n📊 BUNDLE SIZE ANALYSIS REPORT');
    console.log('================================\n');
    
    // Summary
    const totalIssues = this.issues.length;
    const highSeverity = this.issues.filter(i => i.severity === 'HIGH').length;
    const mediumSeverity = this.issues.filter(i => i.severity === 'MEDIUM').length;
    const lowSeverity = this.issues.filter(i => i.severity === 'LOW').length;
    
    console.log(`📈 SUMMARY:`);
    console.log(`Total Issues: ${totalIssues}`);
    console.log(`High Priority: ${highSeverity}`);
    console.log(`Medium Priority: ${mediumSeverity}`);
    console.log(`Low Priority: ${lowSeverity}\n`);
    
    // Issues by category
    const issuesByType = new Map();
    this.issues.forEach(issue => {
      if (!issuesByType.has(issue.type)) {
        issuesByType.set(issue.type, []);
      }
      issuesByType.get(issue.type).push(issue);
    });
    
    console.log('🔍 ISSUES BY CATEGORY:');
    for (const [type, issues] of issuesByType) {
      console.log(`\n${type} (${issues.length} issues):`);
      issues.forEach(issue => {
        console.log(`  • ${issue.reason}`);
        if (issue.packages) console.log(`    Packages: ${issue.packages.join(', ')}`);
        if (issue.package) console.log(`    Package: ${issue.package}`);
        if (issue.library) console.log(`    Library: ${issue.library} (${issue.count} imports)`);
      });
    }
    
    // Recommendations
    console.log('\n💡 OPTIMIZATION RECOMMENDATIONS:');
    this.recommendations.forEach((rec, index) => {
      console.log(`\n${index + 1}. ${rec.title} (Priority: ${rec.priority})`);
      rec.actions.forEach(action => {
        console.log(`   • ${action}`);
      });
    });
    
    console.log('\n✅ Analysis complete! Use these recommendations to optimize your bundle size.');

    // Additional current bundle analysis
    this.analyzeCurrentBundle();
  }

  /**
   * Analyze current bundle state
   */
  analyzeCurrentBundle() {
    console.log('\n📦 CURRENT BUNDLE ANALYSIS:');
    console.log('============================\n');

    try {
      // Analyze build manifests
      const projectRoot = process.cwd();
      const buildManifestPath = path.join(projectRoot, '.next/build-manifest.json');
      const appBuildManifestPath = path.join(projectRoot, '.next/app-build-manifest.json');

      if (fs.existsSync(buildManifestPath)) {
        const buildManifest = JSON.parse(fs.readFileSync(buildManifestPath, 'utf8'));
        console.log('📊 Build Manifest Analysis:');
        console.log(`- Total pages: ${Object.keys(buildManifest.pages || {}).length}`);
        console.log(`- Root main files: ${buildManifest.rootMainFiles?.length || 0}`);

        if (buildManifest.rootMainFiles) {
          console.log('- Root main files:', buildManifest.rootMainFiles.join(', '));
        }
      }

      if (fs.existsSync(appBuildManifestPath)) {
        const appBuildManifest = JSON.parse(fs.readFileSync(appBuildManifestPath, 'utf8'));
        if (appBuildManifest.pages) {
          const totalChunks = Object.values(appBuildManifest.pages).flat().length;
          console.log(`- App chunks: ${totalChunks}`);
        }
      }

      // Analyze chunk sizes
      const chunksDir = path.join(projectRoot, '.next/static/chunks');
      if (fs.existsSync(chunksDir)) {
        const chunks = fs.readdirSync(chunksDir)
          .filter(file => file.endsWith('.js'))
          .map(file => {
            const filePath = path.join(chunksDir, file);
            const stats = fs.statSync(filePath);
            return {
              name: file,
              size: stats.size,
              sizeKB: Math.round(stats.size / 1024)
            };
          })
          .sort((a, b) => b.size - a.size);

        console.log('\n🔍 Chunk Size Analysis:');
        console.log('Top 10 largest chunks:');
        chunks.slice(0, 10).forEach((chunk, index) => {
          const sizeIndicator = chunk.sizeKB > 500 ? '🔴' : chunk.sizeKB > 200 ? '🟡' : '🟢';
          console.log(`${index + 1}. ${sizeIndicator} ${chunk.name}: ${chunk.sizeKB} KB`);
        });

        const totalChunkSize = chunks.reduce((sum, chunk) => sum + chunk.size, 0);
        console.log(`\nTotal chunk size: ${Math.round(totalChunkSize / 1024)} KB`);

        // Identify optimization opportunities
        const largeChunks = chunks.filter(chunk => chunk.sizeKB > 200);
        if (largeChunks.length > 0) {
          console.log(`\n⚠️  Found ${largeChunks.length} chunks larger than 200KB:`);
          largeChunks.forEach(chunk => {
            console.log(`   • ${chunk.name}: ${chunk.sizeKB} KB`);
          });
          console.log('   Consider implementing dynamic imports for these large chunks.');
        }
      }

    } catch (error) {
      console.log('Could not analyze current bundle:', error.message);
    }
  }

  /**
   * Run the complete analysis
   */
  async analyze() {
    console.log('🚀 Starting bundle size analysis...\n');
    
    this.analyzePackageJson();
    this.analyzeImportPatterns();
    this.checkUnusedDependencies();
    this.generateRecommendations();
    this.generateReport();
    
    return {
      issues: this.issues,
      recommendations: this.recommendations,
      summary: {
        totalIssues: this.issues.length,
        highPriority: this.issues.filter(i => i.severity === 'HIGH').length,
        mediumPriority: this.issues.filter(i => i.severity === 'MEDIUM').length,
        lowPriority: this.issues.filter(i => i.severity === 'LOW').length
      }
    };
  }
}

// Run the analyzer
if (require.main === module) {
  const analyzer = new BundleAnalyzer();
  analyzer.analyze().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('Analysis failed:', error);
    process.exit(1);
  });
}

module.exports = BundleAnalyzer;
