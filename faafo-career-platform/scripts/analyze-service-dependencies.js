#!/usr/bin/env node

/**
 * Service Dependency Analysis Script
 * Analyzes service layer architecture and identifies potential issues
 */

const fs = require('fs');
const path = require('path');

class ServiceDependencyAnalyzer {
  constructor() {
    this.services = new Map();
    this.dependencies = new Map();
    this.issues = [];
  }

  /**
   * Analyze all services in the services directory
   */
  analyzeServices() {
    const servicesDir = path.join(process.cwd(), 'src/lib/services');
    const libDir = path.join(process.cwd(), 'src/lib');
    
    console.log('Analyzing service dependencies...\n');
    
    // Get all service files
    const serviceFiles = this.getServiceFiles(servicesDir);
    const libFiles = this.getServiceFiles(libDir);
    
    // Analyze each service
    for (const file of [...serviceFiles, ...libFiles]) {
      this.analyzeService(file);
    }
    
    this.detectArchitecturalIssues();
    this.generateReport();
  }

  /**
   * Get all TypeScript service files
   */
  getServiceFiles(dir) {
    const files = [];
    
    if (!fs.existsSync(dir)) {
      return files;
    }
    
    const entries = fs.readdirSync(dir);
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry);
      const stat = fs.statSync(fullPath);
      
      if (stat.isFile() && entry.endsWith('.ts') && !entry.endsWith('.d.ts')) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  /**
   * Analyze a single service file
   */
  analyzeService(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const relativePath = path.relative(process.cwd(), filePath);
      const serviceName = path.basename(filePath, '.ts');
      
      // Extract imports
      const imports = this.extractImports(content, filePath);
      
      // Analyze service characteristics
      const analysis = {
        name: serviceName,
        path: relativePath,
        imports: imports,
        exportedClasses: this.extractExportedClasses(content),
        exportedFunctions: this.extractExportedFunctions(content),
        hasDefaultExport: content.includes('export default'),
        linesOfCode: content.split('\n').length,
        complexity: this.calculateComplexity(content),
        dependencies: imports.filter(imp => imp.isLocal).length,
        externalDependencies: imports.filter(imp => !imp.isLocal).length
      };
      
      this.services.set(serviceName, analysis);
      this.dependencies.set(serviceName, imports.filter(imp => imp.isLocal));
      
    } catch (error) {
      console.warn(`Warning: Could not analyze ${filePath}: ${error.message}`);
    }
  }

  /**
   * Extract imports from service file
   */
  extractImports(content, filePath) {
    const imports = [];
    const importPatterns = [
      /import\s+.*?\s+from\s+['"`]([^'"`]+)['"`]/g,
      /import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
      /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g
    ];

    for (const pattern of importPatterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const importPath = match[1];
        
        const isLocal = importPath.startsWith('./') || 
                       importPath.startsWith('../') || 
                       importPath.startsWith('@/');
        
        imports.push({
          path: importPath,
          isLocal,
          isService: this.isServiceImport(importPath),
          type: this.categorizeImport(importPath)
        });
      }
    }
    
    return imports;
  }

  /**
   * Check if import is a service
   */
  isServiceImport(importPath) {
    return importPath.includes('/services/') || 
           importPath.includes('Service') ||
           importPath.includes('service');
  }

  /**
   * Categorize import type
   */
  categorizeImport(importPath) {
    if (importPath.startsWith('@prisma/')) return 'database';
    if (importPath.includes('prisma')) return 'database';
    if (importPath.includes('cache')) return 'cache';
    if (importPath.includes('ai') || importPath.includes('gemini')) return 'ai';
    if (importPath.includes('service')) return 'service';
    if (importPath.includes('util')) return 'utility';
    if (importPath.startsWith('@/lib/')) return 'internal';
    if (!importPath.startsWith('.') && !importPath.startsWith('@/')) return 'external';
    return 'local';
  }

  /**
   * Extract exported classes
   */
  extractExportedClasses(content) {
    const classPattern = /export\s+(?:default\s+)?class\s+(\w+)/g;
    const classes = [];
    let match;
    
    while ((match = classPattern.exec(content)) !== null) {
      classes.push(match[1]);
    }
    
    return classes;
  }

  /**
   * Extract exported functions
   */
  extractExportedFunctions(content) {
    const functionPatterns = [
      /export\s+(?:async\s+)?function\s+(\w+)/g,
      /export\s+const\s+(\w+)\s*=\s*(?:async\s+)?\(/g
    ];
    
    const functions = [];
    
    for (const pattern of functionPatterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        functions.push(match[1]);
      }
    }
    
    return functions;
  }

  /**
   * Calculate complexity score
   */
  calculateComplexity(content) {
    const complexityIndicators = [
      /class\s+\w+/g,
      /function\s+\w+/g,
      /if\s*\(/g,
      /for\s*\(/g,
      /while\s*\(/g,
      /switch\s*\(/g,
      /catch\s*\(/g,
      /async\s+/g,
      /await\s+/g,
      /Promise\./g
    ];
    
    let complexity = 0;
    for (const pattern of complexityIndicators) {
      const matches = content.match(pattern);
      complexity += matches ? matches.length : 0;
    }
    
    return complexity;
  }

  /**
   * Detect architectural issues
   */
  detectArchitecturalIssues() {
    console.log('Detecting architectural issues...\n');
    
    for (const [serviceName, service] of this.services) {
      // Check for high coupling
      if (service.dependencies > 5) {
        this.issues.push({
          type: 'HIGH_COUPLING',
          service: serviceName,
          severity: 'MEDIUM',
          description: `Service has ${service.dependencies} local dependencies (high coupling)`
        });
      }
      
      // Check for large services
      if (service.linesOfCode > 500) {
        this.issues.push({
          type: 'LARGE_SERVICE',
          service: serviceName,
          severity: 'LOW',
          description: `Service has ${service.linesOfCode} lines of code (consider splitting)`
        });
      }
      
      // Check for high complexity
      if (service.complexity > 50) {
        this.issues.push({
          type: 'HIGH_COMPLEXITY',
          service: serviceName,
          severity: 'MEDIUM',
          description: `Service has complexity score of ${service.complexity} (consider refactoring)`
        });
      }
      
      // Check for mixed responsibilities
      const importTypes = new Set(service.imports.map(imp => imp.type));
      if (importTypes.size > 4) {
        this.issues.push({
          type: 'MIXED_RESPONSIBILITIES',
          service: serviceName,
          severity: 'MEDIUM',
          description: `Service imports from ${importTypes.size} different categories (mixed responsibilities)`
        });
      }
    }
    
    // Check for potential circular dependency risks
    this.checkCircularDependencyRisks();
  }

  /**
   * Check for patterns that could lead to circular dependencies
   */
  checkCircularDependencyRisks() {
    const serviceImports = new Map();
    
    // Build service import graph
    for (const [serviceName, deps] of this.dependencies) {
      const serviceDeps = deps
        .filter(dep => dep.isService)
        .map(dep => this.extractServiceName(dep.path));
      
      serviceImports.set(serviceName, serviceDeps);
    }
    
    // Check for bidirectional dependencies
    for (const [serviceA, depsA] of serviceImports) {
      for (const serviceB of depsA) {
        const depsB = serviceImports.get(serviceB) || [];
        if (depsB.includes(serviceA)) {
          this.issues.push({
            type: 'BIDIRECTIONAL_DEPENDENCY',
            service: serviceA,
            severity: 'HIGH',
            description: `Bidirectional dependency between ${serviceA} and ${serviceB}`
          });
        }
      }
    }
  }

  /**
   * Extract service name from import path
   */
  extractServiceName(importPath) {
    const parts = importPath.split('/');
    const filename = parts[parts.length - 1];
    return filename.replace(/\.ts$/, '').replace(/Service$/, '');
  }

  /**
   * Generate analysis report
   */
  generateReport() {
    console.log('='.repeat(80));
    console.log('SERVICE DEPENDENCY ANALYSIS REPORT');
    console.log('='.repeat(80));
    
    // Service overview
    console.log(`\nSERVICE OVERVIEW:`);
    console.log(`Total services analyzed: ${this.services.size}`);
    
    const avgDependencies = Array.from(this.services.values())
      .reduce((sum, s) => sum + s.dependencies, 0) / this.services.size;
    console.log(`Average dependencies per service: ${avgDependencies.toFixed(1)}`);
    
    const avgComplexity = Array.from(this.services.values())
      .reduce((sum, s) => sum + s.complexity, 0) / this.services.size;
    console.log(`Average complexity score: ${avgComplexity.toFixed(1)}`);
    
    // Issues summary
    console.log(`\nISSUES FOUND: ${this.issues.length}`);
    
    if (this.issues.length === 0) {
      console.log('✅ No architectural issues detected!');
      return;
    }
    
    const issuesBySeverity = this.groupBy(this.issues, 'severity');
    
    for (const [severity, issues] of Object.entries(issuesBySeverity)) {
      console.log(`\n${severity} SEVERITY (${issues.length} issues):`);
      
      for (const issue of issues) {
        console.log(`  ❌ ${issue.service}: ${issue.description}`);
      }
    }
    
    // Recommendations
    console.log('\nRECOMMENDATIONS:');
    console.log('1. Extract shared interfaces to separate files');
    console.log('2. Use dependency injection for loose coupling');
    console.log('3. Create service facades for complex operations');
    console.log('4. Consider using event-driven patterns');
    console.log('5. Split large services into smaller, focused services');
  }

  /**
   * Group array by property
   */
  groupBy(array, property) {
    return array.reduce((groups, item) => {
      const key = item[property];
      groups[key] = groups[key] || [];
      groups[key].push(item);
      return groups;
    }, {});
  }
}

// Run the analyzer
if (require.main === module) {
  const analyzer = new ServiceDependencyAnalyzer();
  analyzer.analyzeServices();
}

module.exports = ServiceDependencyAnalyzer;
