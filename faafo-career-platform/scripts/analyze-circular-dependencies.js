#!/usr/bin/env node

/**
 * Circular Dependency Analysis Script
 * Analyzes TypeScript/JavaScript files for circular import dependencies
 */

const fs = require('fs');
const path = require('path');

class CircularDependencyAnalyzer {
  constructor() {
    this.dependencyGraph = new Map();
    this.fileImports = new Map();
    this.circularDependencies = [];
    this.analyzed = new Set();
  }

  /**
   * Extract imports from a TypeScript/JavaScript file
   */
  extractImports(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const imports = [];
      
      // Match various import patterns
      const importPatterns = [
        /import\s+.*?\s+from\s+['"`]([^'"`]+)['"`]/g,
        /import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
        /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g
      ];

      for (const pattern of importPatterns) {
        let match;
        while ((match = pattern.exec(content)) !== null) {
          const importPath = match[1];
          
          // Only analyze relative imports (potential circular dependencies)
          if (importPath.startsWith('./') || importPath.startsWith('../')) {
            const resolvedPath = this.resolveImportPath(filePath, importPath);
            if (resolvedPath) {
              imports.push(resolvedPath);
            }
          }
        }
      }
      
      return imports;
    } catch (error) {
      console.warn(`Warning: Could not read file ${filePath}: ${error.message}`);
      return [];
    }
  }

  /**
   * Resolve relative import path to absolute path
   */
  resolveImportPath(fromFile, importPath) {
    const fromDir = path.dirname(fromFile);
    let resolvedPath = path.resolve(fromDir, importPath);
    
    // Try different extensions
    const extensions = ['.ts', '.tsx', '.js', '.jsx'];
    
    for (const ext of extensions) {
      const pathWithExt = resolvedPath + ext;
      if (fs.existsSync(pathWithExt)) {
        return pathWithExt;
      }
    }
    
    // Try index files
    for (const ext of extensions) {
      const indexPath = path.join(resolvedPath, `index${ext}`);
      if (fs.existsSync(indexPath)) {
        return indexPath;
      }
    }
    
    return null;
  }

  /**
   * Build dependency graph for all files
   */
  buildDependencyGraph(rootDir) {
    const files = this.getAllTSFiles(rootDir);
    
    console.log(`Analyzing ${files.length} TypeScript/JavaScript files...`);
    
    for (const file of files) {
      const imports = this.extractImports(file);
      this.fileImports.set(file, imports);
      
      if (!this.dependencyGraph.has(file)) {
        this.dependencyGraph.set(file, new Set());
      }
      
      for (const importPath of imports) {
        this.dependencyGraph.get(file).add(importPath);
      }
    }
  }

  /**
   * Get all TypeScript/JavaScript files recursively
   */
  getAllTSFiles(dir) {
    const files = [];
    
    function traverse(currentDir) {
      const entries = fs.readdirSync(currentDir);
      
      for (const entry of entries) {
        const fullPath = path.join(currentDir, entry);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // Skip node_modules and .next directories
          if (!['node_modules', '.next', '.git', 'dist', 'build'].includes(entry)) {
            traverse(fullPath);
          }
        } else if (stat.isFile()) {
          if (/\.(ts|tsx|js|jsx)$/.test(entry) && !entry.endsWith('.d.ts')) {
            files.push(fullPath);
          }
        }
      }
    }
    
    traverse(dir);
    return files;
  }

  /**
   * Detect circular dependencies using DFS
   */
  detectCircularDependencies() {
    console.log('Detecting circular dependencies...');
    
    for (const [file, dependencies] of this.dependencyGraph) {
      this.findCircularPath(file, file, new Set(), []);
    }
    
    // Remove duplicates
    this.circularDependencies = this.removeDuplicateCycles();
  }

  /**
   * Find circular dependency path using DFS
   */
  findCircularPath(startFile, currentFile, visited, path) {
    if (visited.has(currentFile)) {
      if (currentFile === startFile && path.length > 1) {
        // Found a cycle
        this.circularDependencies.push([...path, currentFile]);
      }
      return;
    }
    
    visited.add(currentFile);
    path.push(currentFile);
    
    const dependencies = this.dependencyGraph.get(currentFile) || new Set();
    for (const dependency of dependencies) {
      this.findCircularPath(startFile, dependency, new Set(visited), [...path]);
    }
  }

  /**
   * Remove duplicate circular dependency cycles
   */
  removeDuplicateCycles() {
    const uniqueCycles = [];
    const seenCycles = new Set();
    
    for (const cycle of this.circularDependencies) {
      // Normalize cycle (start from lexicographically smallest file)
      const minIndex = cycle.indexOf(Math.min(...cycle));
      const normalizedCycle = [...cycle.slice(minIndex), ...cycle.slice(0, minIndex)];
      const cycleKey = normalizedCycle.join(' -> ');
      
      if (!seenCycles.has(cycleKey)) {
        seenCycles.add(cycleKey);
        uniqueCycles.push(cycle);
      }
    }
    
    return uniqueCycles;
  }

  /**
   * Generate analysis report
   */
  generateReport() {
    console.log('\n' + '='.repeat(80));
    console.log('CIRCULAR DEPENDENCY ANALYSIS REPORT');
    console.log('='.repeat(80));
    
    if (this.circularDependencies.length === 0) {
      console.log('✅ No circular dependencies detected!');
      return;
    }
    
    console.log(`❌ Found ${this.circularDependencies.length} circular dependencies:\n`);
    
    for (let i = 0; i < this.circularDependencies.length; i++) {
      const cycle = this.circularDependencies[i];
      console.log(`${i + 1}. Circular Dependency:`);
      
      for (let j = 0; j < cycle.length; j++) {
        const file = cycle[j];
        const relativePath = path.relative(process.cwd(), file);
        console.log(`   ${j === 0 ? '┌─' : j === cycle.length - 1 ? '└─' : '├─'} ${relativePath}`);
        
        if (j < cycle.length - 1) {
          console.log(`   ${j === cycle.length - 2 ? ' ' : '│'}`);
        }
      }
      console.log('');
    }
    
    // Provide recommendations
    console.log('RECOMMENDATIONS:');
    console.log('1. Extract shared interfaces/types to separate files');
    console.log('2. Use dependency injection instead of direct imports');
    console.log('3. Create barrel exports (index.ts files) to centralize exports');
    console.log('4. Consider using event-driven architecture for loose coupling');
    console.log('5. Move shared utilities to a common directory');
  }

  /**
   * Run the analysis
   */
  analyze(rootDir = './src') {
    console.log(`Starting circular dependency analysis in: ${rootDir}`);
    
    this.buildDependencyGraph(rootDir);
    this.detectCircularDependencies();
    this.generateReport();
    
    return this.circularDependencies.length === 0;
  }
}

// Run the analyzer
if (require.main === module) {
  const analyzer = new CircularDependencyAnalyzer();
  const success = analyzer.analyze('./src');
  process.exit(success ? 0 : 1);
}

module.exports = CircularDependencyAnalyzer;
